"use client";

import React, { useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { ProductsDataTable } from "./products-data-table";
import { getProductColumns, Product } from "./columns";
import { ProductSetupWizard } from "./ProductSetupWizard";
import { ProductProfitabilityDashboard } from "./ProductProfitabilityDashboard";
import { deleteProducts } from "@/lib/actions/products/delete-products";
import { updateProduct } from "@/lib/actions/products/update-product";
import { createProduct } from "@/lib/actions/products/create-product";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { RowSelectionState } from "@tanstack/react-table";
import { NewProduct } from "@/lib/actions/products/create-product";
import DeleteConfirmationDialog from "@/components/general/DeleteConfirmationDialog";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Package, BarChart3, Plus, ListFilter, Download, Upload } from "lucide-react";
import { getProductsWithAnalytics } from "@/lib/actions/products/get-products-with-analytics";
import { getProductCategories } from "@/lib/actions/products/get-product-categories";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { AdjustPriceDialog } from "./AdjustPriceDialog";
import { BulkAssignCategoryDialog } from "./BulkAssignCategoryDialog";
import { BulkAdjustPriceDialog, AdjustPriceFormData } from "./BulkAdjustPriceDialog";
import { BulkRemapAccountsDialog, RemapAccountsFormData } from "./BulkRemapAccountsDialog";
import { bulkUpdateProductCategory } from "@/lib/actions/products/bulk-update-product-category";
import { bulkUpdateProductPrice } from "@/lib/actions/products/bulk-update-product-price";
import { bulkUpdateProductAccounts } from "@/lib/actions/products/bulk-update-product-accounts";
import { getProductsForExport } from "@/lib/actions/products/get-products-for-export";
import { downloadCsv } from "@/lib/utils/csv-parser";
import { unparse } from "papaparse";
import { ProductImportDialog } from "./ProductImportDialog";

export type ProductWithAnalytics = Awaited<ReturnType<typeof getProductsWithAnalytics>>[0];

export default function ProductsPageClient({ organizationId }: { organizationId: string }) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editingProduct, setEditingProduct] = React.useState<Product | null>(null);
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [productsToDelete, setProductsToDelete] = React.useState<string[]>([]);
  const [currentTab, setCurrentTab] = React.useState("products");
  const [categoryFilter, setCategoryFilter] = React.useState<string | null>(null);
  const [isAdjustPriceDialogOpen, setIsAdjustPriceDialogOpen] = React.useState(false);
  const [productToAdjust, setProductToAdjust] = React.useState<Product | null>(null);
  const [isAssignCategoryDialogOpen, setIsAssignCategoryDialogOpen] = React.useState(false);
  const [isBulkAdjustPriceDialogOpen, setIsBulkAdjustPriceDialogOpen] = React.useState(false);
  const [isBulkRemapAccountsDialogOpen, setIsBulkRemapAccountsDialogOpen] = React.useState(false);
  const [isExporting, setIsExporting] = React.useState(false);
  const [isImporting, setIsImporting] = React.useState(false);

  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products-with-analytics", organizationId],
    queryFn: () => getProductsWithAnalytics(),
    enabled: !!organizationId,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ["product-categories", organizationId],
    queryFn: () => getProductCategories(),
    enabled: !!organizationId,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const filteredProducts = useMemo(() => {
    if (!categoryFilter) {
      return products;
    }
    return products.filter((p) => p.categoryId === categoryFilter);
  }, [products, categoryFilter]);

  const { data: accounts = [], isLoading: isLoadingAccounts } = useQuery({
    queryKey: ["accountsForProducts", organizationId],
    queryFn: () => getAccounts(),
    enabled: !!organizationId,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const {
    data: profitabilityData,
    isLoading: isLoadingProfitability,
  } = useQuery({
    queryKey: ["products-profitability", organizationId],
    queryFn: () => getProductsWithAnalytics(),
    enabled: !!organizationId,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { mutate: mutateCreateUpdate, isPending: isCreatingOrUpdating } = useMutation({
    mutationFn: (data: { id?: string; productData: NewProduct }) =>
      data.id ? updateProduct(data.id, data.productData) : createProduct(data.productData),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["products-with-analytics", organizationId] });
        setDialogOpen(false);
        setEditingProduct(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });

  const { mutate: mutateAdjustPrice, isPending: isAdjustingPrice } = useMutation({
    mutationFn: (data: { productId: string; price: number }) =>
      updateProduct(data.productId, { price: data.price }),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["products-with-analytics", organizationId] });
        setIsAdjustPriceDialogOpen(false);
        setProductToAdjust(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });

  const { mutate: mutateBulkAdjustPrice, isPending: isBulkAdjustingPrice } = useMutation({
    mutationFn: (data: { productIds: string[]; adjustment: AdjustPriceFormData }) =>
      bulkUpdateProductPrice(data.productIds, {type: data.adjustment.adjustmentType, value: data.adjustment.value}),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["products-with-analytics", organizationId] });
        setIsBulkAdjustPriceDialogOpen(false);
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });

  const { mutate: mutateBulkRemapAccounts, isPending: isBulkRemappingAccounts } = useMutation({
    mutationFn: (data: { productIds: string[]; accounts: RemapAccountsFormData }) =>
        bulkUpdateProductAccounts(data.productIds, data.accounts),
    onSuccess: (result) => {
        if (result.success) {
            toast.success(result.message);
            queryClient.invalidateQueries({ queryKey: ["products-with-analytics", organizationId] });
            setIsBulkRemapAccountsDialogOpen(false);
            setRowSelection({});
        } else {
            toast.error(result.message);
        }
    },
    onError: (error) => toast.error(error.message),
    });

  const { mutate: mutateBulkAssignCategory, isPending: isBulkAssigningCategory } = useMutation({
    mutationFn: (data: { productIds: string[]; categoryId: string }) =>
      bulkUpdateProductCategory(data.productIds, data.categoryId),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["products-with-analytics", organizationId] });
        setIsAssignCategoryDialogOpen(false);
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });

  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (ids: string[]) => deleteProducts(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["products-with-analytics", organizationId] });
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
      setDeleteDialogOpen(false);
      setProductsToDelete([]);
    },
    onError: (error) => {
      toast.error(error.message);
      setDeleteDialogOpen(false);
      setProductsToDelete([]);
    },
  });

  const selectedProductIds = Object.keys(rowSelection).filter(
    (key) => rowSelection[key]
  );

  const openNewDialog = () => {
    setEditingProduct(null);
    setDialogOpen(true);
  };

  const openEditDialog = (product: Product) => {
    setEditingProduct(product);
    setDialogOpen(true);
  };

  const openDeleteDialog = (ids: string[]) => {
    setProductsToDelete(ids);
    setDeleteDialogOpen(true);
  };

  const openAdjustPriceDialog = (product: Product) => {
    setProductToAdjust(product);
    setIsAdjustPriceDialogOpen(true);
  };

  const handleSubmit = (productData: NewProduct) => {
    mutateCreateUpdate({
      id: editingProduct?.id || undefined,
      productData,
    });
  };

  const handleAdjustPriceSubmit = (data: { price: number }) => {
    if (!productToAdjust) return;
    mutateAdjustPrice({ productId: productToAdjust.id, price: data.price });
  };

  const handleBulkAssignCategorySubmit = (data: { categoryId: string }) => {
    mutateBulkAssignCategory({
      productIds: selectedProductIds,
      categoryId: data.categoryId,
    });
  };

  const handleBulkAdjustPriceSubmit = (data: AdjustPriceFormData) => {
    mutateBulkAdjustPrice({
      productIds: selectedProductIds,
      adjustment: data,
    });
  };

  const handleBulkRemapAccountsSubmit = (data: RemapAccountsFormData) => {
    mutateBulkRemapAccounts({
        productIds: selectedProductIds,
        accounts: data,
    });
    };

  const handleConfirmDelete = () => {
    mutateDelete(productsToDelete);
  };

  const handleRowClick = (product: Product) => {
    router.push(`/products/${product.id}`);
  };

  const columns = useMemo(
    () =>
      getProductColumns({
        onEdit: openEditDialog,
        onDelete: (id: string) => openDeleteDialog([id]),
        onAdjustPrice: openAdjustPriceDialog,
      }),
    [accounts]
  );
  
  const wizardEditingProduct = useMemo(() => {
    if (!editingProduct) return null;

    const { type, ...rest } = editingProduct;

    return {
      ...rest,
      productType: type,
      price: editingProduct.price ? Number(editingProduct.price) : undefined,
      costBasis: editingProduct.costBasis
        ? Number(editingProduct.costBasis)
        : undefined,
      description: editingProduct.description ?? undefined,
      inventoryAccountId: editingProduct.inventoryAccountId ?? undefined,
      revenueAccountId: editingProduct.revenueAccountId ?? undefined,
      accountsToCreate: [],
    };
  }, [editingProduct]);

  const handleExport = async () => {
    setIsExporting(true);
    toast.info("Exporting products...");
    const result = await getProductsForExport();
    if (result.success && result.data) {
      const flattenedData = result.data.map((p) => ({
        id: p.id,
        name: p.name,
        description: p.description,
        type: p.type,
        price: p.price,
        costBasis: p.costBasis,
        sku: p.sku,
        isActive: p.isActive,
        totalSold: p.totalSold,
        totalRevenue: p.totalRevenue,
        totalCosts: p.totalCosts,
        category: p.category?.name,
        revenueAccount: p.revenueAccount?.name,
        inventoryAccount: p.inventoryAccount?.name,
        createdAt: p.createdAt,
      }));

      const csv = unparse(flattenedData);
      downloadCsv(csv, `products-export-${new Date().toISOString()}.csv`);
      toast.success("Products exported successfully.");
    } else {
      toast.error(result.message || "Failed to export products.");
    }
    setIsExporting(false);
  };

  if (isLoadingProducts || isLoadingAccounts) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-16 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Products & Services</h1>
          <p className="text-muted-foreground">
            Manage your products with intelligent suggestions and profitability analytics
          </p>
        </div>
        <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <ListFilter className="h-4 w-4" />
                  <span>Filter by Category</span>
                  {categoryFilter && <Badge variant="secondary">{categories.find(c => c.id === categoryFilter)?.name}</Badge>}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Select a category</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onSelect={() => setCategoryFilter(null)}>
                  All Categories
                </DropdownMenuItem>
                {categories.map((category) => (
                  <DropdownMenuItem
                    key={category.id}
                    onSelect={() => setCategoryFilter(category.id)}
                  >
                    {category.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button onClick={handleExport} variant="outline" disabled={isExporting} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                {isExporting ? 'Exporting...' : 'Export'}
            </Button>

            <Button onClick={() => setIsImporting(true)} variant="outline" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Import
            </Button>

            <Button onClick={openNewDialog} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Product
            </Button>
        </div>
      </div>

      <Tabs value={currentTab} onValueChange={setCurrentTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Products
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Profitability Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="space-y-4">
          {selectedProductIds.length > 0 && (
            <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
              <span className="text-sm font-medium">
                {selectedProductIds.length} product{selectedProductIds.length > 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">Bulk Actions</Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onSelect={() => setIsAssignCategoryDialogOpen(true)}>
                      Assign Category
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => setIsBulkAdjustPriceDialogOpen(true)}>
                      Adjust Prices
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => setIsBulkRemapAccountsDialogOpen(true)}>
                        Remap Accounts
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => openDeleteDialog(selectedProductIds)}
                  disabled={isDeleting}
                >
                  Delete Selected
                </Button>
              </div>
            </div>
          )}

          <ProductsDataTable
            columns={columns}
            data={filteredProducts}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onRowClick={handleRowClick}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {products.length > 0 ? (
            <ProductProfitabilityDashboard
              organizationId={organizationId}
              productId={products[0].id}
            />
          ) : (
            <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-lg">
              <BarChart3 className="h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No Profitability Data</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Create products and record transactions to see profitability analytics.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {dialogOpen && (
        <ProductSetupWizard
          open={dialogOpen}
          onOpenChange={(open: boolean) => {
            if (!open) setEditingProduct(null);
            setDialogOpen(open);
          }}
          onSubmit={handleSubmit}
          isPending={isCreatingOrUpdating}
          organizationId={organizationId}
          editingProduct={wizardEditingProduct as any}
        />
      )}

      <AdjustPriceDialog
        isOpen={isAdjustPriceDialogOpen}
        onClose={() => setIsAdjustPriceDialogOpen(false)}
        product={productToAdjust}
        onSubmit={handleAdjustPriceSubmit}
        isPending={isAdjustingPrice}
      />

      <BulkAssignCategoryDialog
        isOpen={isAssignCategoryDialogOpen}
        onClose={() => setIsAssignCategoryDialogOpen(false)}
        onSubmit={handleBulkAssignCategorySubmit}
        isPending={isBulkAssigningCategory}
        categories={categories}
        productCount={selectedProductIds.length}
      />

      <BulkAdjustPriceDialog
        isOpen={isBulkAdjustPriceDialogOpen}
        onClose={() => setIsBulkAdjustPriceDialogOpen(false)}
        onSubmit={handleBulkAdjustPriceSubmit}
        isPending={isBulkAdjustingPrice}
        productCount={selectedProductIds.length}
      />

      <BulkRemapAccountsDialog
        isOpen={isBulkRemapAccountsDialogOpen}
        onClose={() => setIsBulkRemapAccountsDialogOpen(false)}
        onSubmit={handleBulkRemapAccountsSubmit}
        isPending={isBulkRemappingAccounts}
        accounts={accounts}
        productCount={selectedProductIds.length}
      />

      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        isPending={isDeleting}
        count={productsToDelete.length}
      />

      <ProductImportDialog isOpen={isImporting} onClose={() => setIsImporting(false)} />
    </div>
  );
} 