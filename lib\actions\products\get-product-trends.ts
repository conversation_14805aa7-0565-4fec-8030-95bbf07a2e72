"use server";

import { db } from "@/db/drizzle";
import { products, journalEntries, transactions } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray, sql } from "drizzle-orm";

export async function getProductTrends(productId: string) {
    try {
        const { organization } = await getServerUserContext();
        const orgId = organization.id;

        const [product] = await db
            .select({
                revenueAccountId: products.revenueAccountId,
            })
            .from(products)
            .where(and(eq(products.id, productId), eq(products.organizationId, orgId)));

        if (!product || !product.revenueAccountId) {
            return { success: false, message: "Product or revenue account not found", data: [] };
        }

        const accountIds = [product.revenueAccountId];

        const trends = await db
            .select({
                year: sql<number>`EXTRACT(YEAR FROM ${transactions.date})`.as("year"),
                month: sql<number>`EXTRACT(MONTH FROM ${transactions.date})`.as("month"),
                revenue: sql<number>`SUM(CASE WHEN ${journalEntries.accountId} = ${product.revenueAccountId} THEN ${journalEntries.creditAmount} ELSE 0 END)`.as("revenue"),
                cost: sql<number>`SUM(0)`.as("cost"),
            })
            .from(journalEntries)
            .leftJoin(transactions, eq(journalEntries.transactionId, transactions.id))
            .where(and(
                eq(transactions.organizationId, orgId),
                inArray(journalEntries.accountId, accountIds)
            ))
            .groupBy(sql`1, 2`) 
            .orderBy(sql`1, 2`); 

        const formattedTrends = trends.map(t => ({
            date: `${t.year}-${String(t.month).padStart(2, '0')}`,
            revenue: Number(t.revenue),
            cost: Number(t.cost),
        }));
        
        return { success: true, data: formattedTrends };

    } catch (error) {
        console.error("Failed to fetch product trends:", error);
        return { success: false, message: "Failed to fetch product trends", data: [] };
    }
} 