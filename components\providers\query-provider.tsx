"use client";

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import React, { useState } from 'react';

export default function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // Reduce excessive refetching
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        // Keep data fresh for 5 minutes
        staleTime: 5 * 60 * 1000, // 5 minutes
        // Cache data for 10 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        // Retry failed requests less aggressively
        retry: 1,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
} 