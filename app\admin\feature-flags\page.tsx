"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { RefreshCw, Flag, Settings } from "lucide-react";

interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  userPercentage?: number;
  userIds?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export default function FeatureFlagsAdmin() {
  const [flags, setFlags] = useState<Record<string, FeatureFlag>>({});
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);

  // Load feature flags
  const loadFlags = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/feature-flags');
      if (response.ok) {
        const data = await response.json();
        setFlags(data.flags);
      } else {
        console.error('Failed to load feature flags');
      }
    } catch (error) {
      console.error('Error loading feature flags:', error);
    } finally {
      setLoading(false);
    }
  };

  // Toggle feature flag
  const toggleFlag = async (flagKey: string, enabled: boolean) => {
    try {
      setUpdating(flagKey);
      const response = await fetch('/api/admin/feature-flags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ flagKey, enabled }),
      });

      if (response.ok) {
        const data = await response.json();
        setFlags(data.flags);
      } else {
        console.error('Failed to update feature flag');
      }
    } catch (error) {
      console.error('Error updating feature flag:', error);
    } finally {
      setUpdating(null);
    }
  };

  useEffect(() => {
    loadFlags();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span className="ml-2">Loading feature flags...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Flag className="w-5 h-5 text-blue-600" />
          </div>
                      <div>
              <h1 className="text-2xl font-bold text-white">Feature Flags</h1>
              <p className="text-gray-300">Manage feature rollouts and toggles</p>
            </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">Development</Badge>
          <Button onClick={loadFlags} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Feature Flags List */}
      <div className="space-y-4">
        {Object.values(flags).map((flag) => (
          <Card key={flag.key}>
            <CardHeader>
              <div className="flex items-center justify-between">
                                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <CardTitle className="text-lg text-white">{flag.name}</CardTitle>
                      <Badge 
                        variant={flag.enabled ? "default" : "secondary"}
                        className={flag.enabled ? "bg-green-600 text-white border-green-500" : "bg-gray-600 text-gray-200 border-gray-500"}
                      >
                        {flag.enabled ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <CardDescription className="mt-1 text-gray-300">
                      {flag.description}
                    </CardDescription>
                  </div>
                                  <div className="flex items-center space-x-4">
                    {flag.userPercentage !== undefined && (
                      <div className="text-right">
                        <div className="text-sm font-medium text-white">{flag.userPercentage}%</div>
                        <div className="text-xs text-gray-400">rollout</div>
                      </div>
                    )}
                  <Switch
                    checked={flag.enabled}
                    onCheckedChange={(checked) => toggleFlag(flag.key, checked)}
                    disabled={updating === flag.key}
                  />
                </div>
              </div>
            </CardHeader>
                          <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-400">Flag Key:</span>
                    <div className="font-mono text-xs bg-gray-800 text-gray-100 px-2 py-1 rounded mt-1 border border-gray-700">
                      {flag.key}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-400">Last Updated:</span>
                    <div className="text-gray-200 mt-1">
                      {new Date(flag.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-400">Environment:</span>
                    <div className="text-gray-200 mt-1">
                      {process.env.NODE_ENV || 'development'}
                    </div>
                  </div>
                </div>
              </CardContent>
          </Card>
        ))}
      </div>

              {/* Instructions */}
        <Card className="mt-8 border-blue-600 bg-blue-900/20">
          <CardHeader>
            <CardTitle className="text-blue-300 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Development Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-200 space-y-2">
            <p>• <strong>new_onboarding_flow</strong>: Toggle between new and legacy onboarding</p>
            <p>• Changes take effect immediately for new page loads</p>
            <p>• Environment variables override these settings</p>
            <p>• In production, add proper admin role checks</p>
          </CardContent>
        </Card>

      {/* Quick Actions */}
      <div className="mt-6 flex space-x-4">
        <Button
          onClick={() => toggleFlag('new_onboarding_flow', true)}
          disabled={updating !== null}
          variant="outline"
        >
          Enable New Flow
        </Button>
        <Button
          onClick={() => toggleFlag('new_onboarding_flow', false)}
          disabled={updating !== null}
          variant="outline"
        >
          Use Legacy Flow
        </Button>
      </div>
    </div>
  );
} 