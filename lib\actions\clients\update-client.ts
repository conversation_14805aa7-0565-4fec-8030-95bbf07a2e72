"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { clients } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { clientFormSchema } from "@/lib/actions/clients/client.schema";
import { Client } from "@/components/protectedPages/Clients/columns";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

/**
 * Updates an existing client in the database.
 * @param clientData - The full client object, including its ID.
 * @returns The updated client.
 */
export async function updateClient(clientData: Client): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const validatedFields = clientFormSchema.safeParse(clientData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid client data provided." };
    }

    const [updatedClient] = await db
      .update(clients)
      .set(validatedFields.data)
      .where(
        and(eq(clients.id, clientData.id), eq(clients.organizationId, orgId))
      )
      .returning();

    if (!updatedClient) {
      return { success: false, message: "Client not found or you do not have permission to edit it." };
    }

    revalidatePath("/clients");
    return { success: true, message: "Client updated successfully." };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to update client.",
    };
  }
} 