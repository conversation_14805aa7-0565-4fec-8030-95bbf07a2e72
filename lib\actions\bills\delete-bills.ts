"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { bills, billItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, inArray, eq } from "drizzle-orm";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function deleteBills(ids: string[]): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    if (ids.length === 0) {
      return { success: false, message: "No bill IDs provided." };
    }

    await db.transaction(async (tx) => {
        await tx.delete(billItems).where(inArray(billItems.billId, ids));
        await tx.delete(bills).where(and(inArray(bills.id, ids), eq(bills.organizationId, orgId)));
    });

    revalidatePath("/bills");
    return { success: true, message: `${ids.length} bill(s) deleted.` };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : "Failed to delete bills." };
  }
} 