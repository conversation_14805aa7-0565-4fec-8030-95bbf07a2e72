import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface DataTableSkeletonProps {
  columnCount?: number;
  rowCount?: number;
  showSearch?: boolean;
  showActions?: boolean;
  showFilters?: boolean;
}

export function DataTableSkeleton({
  columnCount = 5,
  rowCount = 8,
  showSearch = true,
  showActions = true,
  showFilters = true,
}: DataTableSkeletonProps) {
  return (
    <div className="space-y-4">
      {/* Header with title and actions */}
      <div className="flex justify-between items-center">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-80" />
        </div>
        <div className="flex items-center gap-2">
          {showFilters && <Skeleton className="h-10 w-32" />}
          {showActions && <Skeleton className="h-10 w-24" />}
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Search and filters */}
      {showSearch && (
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-80" />
          <Skeleton className="h-10 w-32" />
        </div>
      )}

      {/* Data table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-6 w-24" />
          </div>
        </CardHeader>
        <CardContent>
          {/* Table header */}
          <div className="border-b pb-2 mb-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columnCount}, 1fr)` }}>
              {Array.from({ length: columnCount }).map((_, i) => (
                <Skeleton key={i} className="h-4 w-20" />
              ))}
            </div>
          </div>

          {/* Table rows */}
          <div className="space-y-3">
            {Array.from({ length: rowCount }).map((_, rowIndex) => (
              <div
                key={rowIndex}
                className="grid gap-4 items-center py-3 border-b border-border/50"
                style={{ gridTemplateColumns: `repeat(${columnCount}, 1fr)` }}
              >
                {Array.from({ length: columnCount }).map((_, colIndex) => (
                  <Skeleton key={colIndex} className="h-4 w-full" />
                ))}
              </div>
            ))}
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4 pt-4 border-t">
            <Skeleton className="h-4 w-32" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-8 w-8" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 