"use client";

import React, { useState, useRef, useEffect } from "react";
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>eader, Sheet<PERSON>itle, SheetDescription } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Upload, Download, FileText, AlertCircle, CheckCircle, Loader2, AlertTriangle, X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  parseAccountsCSV, 
  ParsedA<PERSON>unt, 
  CSVParseResult, 
  getSelectedValidAccounts,
  toggleAccountSelection,
  selectAllValidAccounts,
  deselectAllAccounts,
  exportAccountsToCSV
} from "@/lib/utils/csv-parser";
import { toast } from "sonner";
// Remove direct import of professional-chart-of-accounts.json, as it should be loaded via server action for security.
// import professionalChartData from "@/lib/data/professional-chart-of-accounts.json";
import { coaTemplatesMetadata, getTemplateAccounts } from "@/lib/data/coaTemplates";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { DropdownMenu, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface ImportAccountsSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportSuccess: () => void;
  organizationId: string;
  importAccountsAction: typeof import("@/lib/actions/accounting/import-accounts").importAccounts;
}

export default function ImportAccountsSheet({
  open,
  onOpenChange,
  onImportSuccess,
  organizationId,
  importAccountsAction,
}: ImportAccountsSheetProps) {
  const [activeTab, setActiveTab] = useState("csv");
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [parseResult, setParseResult] = useState<CSVParseResult | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [selectedDefaultAccounts, setSelectedDefaultAccounts] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Template picker UI
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);

  // Template preview state
  const [templatePreview, setTemplatePreview] = useState<any[] | null>(null);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(true);

  // Preview filter and columns state
  const [previewSearch, setPreviewSearch] = useState("");
  const [previewColumns, setPreviewColumns] = useState({
    gaapCategory: false,
    ifrsCategory: false,
    subcategory: false,
    description: false,
  });

  // Filtered and grouped preview data
  const filteredPreview = templatePreview
    ? templatePreview.filter((acc: any) => {
        const q = previewSearch.toLowerCase();
        return (
          acc.accountNumber?.toLowerCase().includes(q) ||
          acc.name?.toLowerCase().includes(q) ||
          acc.type?.toLowerCase().includes(q)
        );
      })
    : [];
  // Group by type
  const groupedPreview = filteredPreview.reduce((groups: Record<string, any[]>, acc: any) => {
    if (!groups[acc.type]) groups[acc.type] = [];
    groups[acc.type].push(acc);
    return groups;
  }, {} as Record<string, any[]>);

  // Load preview when template is selected
  useEffect(() => {
    if (selectedTemplateId) {
      setIsPreviewLoading(true);
      getTemplateAccounts(selectedTemplateId).then((accounts) => {
        setTemplatePreview(accounts || []);
        setIsPreviewLoading(false);
      });
    } else {
      setTemplatePreview(null);
    }
  }, [selectedTemplateId]);

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error("Please select a CSV file");
      return;
    }

    setCsvFile(file);
    setParseResult(null);

    try {
      const result = await parseAccountsCSV(file);
      setParseResult(result);
      
      if (result.success) {
        toast.success(`Parsed ${result.data.length} accounts from CSV`);
        if (result.warnings.length > 0) {
          toast.warning(`${result.warnings.length} warnings found. Check preview for details.`);
        }
      } else {
        toast.error("CSV parsing failed. Check errors below.");
      }
    } catch (error) {
      toast.error("Failed to parse CSV file");
      setParseResult({
        success: false,
        data: [],
        errors: ["Failed to read CSV file"],
        warnings: [],
        totalAccounts: 0,
        validAccounts: 0,
        accountsWithWarnings: 0,
        accountsWithErrors: 0
      });
    }
  };

  const handleCSVImport = async () => {
    if (!parseResult?.data.length) {
      toast.error("No data to import");
      return;
    }

    const selectedAccounts = getSelectedValidAccounts(parseResult.data);
    if (selectedAccounts.length === 0) {
      toast.error("No valid accounts selected for import");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      const result = await importAccountsAction({ organizationId, parsedAccounts: selectedAccounts });
      clearInterval(progressInterval);
      setImportProgress(100);

      if (result.success) {
        toast.success(`Successfully imported ${result.importedCount} accounts.`);
        onImportSuccess();
        onOpenChange(false);
        resetState();
      } else if (result.errors) {
        toast.error(result.errors.join("\n"));
      } else if (result.duplicates) {
        toast.error(result.duplicates.join("\n"));
      } else {
        toast.error("Import failed. Please try again.");
      }
    } catch (error) {
      toast.error("Import failed. Please try again.");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
    }
  };

  // Dry run/validation modal state
  const [showValidation, setShowValidation] = useState(false);
  const [validationSummary, setValidationSummary] = useState<any>(null);
  const [isImportingFinal, setIsImportingFinal] = useState(false);
  const [importProgressValue, setImportProgressValue] = useState(0);
  const [importResult, setImportResult] = useState<any>(null);
  const [backupAccounts, setBackupAccounts] = useState<any[] | null>(null);
  const [undoAvailable, setUndoAvailable] = useState(false);

  // Helper to fetch current chart of accounts for backup (simulate for now)
  async function fetchCurrentAccounts() {
    // TODO: Replace with real API call
    return [];
  }

  // Dry run/validation for template import
  const handleTemplateImport = async (templateId: string) => {
    setIsPreviewLoading(true);
    const templateAccounts = await getTemplateAccounts(templateId);
    setIsPreviewLoading(false);
    if (!templateAccounts) {
      toast.error("Failed to load template accounts.");
      return;
    }
    // Simulate validation: check for duplicate names/numbers in template
    const nameSet = new Set();
    const numberSet = new Set();
    const duplicates: string[] = [];
    for (const acc of templateAccounts) {
      if (nameSet.has(acc.name.toLowerCase())) duplicates.push(`Duplicate name: ${acc.name}`);
      if (numberSet.has(acc.accountNumber)) duplicates.push(`Duplicate number: ${acc.accountNumber}`);
      nameSet.add(acc.name.toLowerCase());
      numberSet.add(acc.accountNumber);
    }
    setValidationSummary({
      count: templateAccounts.length,
      duplicates,
      templateAccounts,
      templateId,
    });
    setShowValidation(true);
  };

  // Final import after validation
  const handleConfirmImport = async () => {
    setShowValidation(false);
    setIsImportingFinal(true);
    setImportProgressValue(0);
    // Backup current accounts
    const backup = await fetchCurrentAccounts();
    setBackupAccounts(backup);
    setUndoAvailable(false);
    // Simulate progress
    const progressInterval = setInterval(() => {
      setImportProgressValue((v) => Math.min(v + 10, 90));
    }, 100);
    // Do the import
    const result = await importAccountsAction({ organizationId, templateId: validationSummary.templateId });
    clearInterval(progressInterval);
    setImportProgressValue(100);
    setIsImportingFinal(false);
    setImportResult(result);
    setUndoAvailable(true);
    if (result.success) {
      toast.success(`Successfully imported ${result.importedCount} accounts.`);
      onImportSuccess();
      onOpenChange(false);
      resetState();
    } else if (result.errors) {
      toast.error(result.errors.join("\n"));
    } else if (result.duplicates) {
      toast.error(result.duplicates.join("\n"));
    } else {
      toast.error("Import failed. Please try again.");
    }
  };

  // Undo import (restore backup)
  const handleUndoImport = async () => {
    if (!backupAccounts) return;
    // TODO: Replace with real API call to restore accounts
    setUndoAvailable(false);
    toast.success("Chart of accounts restored to previous state.");
  };

  const downloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/templates/accounts-template.csv';
    link.download = 'accounts-template.csv';
    link.click();
    toast.success("Template downloaded");
  };

  const resetState = () => {
    setCsvFile(null);
    setParseResult(null);
    setImportProgress(0);
    setSelectedDefaultAccounts([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleToggleSelection = (index: number) => {
    if (!parseResult) return;
    const updatedAccounts = toggleAccountSelection(parseResult.data, index);
    setParseResult({
      ...parseResult,
      data: updatedAccounts
    });
  };

  const handleSelectAll = () => {
    if (!parseResult) return;
    const updatedAccounts = selectAllValidAccounts(parseResult.data);
    setParseResult({
      ...parseResult,
      data: updatedAccounts
    });
  };

  const handleDeselectAll = () => {
    if (!parseResult) return;
    const updatedAccounts = deselectAllAccounts(parseResult.data);
    setParseResult({
      ...parseResult,
      data: updatedAccounts
    });
  };

  const handleExportCorrected = () => {
    if (!parseResult) return;
    exportAccountsToCSV(parseResult.data, 'corrected-accounts.csv');
    toast.success("Corrected CSV exported");
  };

  // Initialize selected default accounts when switching to default tab
  React.useEffect(() => {
    if (activeTab === "default" && selectedDefaultAccounts.length === 0) {
      // setSelectedDefaultAccounts(professionalChartData.map((acc: any) => acc.accountNumber)); // This line is removed as professionalChartData is no longer imported directly.
    }
  }, [activeTab, selectedDefaultAccounts.length]);

  const handleToggleDefaultAccount = (accountNumber: string) => {
    setSelectedDefaultAccounts(prev => 
      prev.includes(accountNumber) 
        ? prev.filter(num => num !== accountNumber)
        : [...prev, accountNumber]
    );
  };

  // Add state for ultimate template data
  const [ultimateTemplateData, setUltimateTemplateData] = useState<any[]>([]);

  // Function to load ultimate template data
  const loadUltimateTemplateData = async () => {
    const data = await getTemplateAccounts('ultimate');
    setUltimateTemplateData(Array.isArray(data) ? data : []);
  };

  // Example: Load on tab switch to 'default' (or wherever you previously used professionalChartData)
  useEffect(() => {
    if (activeTab === "default" && ultimateTemplateData.length === 0) {
      loadUltimateTemplateData();
    }
  }, [activeTab, ultimateTemplateData.length]);

  // Update usages of professionalChartData to use ultimateTemplateData
  // For example, when selecting all default accounts:
  const handleSelectAllDefault = () => {
    setSelectedDefaultAccounts(ultimateTemplateData.map((acc: any) => acc.accountNumber));
  };

  const handleDeselectAllDefault = () => {
    setSelectedDefaultAccounts([]);
  };

  const handleSheetClose = (open: boolean) => {
    if (!isImporting) {
      onOpenChange(open);
      if (!open) {
        resetState();
      }
    }
  };

  // Bulk edit modal state
  const [showBulkEdit, setShowBulkEdit] = useState(false);
  const [bulkEditAccounts, setBulkEditAccounts] = useState<any[] | null>(null);

  // Simulate export current chart
  const handleExportCurrentChart = async () => {
    // TODO: Replace with real API call to fetch current accounts
    const currentAccounts: any[] = [];
    exportAccountsToCSV(currentAccounts, 'current-chart-of-accounts.csv');
  };

  // Simulate save as template
  const handleSaveAsTemplate = async () => {
    // TODO: Replace with real API call to save current chart as template
    toast.success('Current chart saved as custom template!');
  };

  // Bulk edit handlers
  const handleOpenBulkEdit = () => {
    setBulkEditAccounts(templatePreview ? [...templatePreview] : []);
    setShowBulkEdit(true);
  };
  const handleBulkEditChange = (index: number, field: string, value: string) => {
    setBulkEditAccounts((prev) =>
      prev ? prev.map((acc, i) => (i === index ? { ...acc, [field]: value } : acc)) : prev
    );
  };
  const handleBulkEditSave = () => {
    setShowBulkEdit(false);
    setTemplatePreview(bulkEditAccounts);
    toast.success('Bulk edits applied to template preview.');
  };

  return (
    <Sheet open={open} onOpenChange={handleSheetClose}>
      <SheetContent 
        side="right" 
        className="!w-[98vw] sm:!w-[95vw] md:!w-[90vw] lg:!w-[85vw] xl:!w-[80vw] 2xl:!w-[75vw] !max-w-none p-0 gap-0 overflow-hidden"
        aria-describedby="import-accounts-description"
      >
        <SheetHeader className="px-6 py-4 sm:px-8 sm:py-5 border-b">
          <SheetTitle className="text-xl sm:text-2xl">Import Chart of Accounts</SheetTitle>
          <SheetDescription id="import-accounts-description">
            Import accounts from a CSV file or use a pre-built professional chart of accounts template
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="px-6 pt-4 sm:px-8 sm:pt-5">
              <TabsList className="grid w-full grid-cols-2 h-10 sm:h-11">
                <TabsTrigger value="csv" className="text-sm sm:text-base">Import from CSV</TabsTrigger>
                <TabsTrigger value="template" className="text-sm sm:text-base">Import from Template</TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-y-auto px-6 pb-6 sm:px-8 sm:pb-8">
              <TabsContent value="csv" className="space-y-4 mt-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                      <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                      Upload CSV File
                    </CardTitle>
                    <CardDescription className="text-xs sm:text-sm">
                      Upload a CSV file containing your chart of accounts. Make sure it follows the required format.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                      <Button 
                        onClick={() => fileInputRef.current?.click()} 
                        variant="outline" 
                        disabled={isImporting}
                        size="sm"
                        className="w-full sm:w-auto"
                      >
                        <Upload className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Choose CSV File
                      </Button>
                      <Button 
                        onClick={downloadTemplate} 
                        variant="ghost" 
                        size="sm"
                        className="w-full sm:w-auto"
                      >
                        <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Download Template
                      </Button>
                    </div>

                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".csv"
                      onChange={handleFileSelect}
                      className="hidden"
                    />

                    {csvFile && (
                      <div className="text-xs sm:text-sm text-muted-foreground">
                        Selected: {csvFile.name} ({(csvFile.size / 1024).toFixed(1)} KB)
                      </div>
                    )}

                    {parseResult && (
                      <div className="space-y-4">
                        {parseResult.errors.length > 0 && (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              <div className="font-semibold mb-2 text-xs sm:text-sm">Errors found:</div>
                              <ul className="list-disc list-inside space-y-1">
                                {parseResult.errors.map((error, index) => (
                                  <li key={index} className="text-xs">{error}</li>
                                ))}
                              </ul>
                            </AlertDescription>
                          </Alert>
                        )}

                        {parseResult.warnings.length > 0 && (
                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              <div className="font-semibold mb-2 text-xs sm:text-sm">Warnings:</div>
                              <ul className="list-disc list-inside space-y-1">
                                {parseResult.warnings.map((warning, index) => (
                                  <li key={index} className="text-xs">{warning}</li>
                                ))}
                              </ul>
                            </AlertDescription>
                          </Alert>
                        )}

                        {parseResult.data.length > 0 && (
                          <div className="space-y-2">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                              <h4 className="font-semibold flex items-center gap-2 text-sm sm:text-base">
                                {parseResult.validAccounts > 0 && <CheckCircle className="h-4 w-4 text-green-600" />}
                                {parseResult.accountsWithWarnings > 0 && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                                {parseResult.accountsWithErrors > 0 && <X className="h-4 w-4 text-red-600" />}
                                Preview ({parseResult.totalAccounts} accounts)
                              </h4>
                              <div className="flex flex-wrap gap-1 sm:gap-2">
                                <Button variant="outline" size="sm" onClick={handleSelectAll} className="text-xs">
                                  Select Valid
                                </Button>
                                <Button variant="outline" size="sm" onClick={handleDeselectAll} className="text-xs">
                                  Deselect All
                                </Button>
                                {(parseResult.accountsWithErrors > 0 || parseResult.accountsWithWarnings > 0) && (
                                  <Button variant="outline" size="sm" onClick={handleExportCorrected} className="text-xs">
                                    <Download className="h-3 w-3 mr-1" />
                                    Export
                                  </Button>
                                )}
                              </div>
                            </div>

                            <div className="flex flex-wrap gap-2 sm:gap-4 text-xs sm:text-sm">
                              <span className="flex items-center gap-1">
                                <CheckCircle className="h-3 w-3 text-green-600" />
                                {parseResult.validAccounts} Valid
                              </span>
                              {parseResult.accountsWithWarnings > 0 && (
                                <span className="flex items-center gap-1">
                                  <AlertTriangle className="h-3 w-3 text-yellow-600" />
                                  {parseResult.accountsWithWarnings} Warnings
                                </span>
                              )}
                              {parseResult.accountsWithErrors > 0 && (
                                <span className="flex items-center gap-1">
                                  <X className="h-3 w-3 text-red-600" />
                                  {parseResult.accountsWithErrors} Errors
                                </span>
                              )}
                            </div>
                            
                            <div className="border rounded-md overflow-hidden">
                              <div className="overflow-x-auto max-h-80 sm:max-h-96 lg:max-h-[28rem] xl:max-h-[32rem]">
                                <Table>
                                  <TableHeader>
                                    <TableRow>
                                      <TableHead className="w-8 sm:w-12">
                                        <Checkbox
                                          checked={parseResult.data.filter(acc => acc.validationStatus !== 'error').every(acc => acc.selected)}
                                          onCheckedChange={(checked) => checked ? handleSelectAll() : handleDeselectAll()}
                                        />
                                      </TableHead>
                                      <TableHead className="w-16">Status</TableHead>
                                      <TableHead className="w-20 sm:w-24">Account #</TableHead>
                                      <TableHead className="min-w-[140px]">Name</TableHead>
                                      <TableHead className="w-16 sm:w-20">Type</TableHead>
                                      <TableHead className="hidden sm:table-cell w-16 sm:w-20">Balance</TableHead>
                                      <TableHead className="min-w-[120px] sm:min-w-[160px]">Issues</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {parseResult.data.map((account, index) => (
                                      <TableRow key={index} className={account.validationStatus === 'error' ? 'bg-red-50' : ''}>
                                        <TableCell className="p-2">
                                          <Checkbox
                                            checked={account.selected}
                                            disabled={account.validationStatus === 'error'}
                                            onCheckedChange={() => handleToggleSelection(index)}
                                          />
                                        </TableCell>
                                        <TableCell className="p-2">
                                          {account.validationStatus === 'valid' && <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />}
                                          {account.validationStatus === 'warning' && <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-600" />}
                                          {account.validationStatus === 'error' && <X className="h-3 w-3 sm:h-4 sm:w-4 text-red-600" />}
                                        </TableCell>
                                        <TableCell className="font-mono text-xs p-2">
                                          {account.accountNumber || <span className="text-muted-foreground">Auto</span>}
                                        </TableCell>
                                        <TableCell className="text-xs sm:text-sm p-2">{account.name}</TableCell>
                                        <TableCell className="p-2">
                                          <Badge variant="outline" className="text-xs">{account.type}</Badge>
                                        </TableCell>
                                        <TableCell className="hidden sm:table-cell text-xs p-2">{account.normalBalance}</TableCell>
                                        <TableCell className="max-w-[120px] sm:max-w-[160px] lg:max-w-[200px] p-2">
                                          {account.errors.length > 0 && (
                                            <div className="text-red-600 text-xs">
                                              {account.errors.slice(0, 1).join(', ')}
                                              {account.errors.length > 1 && '...'}
                                            </div>
                                          )}
                                          {account.warnings.length > 0 && (
                                            <div className="text-yellow-600 text-xs">
                                              {account.warnings.slice(0, 1).join(', ')}
                                              {account.warnings.length > 1 && '...'}
                                            </div>
                                          )}
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {isImporting && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-xs sm:text-sm">Importing accounts...</span>
                        </div>
                        <Progress value={importProgress} className="w-full" />
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row justify-end gap-2 pt-2">
                      <Button 
                        variant="outline" 
                        onClick={() => handleSheetClose(false)}
                        disabled={isImporting}
                        size="sm"
                        className="w-full sm:w-auto order-2 sm:order-1"
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleCSVImport}
                        disabled={!parseResult?.data.length || getSelectedValidAccounts(parseResult?.data || []).length === 0 || isImporting}
                        size="sm"
                        className="w-full sm:w-auto order-1 sm:order-2"
                      >
                        {isImporting ? (
                          <>
                            <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                            Import {getSelectedValidAccounts(parseResult?.data || []).length} Selected Account{getSelectedValidAccounts(parseResult?.data || []).length !== 1 ? 's' : ''}
                          </>
                        ) : (
                          <span className="text-xs sm:text-sm">
                            Import {getSelectedValidAccounts(parseResult?.data || []).length} Selected Account{getSelectedValidAccounts(parseResult?.data || []).length !== 1 ? 's' : ''}
                          </span>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="template" className="space-y-4 mt-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base sm:text-lg">Chart of Accounts Templates</CardTitle>
                    <CardDescription className="text-xs sm:text-sm">
                      Choose a template that best fits your business. You can only import one template at a time.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                      {coaTemplatesMetadata.map((template) => (
                        <div
                          key={template.id}
                          className={`border rounded-lg p-4 cursor-pointer transition-colors ${selectedTemplateId === template.id ? 'border-primary bg-primary/10' : 'border-muted bg-background'}`}
                          onClick={() => setSelectedTemplateId(template.id)}
                        >
                          <div className="font-semibold text-base mb-1">{template.templateName}</div>
                          <div className="text-xs text-muted-foreground mb-2">{template.description}</div>
                          {template.isDefault && <Badge className="bg-green-100 text-green-800">Recommended</Badge>}
                        </div>
                      ))}
                    </div>
                    {selectedTemplateId && (
                      <>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                          <input
                            type="text"
                            placeholder="Search accounts..."
                            value={previewSearch}
                            onChange={e => setPreviewSearch(e.target.value)}
                            className="border rounded px-2 py-1 text-sm w-full sm:w-64"
                          />
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm">Columns</Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuCheckboxItem
                                checked={previewColumns.gaapCategory}
                                onCheckedChange={v => setPreviewColumns(c => ({ ...c, gaapCategory: v as boolean }))}
                              >GAAP Category</DropdownMenuCheckboxItem>
                              <DropdownMenuCheckboxItem
                                checked={previewColumns.ifrsCategory}
                                onCheckedChange={v => setPreviewColumns(c => ({ ...c, ifrsCategory: v as boolean }))}
                              >IFRS Category</DropdownMenuCheckboxItem>
                              <DropdownMenuCheckboxItem
                                checked={previewColumns.subcategory}
                                onCheckedChange={v => setPreviewColumns(c => ({ ...c, subcategory: v as boolean }))}
                              >Subcategory</DropdownMenuCheckboxItem>
                              <DropdownMenuCheckboxItem
                                checked={previewColumns.description}
                                onCheckedChange={v => setPreviewColumns(c => ({ ...c, description: v as boolean }))}
                              >Description</DropdownMenuCheckboxItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        <div className="flex flex-wrap gap-2 mb-2">
                          <Button variant="outline" size="sm" onClick={() => templatePreview && exportAccountsToCSV(templatePreview, 'template-preview.csv')} disabled={!templatePreview || templatePreview.length === 0}>Export Preview as CSV</Button>
                          <Button variant="outline" size="sm" onClick={handleExportCurrentChart}>Export Current Chart as CSV</Button>
                          <Button variant="outline" size="sm" onClick={handleSaveAsTemplate}>Save Current Chart as Template</Button>
                          <Button variant="outline" size="sm" onClick={handleOpenBulkEdit} disabled={!templatePreview || templatePreview.length === 0}>Bulk Edit Preview</Button>
                        </div>
                        <Collapsible open={previewOpen} onOpenChange={setPreviewOpen}>
                          <CollapsibleTrigger asChild>
                            <Button variant="outline" size="sm" className="mb-2">
                              {previewOpen ? "Hide" : "Show"} Template Preview
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            {isPreviewLoading ? (
                              <div className="flex items-center gap-2 py-4"><Loader2 className="animate-spin h-4 w-4" /> Loading preview...</div>
                            ) : filteredPreview.length > 0 ? (
                              <div className="border rounded-md overflow-hidden mb-4">
                                <div className="overflow-x-auto max-h-80 sm:max-h-96 lg:max-h-[28rem] xl:max-h-[32rem]">
                                  <Table>
                                    <TableHeader>
                                      <TableRow>
                                        <TableHead className="w-20 sm:w-24">Account #</TableHead>
                                        <TableHead className="min-w-[140px]">Name</TableHead>
                                        <TableHead className="w-16 sm:w-20">Type</TableHead>
                                        <TableHead className="hidden sm:table-cell w-16 sm:w-20">Balance</TableHead>
                                        <TableHead className="hidden md:table-cell min-w-[100px] sm:min-w-[120px]">Parent/Header</TableHead>
                                        {previewColumns.gaapCategory && <TableHead>GAAP</TableHead>}
                                        {previewColumns.ifrsCategory && <TableHead>IFRS</TableHead>}
                                        {previewColumns.subcategory && <TableHead>Subcategory</TableHead>}
                                        {previewColumns.description && <TableHead>Description</TableHead>}
                                      </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                      {Object.entries(groupedPreview).map(([type, accounts]) => (
                                        <React.Fragment key={type}>
                                          <TableRow>
                                            <TableCell colSpan={5 + Object.values(previewColumns).filter(Boolean).length} className="bg-muted font-semibold text-xs uppercase text-muted-foreground">
                                              {type.charAt(0).toUpperCase() + type.slice(1)}
                                            </TableCell>
                                          </TableRow>
                                          {accounts.map((account: any) => (
                                            <TableRow key={account.accountNumber}>
                                              <TableCell className="font-mono text-xs p-2" style={{ paddingLeft: `${account.level * 16}px` }}>{account.accountNumber}</TableCell>
                                              <TableCell className="text-xs sm:text-sm p-2">{account.name}</TableCell>
                                              <TableCell className="p-2">
                                                <Badge variant="outline" className="text-xs">{account.type}</Badge>
                                              </TableCell>
                                              <TableCell className="hidden sm:table-cell text-xs p-2">{account.normalBalance}</TableCell>
                                              <TableCell className="hidden md:table-cell p-2">
                                                {account.isHeader ? (
                                                  <Badge variant="secondary" className="text-xs">Header</Badge>
                                                ) : account.parentAccountNumber ? (
                                                  <span className="text-xs text-muted-foreground">
                                                    subaccount of {account.parentAccountNumber}
                                                  </span>
                                                ) : (
                                                  <span className="text-xs text-muted-foreground">-</span>
                                                )}
                                              </TableCell>
                                              {previewColumns.gaapCategory && <TableCell className="text-xs">{account.gaapCategory}</TableCell>}
                                              {previewColumns.ifrsCategory && <TableCell className="text-xs">{account.ifrsCategory}</TableCell>}
                                              {previewColumns.subcategory && <TableCell className="text-xs">{account.subcategory}</TableCell>}
                                              {previewColumns.description && <TableCell className="text-xs">{account.description}</TableCell>}
                                            </TableRow>
                                          ))}
                                        </React.Fragment>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </div>
                              </div>
                            ) : (
                              <div className="text-xs text-muted-foreground py-4">No accounts found in this template.</div>
                            )}
                          </CollapsibleContent>
                        </Collapsible>
                      </>
                    )}
                    <Button
                      onClick={() => selectedTemplateId && handleTemplateImport(selectedTemplateId)}
                      disabled={!selectedTemplateId || isImporting}
                      className="mt-4"
                    >
                      {isImporting ? <Loader2 className="animate-spin mr-2 h-4 w-4" /> : null}
                      Import Selected Template
                    </Button>
                    {undoAvailable && (
                      <Button variant="outline" onClick={handleUndoImport} className="mt-2">Undo Import</Button>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </SheetContent>
      <Dialog open={showValidation} onOpenChange={setShowValidation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Review Import</DialogTitle>
          </DialogHeader>
          <div className="mb-2 text-sm">You are about to import <b>{validationSummary?.count}</b> accounts from this template.</div>
          {validationSummary?.duplicates?.length > 0 && (
            <div className="mb-2 text-xs text-red-600">
              <b>Potential issues:</b>
              <ul className="list-disc ml-4">
                {validationSummary.duplicates.map((d: string, i: number) => <li key={i}>{d}</li>)}
              </ul>
            </div>
          )}
          <div className="mb-2 text-xs text-muted-foreground">Please confirm to proceed with the import.</div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowValidation(false)}>Cancel</Button>
            <Button onClick={handleConfirmImport}>Confirm Import</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* Import progress and result summary */}
      {isImportingFinal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/30 z-50">
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 w-full max-w-md flex flex-col items-center">
            <div className="mb-2 flex items-center gap-2"><Loader2 className="animate-spin h-5 w-5" /> Importing accounts...</div>
            <Progress value={importProgressValue} className="w-full mb-2" />
          </div>
        </div>
      )}
      {importResult && (
        <Dialog open={!!importResult} onOpenChange={() => setImportResult(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Import Summary</DialogTitle>
            </DialogHeader>
            <div className="mb-2 text-sm">{importResult.success ? `Successfully imported ${importResult.importedCount} accounts.` : "Some accounts failed to import."}</div>
            {importResult.errors && (
              <div className="mb-2 text-xs text-red-600">
                <b>Errors:</b>
                <ul className="list-disc ml-4">
                  {importResult.errors.map((e: string, i: number) => <li key={i}>{e}</li>)}
                </ul>
              </div>
            )}
            {importResult.duplicates && (
              <div className="mb-2 text-xs text-yellow-700">
                <b>Duplicates:</b>
                <ul className="list-disc ml-4">
                  {importResult.duplicates.map((d: string, i: number) => <li key={i}>{d}</li>)}
                </ul>
              </div>
            )}
            <DialogFooter>
              <Button onClick={() => setImportResult(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      <Dialog open={showBulkEdit} onOpenChange={setShowBulkEdit}>
        <DialogContent className="max-w-3xl w-full">
          <DialogHeader>
            <DialogTitle>Bulk Edit Template Accounts</DialogTitle>
          </DialogHeader>
          <div className="overflow-x-auto max-h-96">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Account #</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Balance</TableHead>
                  <TableHead>Description</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bulkEditAccounts?.map((acc, i) => (
                  <TableRow key={i}>
                    <TableCell><input className="border rounded px-1 w-20" value={acc.accountNumber} onChange={e => handleBulkEditChange(i, 'accountNumber', e.target.value)} /></TableCell>
                    <TableCell><input className="border rounded px-1 w-32" value={acc.name} onChange={e => handleBulkEditChange(i, 'name', e.target.value)} /></TableCell>
                    <TableCell><input className="border rounded px-1 w-20" value={acc.type} onChange={e => handleBulkEditChange(i, 'type', e.target.value)} /></TableCell>
                    <TableCell><input className="border rounded px-1 w-16" value={acc.normalBalance} onChange={e => handleBulkEditChange(i, 'normalBalance', e.target.value)} /></TableCell>
                    <TableCell><input className="border rounded px-1 w-40" value={acc.description || ''} onChange={e => handleBulkEditChange(i, 'description', e.target.value)} /></TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkEdit(false)}>Cancel</Button>
            <Button onClick={handleBulkEditSave}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Sheet>
  );
} 