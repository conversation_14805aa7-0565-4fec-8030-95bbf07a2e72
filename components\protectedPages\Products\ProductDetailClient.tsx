"use client";

import { OptimizationSuggestions } from "@/components/protectedPages/Products/OptimizationSuggestions";
import { CostBreakdownChart } from "@/components/protectedPages/Products/charts/CostBreakdownChart";
import { RevenueCostTrendChart } from "@/components/protectedPages/Products/charts/RevenueCostTrendChart";
import { type ProductCost } from "@/lib/services/cost-calculation.service";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { type getProductById } from "@/lib/actions/products/get-product-by-id";

type ProductWithDetails = NonNullable<Awaited<ReturnType<typeof getProductById>>>;


interface ProductDetailClientProps {
  product: ProductWithDetails;
  costData: ProductCost | null;
  trendData: any[];
}

const formatCurrency = (value: number | string | null | undefined) => {
    if (value === null || value === undefined) return "N/A";
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(numValue)) return "N/A";
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
    }).format(numValue);
};

export function ProductDetailClient({
  product,
  costData,
  trendData,
}: ProductDetailClientProps) {

  if (!product) {
      return <div>Product not found.</div>
  }
  
  const price = product.price ? Number(product.price) : null;
  const costBasis = product.costBasis ? Number(product.costBasis) : null;
  const totalRevenue = product.totalRevenue ? Number(product.totalRevenue) : 0;
  const totalCosts = product.totalCosts ? Number(product.totalCosts) : 0;

  const grossMargin = price !== null && costBasis !== null ? price - costBasis : null;
  const grossMarginPercentage = grossMargin !== null && price !== null && price > 0 ? (grossMargin / price) * 100 : null;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
            <h1 className="text-3xl font-bold">{product.name}</h1>
            <p className="text-muted-foreground">{product.description}</p>
            {product.category && <Badge variant="outline">{product.category.name}</Badge>}
        </div>
        <OptimizationSuggestions productId={product.id} />
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Profitability Metrics</CardTitle>
          <CardDescription>Key performance indicators for this product.</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Price</div>
                <div className="text-2xl font-bold">{formatCurrency(price)}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Cost Basis</div>
                <div className="text-2xl font-bold">{formatCurrency(costBasis)}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Gross Margin</div>
                <div className="text-2xl font-bold">{formatCurrency(grossMargin)}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Margin %</div>
                <div className="text-2xl font-bold">{grossMarginPercentage ? `${grossMarginPercentage.toFixed(2)}%` : 'N/A'}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Units Sold</div>
                <div className="text-2xl font-bold">{product.totalSold ?? 0}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Total Revenue</div>
                <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Total Costs</div>
                <div className="text-2xl font-bold">{formatCurrency(totalCosts)}</div>
            </div>
            <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">Net Profit</div>
                <div className="text-2xl font-bold">{formatCurrency(totalRevenue - totalCosts)}</div>
            </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
            <CardHeader>
                <CardTitle>Account Mappings</CardTitle>
                <CardDescription>Accounts used for automated transaction recording.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Revenue Account</span>
                    <span>{product.revenueAccount?.name ?? 'Not Set'}</span>
                </div>
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Cost of Goods Sold (COGS) Account</span>
                    <span>{product.cogsAccount?.name ?? 'Not Set'}</span>
                </div>
                <div className="flex justify-between">
                    <span className="text-muted-foreground">Inventory Asset Account</span>
                    <span>{product.inventoryAccount?.name ?? 'Not Set'}</span>
                </div>
            </CardContent>
        </Card>
        <Card>
            <CardHeader>
                <CardTitle>Cost Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
                <CostBreakdownChart data={costData?.breakdown || []} />
            </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
            <CardTitle>Revenue vs. Cost Trends</CardTitle>
        </CardHeader>
        <CardContent>
            <RevenueCostTrendChart data={trendData} />
        </CardContent>
      </Card>

    </div>
  );
} 