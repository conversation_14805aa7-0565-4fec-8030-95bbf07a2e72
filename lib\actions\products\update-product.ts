"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { ProductBusinessRules } from "./product.rules";

const productUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  description: z.string().optional(),
  price: z.number().positive("Price must be a positive number").optional(),
  productType: z.enum([
    "service",
    "digital_service",
    "physical_product",
    "digital_product",
    "subscription",
    "bundle",
  ]).optional(),
  revenueAccountId: z.string().min(1, "Revenue account is required").optional(),
  cogsAccountId: z.string().optional(),
  inventoryAccountId: z.string().optional(),
  costBasis: z.number().nonnegative("Cost must be a positive number").optional(),
});

type ProductUpdateData = z.infer<typeof productUpdateSchema>;

type ActionResponse = Promise<{
  success: boolean;
  message: string;
  data?: any;
}>;

export async function updateProduct(
  productId: string,
  productData: ProductUpdateData
): ActionResponse {
  try {
    const validatedFields = productUpdateSchema.safeParse(productData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid product data provided." };
    }

    const businessRules = new ProductBusinessRules();
    const validation = businessRules.validateProductSetup(validatedFields.data as any);
    
    if (!validation.isValid) {
        return { success: false, message: validation.errors.join('\\n') };
    }

    const { ...updateData } = validatedFields.data;

    const [updatedProduct] = await db
      .update(products)
      .set({
        ...updateData,
        price: updateData.price ? updateData.price.toString() : undefined,
        costBasis: updateData.costBasis ? updateData.costBasis.toString() : undefined,
        updatedAt: new Date(),
      })
      .where(eq(products.id, productId))
      .returning();

    if (!updatedProduct) {
      return { success: false, message: "Product not found or failed to update." };
    }

    revalidatePath("/products");
    return {
      success: true,
      message: "Product updated successfully.",
      data: updatedProduct,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update product.",
    };
  }
} 