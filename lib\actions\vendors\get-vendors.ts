"use server";

import { db } from "@/db/drizzle";
import { vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";

export async function getVendors() {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const vendorList = await db
      .select()
      .from(vendors)
      .where(eq(vendors.organizationId, orgId))
      .orderBy(desc(vendors.createdAt));
    return vendorList;
  } catch (error) {
    console.error("Failed to fetch vendors:", error);
    return [];
  }
} 