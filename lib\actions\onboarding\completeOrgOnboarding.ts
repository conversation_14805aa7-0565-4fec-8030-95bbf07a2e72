"use server";
import { updateOrganizationSettingsAction } from "@/lib/actions/organization/update-organization-settings";
import { getUserOrganizationId } from "@/lib/actions/user/get-user-organization-id";
import { db } from "@/db/drizzle";
import { onboarding } from "@/db/schema/schema";
import { organization } from "@/db/schema/schema";
import { eq } from "drizzle-orm";
import { createAccountsFromTemplate } from "./createAccountsFromTemplate";

export async function completeOrgOnboarding(userId: string, onboardingData: any) {
  try {
    // Get the user's organizationId
    const organizationId = await getUserOrganizationId(userId);
    if (!organizationId) {
      return { success: false, error: "No organization found for user." };
    }

    // Prepare organization update values from onboardingData
    const orgInfo = {
      ...(onboardingData.organization || {}),
      ...(onboardingData.orgDetails || {}),
      ...(onboardingData.businessDetails || {}),
    };

    // Build update object with only present fields
    const updateObj: Record<string, any> = {};
    const updatableFields = [
      "name", "slug", "logo", "metadata", "businessType", "email", "industry", "website", "phone", "taxId", "taxIdType", "currency", "legalEntity", "howHeard", "subscriptionId", "planType", "planStatus", "trialEndsAt", "subscriptionCurrentPeriodStart", "subscriptionCurrentPeriodEnd", "monthlyTransactionLimit", "monthlyTransactionCount", "lastTransactionResetDate", "requireTwoFactor"
    ];
    updatableFields.forEach(field => {
      if (orgInfo[field] !== undefined) {
        updateObj[field] = orgInfo[field];
      }
    });
    updateObj.updatedAt = new Date();

    // Directly update the organizations table
    await db.update(organization)
      .set(updateObj)
      .where(eq(organization.id, organizationId));

    // Import chart of accounts template if selected
    let coaImportResult = null;
    let templateId = onboardingData?.businessDetails?.chartTemplate || onboardingData?.chartOfAccounts?.selectedTemplateId;
    if (templateId) {
      try {
        coaImportResult = await createAccountsFromTemplate(organizationId, templateId);
      } catch (err) {
        console.error("Error importing chart of accounts template:", err);
        coaImportResult = { success: false, error: err instanceof Error ? err.message : String(err) };
      }
    }

    // Mark onboarding as completed
    await db.update(onboarding)
      .set({ isCompleted: true, completedAt: new Date() })
      .where(eq(onboarding.userId, userId));

    return { success: true, coaImportResult };
  } catch (error) {
    console.error("Error completing onboarding:", error);
    return { success: false, error: (error as any)?.message || "Unknown error" };
  }
} 