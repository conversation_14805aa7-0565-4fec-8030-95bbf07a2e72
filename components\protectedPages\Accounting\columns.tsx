"use client";

import { accounts } from "@/db/schema/schema";
import { type ColumnDef, Row } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle } from "lucide-react";

export type Account = typeof accounts.$inferSelect;

const formatEnumValue = (value: string | null) => {
  if (!value) return "-";
  return value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getClassificationBadgeColor = (classification: string | null) => {
  switch (classification) {
    case 'current':
      return 'bg-green-100 text-green-800 hover:bg-green-200';
    case 'non_current':
      return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
    case 'operating':
      return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
    case 'non_operating':
      return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
  }
};

export const baseColumns: ColumnDef<Account>[] = [
  { 
    accessorKey: "accountNumber", 
    header: "Account #",
    enableSorting: true,
    sortingFn: (rowA, rowB, columnId) => {
      // Numeric sort for account numbers
      const a = Number(rowA.getValue(columnId));
      const b = Number(rowB.getValue(columnId));
      if (isNaN(a) && isNaN(b)) return 0;
      if (isNaN(a)) return 1;
      if (isNaN(b)) return -1;
      return a - b;
    },
    cell: ({ row }) => {
      const number = row.getValue("accountNumber") as string;
      return number ? (
        <span className="font-mono text-sm font-medium">{number}</span>
      ) : (
        <span className="text-gray-400">-</span>
      );
    }
  },
  { 
    accessorKey: "name", 
    header: "Account Name",
    enableSorting: true,
    cell: ({ row }) => {
      const name = row.getValue("name") as string;
      const isHeader = row.original.isHeader;
      return (
        <div className="flex flex-col">
          <span className={`font-medium ${isHeader ? 'font-bold text-gray-900' : 'text-gray-700'}`}>
            {name}
          </span>
          {row.original.description && (
            <span className="text-xs text-gray-500 mt-1">{row.original.description}</span>
          )}
        </div>
      );
    }
  },
  { 
    accessorKey: "type", 
    header: "Type",
    enableSorting: true,
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      const typeColors = {
        asset: 'bg-blue-100 text-blue-800',
        liability: 'bg-red-100 text-red-800',
        equity: 'bg-purple-100 text-purple-800',
        revenue: 'bg-green-100 text-green-800',
        expense: 'bg-orange-100 text-orange-800',
      };
      return (
        <Badge variant="outline" className={typeColors[type as keyof typeof typeColors]}>
          {formatEnumValue(type)}
        </Badge>
      );
    }
  },
  {
    accessorKey: "classification",
    header: "Classification",
    enableSorting: true,
    cell: ({ row }) => {
      const classification = row.original.classification;
      if (!classification || classification === 'n_a') return <span className="text-gray-400">-</span>;
      return (
        <Badge 
          variant="outline" 
          className={getClassificationBadgeColor(classification)}
        >
          {formatEnumValue(classification)}
        </Badge>
      );
    }
  },
  { 
    accessorKey: "normalBalance", 
    header: "Balance",
    enableSorting: true,
    cell: ({ row }) => {
      const balance = row.getValue("normalBalance") as string;
      return (
        <Badge variant={balance === 'debit' ? 'default' : 'secondary'}>
          {balance === 'debit' ? 'Dr' : 'Cr'}
        </Badge>
      );
    }
  },
]; 

// Helper for global filtering across accountNumber, name, and description
export function accountGlobalFilterFn<RowData extends { accountNumber?: string; name?: string; description?: string }>(
  row: Row<RowData>,
  columnId: string,
  filterValue: string
): boolean {
  const { accountNumber, name, description } = row.original;
  const search = filterValue.toLowerCase();
  return (
    (accountNumber && String(accountNumber).toLowerCase().includes(search)) ||
    (name && name.toLowerCase().includes(search)) ||
    (description && description.toLowerCase().includes(search))
  ) ? true : false;
} 