[{"id": "small-business-starter", "name": "Small Business Starter", "description": "Basic budget template for small businesses with essential accounts", "category": "General", "totalBudget": 120000, "lineItems": [{"accountType": "revenue", "accountNamePattern": "sales revenue|revenue|income", "suggestedName": "Sales Revenue", "budgetedAmount": 120000, "percentage": 100, "notes": "Primary revenue stream"}, {"accountType": "expense", "accountNamePattern": "salaries|wages|payroll", "suggestedName": "Salaries & Wages", "budgetedAmount": 36000, "percentage": 30, "notes": "Employee compensation"}, {"accountType": "expense", "accountNamePattern": "rent|lease", "suggestedName": "Rent Expense", "budgetedAmount": 18000, "percentage": 15, "notes": "Office/facility rent"}, {"accountType": "expense", "accountNamePattern": "marketing|advertising", "suggestedName": "Marketing & Advertising", "budgetedAmount": 12000, "percentage": 10, "notes": "Marketing and promotional activities"}, {"accountType": "expense", "accountNamePattern": "utilities", "suggestedName": "Utilities", "budgetedAmount": 3600, "percentage": 3, "notes": "Electricity, water, internet"}, {"accountType": "expense", "accountNamePattern": "office supplies|supplies", "suggestedName": "Office Supplies", "budgetedAmount": 2400, "percentage": 2, "notes": "General office supplies and materials"}, {"accountType": "expense", "accountNamePattern": "professional services|legal|accounting", "suggestedName": "Professional Services", "budgetedAmount": 6000, "percentage": 5, "notes": "Legal, accounting, consulting fees"}, {"accountType": "expense", "accountNamePattern": "insurance", "suggestedName": "Insurance", "budgetedAmount": 4800, "percentage": 4, "notes": "Business insurance premiums"}, {"accountType": "expense", "accountNamePattern": "travel|travel expense", "suggestedName": "Travel Expenses", "budgetedAmount": 3600, "percentage": 3, "notes": "Business travel and transportation"}]}, {"id": "saas-business", "name": "SaaS Business", "description": "Budget template optimized for Software as a Service businesses", "category": "Technology", "totalBudget": 500000, "lineItems": [{"accountType": "revenue", "accountNamePattern": "subscription revenue|recurring revenue|saas revenue", "suggestedName": "Subscription Revenue", "budgetedAmount": 480000, "percentage": 96, "notes": "Monthly recurring revenue"}, {"accountType": "revenue", "accountNamePattern": "setup fees|onboarding|professional services", "suggestedName": "Setup & Professional Services", "budgetedAmount": 20000, "percentage": 4, "notes": "One-time setup and consulting fees"}, {"accountType": "expense", "accountNamePattern": "salaries|wages|payroll", "suggestedName": "Salaries & Benefits", "budgetedAmount": 200000, "percentage": 40, "notes": "Developer and staff salaries"}, {"accountType": "expense", "accountNamePattern": "hosting|cloud|aws|server", "suggestedName": "Cloud Infrastructure", "budgetedAmount": 50000, "percentage": 10, "notes": "AWS, hosting, and infrastructure costs"}, {"accountType": "expense", "accountNamePattern": "marketing|advertising|customer acquisition", "suggestedName": "Customer Acquisition", "budgetedAmount": 75000, "percentage": 15, "notes": "Digital marketing and customer acquisition"}, {"accountType": "expense", "accountNamePattern": "software|tools|subscriptions", "suggestedName": "Software & Tools", "budgetedAmount": 25000, "percentage": 5, "notes": "Development tools and software subscriptions"}, {"accountType": "expense", "accountNamePattern": "support|customer service", "suggestedName": "Customer Support", "budgetedAmount": 30000, "percentage": 6, "notes": "Customer support and success"}, {"accountType": "expense", "accountNamePattern": "research|development|r&d", "suggestedName": "Research & Development", "budgetedAmount": 40000, "percentage": 8, "notes": "Product development and innovation"}, {"accountType": "expense", "accountNamePattern": "professional services|legal|accounting", "suggestedName": "Professional Services", "budgetedAmount": 15000, "percentage": 3, "notes": "Legal, accounting, and compliance"}]}, {"id": "retail-business", "name": "Retail Business", "description": "Budget template for retail and e-commerce businesses", "category": "Retail", "totalBudget": 300000, "lineItems": [{"accountType": "revenue", "accountNamePattern": "product sales|sales revenue|retail sales", "suggestedName": "Product Sales", "budgetedAmount": 300000, "percentage": 100, "notes": "Revenue from product sales"}, {"accountType": "expense", "accountNamePattern": "cost of goods sold|cogs|inventory", "suggestedName": "Cost of Goods Sold", "budgetedAmount": 150000, "percentage": 50, "notes": "Direct cost of products sold"}, {"accountType": "expense", "accountNamePattern": "salaries|wages|payroll", "suggestedName": "Salaries & Wages", "budgetedAmount": 45000, "percentage": 15, "notes": "Staff compensation"}, {"accountType": "expense", "accountNamePattern": "rent|lease|store rent", "suggestedName": "Store Rent", "budgetedAmount": 36000, "percentage": 12, "notes": "Retail space rental"}, {"accountType": "expense", "accountNamePattern": "marketing|advertising|promotion", "suggestedName": "Marketing & Promotions", "budgetedAmount": 21000, "percentage": 7, "notes": "Marketing and promotional campaigns"}, {"accountType": "expense", "accountNamePattern": "utilities", "suggestedName": "Utilities", "budgetedAmount": 6000, "percentage": 2, "notes": "Store utilities and maintenance"}, {"accountType": "expense", "accountNamePattern": "shipping|delivery|freight", "suggestedName": "Shipping & Delivery", "budgetedAmount": 9000, "percentage": 3, "notes": "Product shipping and delivery costs"}, {"accountType": "expense", "accountNamePattern": "payment processing|credit card fees", "suggestedName": "Payment Processing", "budgetedAmount": 6000, "percentage": 2, "notes": "Credit card and payment processing fees"}, {"accountType": "expense", "accountNamePattern": "insurance", "suggestedName": "Insurance", "budgetedAmount": 4800, "percentage": 1.6, "notes": "Business and liability insurance"}]}, {"id": "consulting-business", "name": "Consulting Business", "description": "Budget template for professional services and consulting firms", "category": "Professional Services", "totalBudget": 250000, "lineItems": [{"accountType": "revenue", "accountNamePattern": "consulting revenue|service revenue|professional fees", "suggestedName": "Consulting Revenue", "budgetedAmount": 240000, "percentage": 96, "notes": "Professional consulting services"}, {"accountType": "revenue", "accountNamePattern": "training|workshop|speaking", "suggestedName": "Training & Workshops", "budgetedAmount": 10000, "percentage": 4, "notes": "Training and workshop revenue"}, {"accountType": "expense", "accountNamePattern": "salaries|wages|contractor|freelancer", "suggestedName": "Contractor & Staff Costs", "budgetedAmount": 100000, "percentage": 40, "notes": "Consultant and staff compensation"}, {"accountType": "expense", "accountNamePattern": "marketing|business development|networking", "suggestedName": "Business Development", "budgetedAmount": 25000, "percentage": 10, "notes": "Marketing and business development"}, {"accountType": "expense", "accountNamePattern": "office|rent|coworking", "suggestedName": "Office Expenses", "budgetedAmount": 18000, "percentage": 7.2, "notes": "Office space and coworking"}, {"accountType": "expense", "accountNamePattern": "travel|client meetings", "suggestedName": "Travel & Client Meetings", "budgetedAmount": 15000, "percentage": 6, "notes": "Client travel and meeting expenses"}, {"accountType": "expense", "accountNamePattern": "professional development|training|certification", "suggestedName": "Professional Development", "budgetedAmount": 10000, "percentage": 4, "notes": "Certifications and skill development"}, {"accountType": "expense", "accountNamePattern": "technology|software|tools", "suggestedName": "Technology & Tools", "budgetedAmount": 8000, "percentage": 3.2, "notes": "Software and technology tools"}, {"accountType": "expense", "accountNamePattern": "professional services|legal|accounting", "suggestedName": "Professional Services", "budgetedAmount": 7500, "percentage": 3, "notes": "Legal and accounting services"}, {"accountType": "expense", "accountNamePattern": "insurance|liability", "suggestedName": "Professional Insurance", "budgetedAmount": 6000, "percentage": 2.4, "notes": "Professional liability insurance"}]}, {"id": "manufacturing-basic", "name": "Manufacturing Business", "description": "Budget template for manufacturing and production businesses", "category": "Manufacturing", "totalBudget": 800000, "lineItems": [{"accountType": "revenue", "accountNamePattern": "product sales|manufacturing sales|wholesale", "suggestedName": "Product Sales", "budgetedAmount": 800000, "percentage": 100, "notes": "Revenue from manufactured products"}, {"accountType": "expense", "accountNamePattern": "raw materials|materials|components", "suggestedName": "Raw Materials", "budgetedAmount": 320000, "percentage": 40, "notes": "Direct materials and components"}, {"accountType": "expense", "accountNamePattern": "direct labor|production labor", "suggestedName": "Direct Labor", "budgetedAmount": 160000, "percentage": 20, "notes": "Production worker wages"}, {"accountType": "expense", "accountNamePattern": "manufacturing overhead|factory overhead", "suggestedName": "Manufacturing Overhead", "budgetedAmount": 80000, "percentage": 10, "notes": "Factory utilities, maintenance, indirect costs"}, {"accountType": "expense", "accountNamePattern": "salaries|administrative|management", "suggestedName": "Administrative Salaries", "budgetedAmount": 64000, "percentage": 8, "notes": "Management and administrative staff"}, {"accountType": "expense", "accountNamePattern": "equipment|machinery|depreciation", "suggestedName": "Equipment & Depreciation", "budgetedAmount": 48000, "percentage": 6, "notes": "Equipment costs and depreciation"}, {"accountType": "expense", "accountNamePattern": "facility|rent|lease", "suggestedName": "Facility Costs", "budgetedAmount": 32000, "percentage": 4, "notes": "Factory and warehouse rental"}, {"accountType": "expense", "accountNamePattern": "quality control|testing|inspection", "suggestedName": "Quality Control", "budgetedAmount": 16000, "percentage": 2, "notes": "Quality assurance and testing"}, {"accountType": "expense", "accountNamePattern": "shipping|distribution|logistics", "suggestedName": "Distribution & Logistics", "budgetedAmount": 24000, "percentage": 3, "notes": "Product distribution and shipping"}]}]