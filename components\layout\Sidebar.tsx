"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  ChevronDown,
  ChevronRight,
  Home,
  FileText,
  List,
  Book,
  BarChart,
  Briefcase,
  Package,
  Users,
  Truck,
  Calculator,
  TrendingUp,
  Settings,
  CreditCard,
  User,
  Shield,
  Plus,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from '@/lib/auth-client';
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { QuickCreateDialog } from "@/components/general/QuickCreateDialog";
import { useState } from "react";

const navConfig = [
  {
    title: "FINANCE",
    items: [
      { href: "/dashboard", label: "Dashboard", icon: Home },
      { href: "/invoices", label: "Invoices", icon: FileText },
      { href: "/bills", label: "Bills", icon: FileText },
      { href: "/transactions", label: "Transactions", icon: List },
      { 
        label: "Accounting", 
        icon: Book, 
        expandable: true,
        subItems: [
          { href: "/accounting/chart-of-accounts", label: "Chart of Accounts", icon: List },
          { href: "/accounting/budget", label: "Budget Management", icon: Calculator },
        ]
      },
      { href: "/reporting", label: "Reporting", icon: BarChart },
    ],
  },
  {
    title: "OPERATIONS",
    items: [
      { href: "/projects", label: "Projects", icon: Briefcase },
      { href: "/products", label: "Products", icon: Package },
      { href: "/clients", label: "Clients", icon: Users },
      { href: "/vendors", label: "Vendors", icon: Truck },
    ],
  },
  {
    title: "SETTINGS",
    items: [
      { href: "/settings/billing", label: "Billing & Plans", icon: CreditCard },
      { href: "/settings/profile", label: "Profile", icon: User },
      { href: "/settings/organization", label: "Organization", icon: Settings },
      { href: "/settings/organization/roles", label: "Roles & Permissions", icon: Shield },
      { href: "/settings/audit", label: "Audit Log", icon: Shield },
    ],
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();
  // Type assertion is necessary because organizations and currentOrganizationId are not in the default user type
  const userWithOrgs = session?.user as any;
  const organizations = Array.isArray(userWithOrgs?.organizations) ? userWithOrgs.organizations : [];
  const currentOrganizationId = userWithOrgs?.currentOrganizationId;
  const currentOrg = organizations.find((org: any) => org.organizationId === currentOrganizationId);

  // Add state at the top of Sidebar
  const [quickCreateOpen, setQuickCreateOpen] = useState(false);

  return (
    <div className="hidden lg:flex lg:flex-col lg:w-64 lg:border-r lg:bg-gray-50 dark:lg:bg-gray-900/50 lg:max-h-screen lg:overflow-hidden">
      <div className="flex flex-col h-full">
        <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6 flex-shrink-0">
          <Link href="/" className="flex items-center gap-2 font-semibold text-lg">
            NextGenBusiness
          </Link>
        </div>

        {/* + New Button and Custom Dialog */}
        <div className="px-4 pt-4 pb-2">
          <Button
            variant="default"
            className="w-full flex items-center gap-2 justify-center"
            size="lg"
            onClick={() => setQuickCreateOpen(true)}
          >
            <Plus className="w-5 h-5" />
            New
          </Button>
          <QuickCreateDialog open={quickCreateOpen} onOpenChange={setQuickCreateOpen} />
        </div>

        <nav className="flex-1 px-4 py-4 space-y-4 overflow-y-auto pb-8">
          {navConfig.map((group) => (
            <Collapsible key={group.title} defaultOpen={true}>
              <CollapsibleTrigger className="w-full text-left">
                <div className="flex items-center justify-between w-full">
                  <span className="text-xs font-semibold tracking-wider text-gray-500 uppercase">
                    {group.title}
                  </span>
                  <ChevronDown className="w-4 h-4 transition-transform duration-200 [&[data-state=open]]:rotate-180" />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-2 space-y-1">
                {group.items.map((item) => {
                  const Icon = item.icon;
                  
                  // Handle expandable items (like Accounting)
                  if (item.expandable && item.subItems) {
                    const isAccountingActive = pathname.startsWith('/accounting');
                    return (
                      <Collapsible key={item.label} defaultOpen={isAccountingActive}>
                        <CollapsibleTrigger className="w-full">
                          <div className={cn(
                            "flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md",
                            isAccountingActive
                              ? "bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100"
                              : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                          )}>
                            <div className="flex items-center">
                              <Icon className="w-5 h-5 mr-3" />
                              {item.label}
                            </div>
                            <ChevronRight className="w-4 h-4 transition-transform duration-200 [&[data-state=open]]:rotate-90" />
                          </div>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="ml-6 mt-1 space-y-1">
                          {item.subItems.map((subItem) => {
                            const SubIcon = subItem.icon;
                            return (
                              <Link
                                key={subItem.href}
                                href={subItem.href}
                                className={cn(
                                  "flex items-center px-3 py-2 text-sm font-medium rounded-md",
                                  pathname === subItem.href
                                    ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                                    : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                                )}
                              >
                                <SubIcon className="w-4 h-4 mr-3" />
                                {subItem.label}
                              </Link>
                            );
                          })}
                        </CollapsibleContent>
                      </Collapsible>
                    );
                  }

                  // Handle regular items
                  return (
                    <Link
                      key={item.href}
                      href={item.href!}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm font-medium rounded-md",
                        pathname.startsWith(item.href!)
                          ? "bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100"
                          : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                      )}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      {item.label}
                    </Link>
                  );
                })}
              </CollapsibleContent>
            </Collapsible>
          ))}
        </nav>
      </div>
    </div>
  );
} 