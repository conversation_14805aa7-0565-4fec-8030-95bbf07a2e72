"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, AlertCircle } from "lucide-react";
import { getBudgetVsActualData } from "@/lib/actions/budget/get-budget-vs-actual-data";

interface BudgetVsActualLineItem {
  accountId: string;
  accountName: string;
  accountType: string;
  budgetedAmount: number;
  actualAmount: number;
  variance: number;
  variancePercent: number;
}

interface BudgetVsActualSummary {
  totalBudgetedRevenue: number;
  totalActualRevenue: number;
  revenueVariance: number;
  revenueVariancePercent: number;
  totalBudgetedExpenses: number;
  totalActualExpenses: number;
  expenseVariance: number;
  expenseVariancePercent: number;
  budgetedNetIncome: number;
  actualNetIncome: number;
  netIncomeVariance: number;
  netIncomeVariancePercent: number;
}

interface BudgetVsActualReportProps {
  budgetPeriodId: string;
}

export function BudgetVsActualReport({ budgetPeriodId }: BudgetVsActualReportProps) {
  const [data, setData] = useState<{
    lineItems: BudgetVsActualLineItem[];
    summary: BudgetVsActualSummary;
    period: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, [budgetPeriodId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const result = await getBudgetVsActualData(budgetPeriodId);
      setData(result);
    } catch (error) {
      console.error("Error loading budget vs actual data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getVarianceBadge = (variance: number, variancePercent: number) => {
    const isPositive = variance >= 0;
    const isSignificant = Math.abs(variancePercent) > 10;
    
    let color = "";
    let text = "";
    
    if (isSignificant) {
      color = isPositive 
        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      text = isPositive ? "Favorable" : "Unfavorable";
    } else {
      color = "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      text = "On Track";
    }

    return (
      <Badge className={color}>
        {text}
      </Badge>
    );
  };

  const getProgressValue = (budgeted: number, actual: number) => {
    if (budgeted === 0) return 0;
    return Math.min((actual / budgeted) * 100, 100);
  };

  const getProgressColor = (variance: number) => {
    if (Math.abs(variance) <= 10) return "bg-green-500";
    if (Math.abs(variance) <= 25) return "bg-yellow-500";
    return "bg-red-500";
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-gray-500 dark:text-gray-400">Loading budget vs actual data...</div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load budget vs actual data.
          </p>
        </CardContent>
      </Card>
    );
  }

  const revenueItems = data.lineItems.filter(item => item.accountType === "revenue");
  const expenseItems = data.lineItems.filter(item => item.accountType === "expense");

  return (
    <Card>
      <CardHeader>
        <CardTitle>Budget vs Actual Analysis</CardTitle>
        <CardDescription>
          Comprehensive analysis of budgeted vs actual performance for {data.period}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="text-sm text-gray-600 dark:text-gray-400">Revenue Performance</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {formatCurrency(data.summary.totalActualRevenue)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Budget: {formatCurrency(data.summary.totalBudgetedRevenue)}
            </div>
            <div className={`text-sm mt-1 ${
              data.summary.revenueVariance >= 0 ? "text-green-600" : "text-red-600"
            }`}>
              Variance: {formatCurrency(data.summary.revenueVariance)} 
              ({data.summary.revenueVariancePercent >= 0 ? "+" : ""}{data.summary.revenueVariancePercent.toFixed(1)}%)
            </div>
          </div>

          <div className="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="text-sm text-gray-600 dark:text-gray-400">Expense Performance</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {formatCurrency(data.summary.totalActualExpenses)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Budget: {formatCurrency(data.summary.totalBudgetedExpenses)}
            </div>
            <div className={`text-sm mt-1 ${
              data.summary.expenseVariance <= 0 ? "text-green-600" : "text-red-600"
            }`}>
              Variance: {formatCurrency(data.summary.expenseVariance)} 
              ({data.summary.expenseVariancePercent >= 0 ? "+" : ""}{data.summary.expenseVariancePercent.toFixed(1)}%)
            </div>
          </div>

          <div className="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="text-sm text-gray-600 dark:text-gray-400">Net Income</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
              {formatCurrency(data.summary.actualNetIncome)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Budget: {formatCurrency(data.summary.budgetedNetIncome)}
            </div>
            <div className={`text-sm mt-1 ${
              data.summary.netIncomeVariance >= 0 ? "text-green-600" : "text-red-600"
            }`}>
              Variance: {formatCurrency(data.summary.netIncomeVariance)} 
              ({data.summary.netIncomeVariancePercent >= 0 ? "+" : ""}{data.summary.netIncomeVariancePercent.toFixed(1)}%)
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 