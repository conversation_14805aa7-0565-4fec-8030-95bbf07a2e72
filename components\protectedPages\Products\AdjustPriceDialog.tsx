"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Product } from "./columns";

const adjustPriceSchema = z.object({
  price: z.number().positive("Price must be a positive number."),
});

type AdjustPriceFormData = z.infer<typeof adjustPriceSchema>;

interface AdjustPriceDialogProps {
  product: Product | null;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AdjustPriceFormData) => void;
  isPending: boolean;
}

export function AdjustPriceDialog({
  product,
  isOpen,
  onClose,
  onSubmit,
  isPending,
}: AdjustPriceDialogProps) {
  const form = useForm<AdjustPriceFormData>({
    resolver: zodResolver(adjustPriceSchema),
    defaultValues: {
      price: product?.price ? parseFloat(product.price) : 0,
    },
  });

  if (!product) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Adjust Price for {product.name}</DialogTitle>
          <DialogDescription>
            Update the price and see the potential impact on profitability.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>New Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Saving..." : "Save Price"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 