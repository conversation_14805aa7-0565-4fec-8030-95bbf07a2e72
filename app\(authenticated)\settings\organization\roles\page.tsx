import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { RolesClient } from "./client";

export default async function RolesPage() {
    return (
        <div>
            <PageHeader
                title="Roles & Permissions"
                description="Manage user roles and their permissions within the organization."
            />
            <div className="mt-8">
                <Card>
                    <CardHeader>
                        <CardTitle>Team Members</CardTitle>
                        <CardDescription>
                            Assign roles to team members to control their access to resources.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <RolesClient />
                    </CardContent>
                </Card>
            </div>
        </div>
    );
} 