'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Check } from 'lucide-react';

const plans = {
  starter: {
    name: 'Starter',
    description: 'Perfect for testing our accounting automation',
    monthlyPrice: 29,
    annualPrice: 290,
    userLimit: 1,
    transactionLimit: 50,
    featured: false,
    features: [
      'Automated transaction categorization',
      'Basic financial reporting',
      'Email support',
      'Bank account connections',
      'Invoice management'
    ],
    monthlySlug: 'starter-monthly',
    annualSlug: 'starter-annual'
  },
  professional: {
    name: 'Professional',
    description: 'Ideal for freelancers and consultants',
    monthlyPrice: 129,
    annualPrice: 1290,
    userLimit: 3,
    transactionLimit: 500,
    featured: true,
    features: [
      'Everything in Starter',
      'Project tracking & profitability',
      'Client collaboration portal', 
      'Advanced financial reporting',
      'Priority support',
      'Custom invoice branding'
    ],
    monthlySlug: 'professional-monthly',
    annualSlug: 'professional-annual'
  },
  business: {
    name: 'Business',
    description: 'For growing businesses with complex needs',
    monthlyPrice: 499,
    annualPrice: 4990,
    userLimit: 15,
    transactionLimit: 2500,
    featured: false,
    features: [
      'Everything in Professional',
      'Multi-location support',
      'Advanced analytics dashboard',
      'Team management & permissions',
      'API access',
      'Dedicated account manager'
    ],
    monthlySlug: 'business-monthly',
    annualSlug: 'business-annual'
  },
  unlimited: {
    name: 'Unlimited',
    description: 'Enterprise-grade with no limits',
    monthlyPrice: 999,
    annualPrice: 9990,
    userLimit: 'unlimited',
    transactionLimit: 'unlimited',
    featured: false,
    features: [
      'Everything in Business',
      'Unlimited users & transactions',
      'White-glove onboarding',
      'Custom development',
      '24/7 dedicated support',
      'SLA guarantee (99.9% uptime)'
    ],
    monthlySlug: 'unlimited-monthly',
    annualSlug: 'unlimited-annual'
  }
};

export default function Pricing() {
  const [isAnnual, setIsAnnual] = useState(false);
  const router = useRouter();

  const handleSignUp = () => {
    router.push('/register');
  };

  return (
    <section className="py-12 md:py-20" id="pricing">
      <div className="mx-auto max-w-6xl px-6">
        <div className="mx-auto max-w-2xl space-y-3 text-center">
          <h1 className="text-center text-4xl font-semibold lg:text-5xl">
            Simple, Transparent Pricing
          </h1>
          <p className="text-muted-foreground">
            Choose the plan that fits your business needs. Start with a 7-day free trial.
          </p>
        </div>
        
        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4 mt-6">
          <span className={`text-sm ${!isAnnual ? 'font-semibold' : 'text-muted-foreground'}`}>
            Monthly
          </span>
          <Switch
            checked={isAnnual}
            onCheckedChange={setIsAnnual}
          />
          <span className={`text-sm ${isAnnual ? 'font-semibold' : 'text-muted-foreground'}`}>
            Annual
          </span>
          {isAnnual && (
            <Badge variant="secondary" className="ml-2">
              Save up to 17%
            </Badge>
          )}
        </div>

        <div className="mt-8 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Object.entries(plans).map(([key, plan]) => (
            <Card key={key} className={`relative ${plan.featured ? 'border-primary ring-2 ring-primary' : ''}`}>
              {plan.featured && (
                <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary">
                  Most Popular
                </Badge>
              )}
              
              <CardHeader className="pb-4">
                <CardTitle className="font-medium">{plan.name}</CardTitle>
                
                <div className="my-2">
                  <span className="text-3xl font-bold">
                    ${isAnnual ? Math.floor(plan.annualPrice / 12) : plan.monthlyPrice}
                  </span>
                  <span className="text-muted-foreground">
                    {isAnnual ? '/mo billed annually' : '/mo'}
                  </span>
                </div>

                <CardDescription className="text-sm min-h-[32px]">
                  {plan.description}
                </CardDescription>
                
                <div className="pt-1 text-xs text-muted-foreground space-y-0">
                  <div>
                    {typeof plan.userLimit === 'number' 
                      ? `${plan.userLimit} user${plan.userLimit > 1 ? 's' : ''}`
                      : 'Unlimited users'
                    }
                  </div>
                  <div>
                    {typeof plan.transactionLimit === 'number'
                      ? `${plan.transactionLimit.toLocaleString('en-US')} transactions/month`
                      : 'Unlimited transactions'
                    }
                  </div>
                </div>

                <Button 
                  onClick={handleSignUp}
                  className={`mt-3 w-full ${plan.featured ? '' : 'variant-outline'}`}
                  variant={plan.featured ? 'default' : 'outline'}
                  size="sm"
                >
                  Start 7-Day Trial
                </Button>
              </CardHeader>

              <CardContent className="space-y-3 pt-0">
                <hr className="border-dashed" />

                <ul className="list-outside space-y-2 text-xs">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="size-3 mt-0.5 text-green-600 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="mt-6 text-center text-sm text-muted-foreground">
          <p>
            Create an account to select a plan. All plans start with a 7-day free trial.
          </p>
        </div>
      </div>
    </section>
  );
}
