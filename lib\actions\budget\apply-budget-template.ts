"use server";

import { db } from "@/db/drizzle";
import { budgetLineItems, accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, inArray } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import budgetTemplatesData from "@/lib/data/budget-templates.json";

type BudgetTemplate = (typeof budgetTemplatesData)[number];

export async function applyBudgetTemplate(budgetPeriodId: string, templateId: string, scaleFactor: number = 1) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  const template = budgetTemplatesData.find((t) => t.id === templateId);

  if (!template) {
    return { error: "Template not found" };
  }

  try {
    const accountNames = template.lineItems.map((item) => item.suggestedName);
    const existingAccounts = await db
      .select({ id: accounts.id, name: accounts.name })
      .from(accounts)
      .where(and(eq(accounts.organizationId, organizationId), inArray(accounts.name, accountNames)));

    const accountNameToIdMap = new Map(existingAccounts.map((a) => [a.name, a.id]));

    const itemsToInsert = template.lineItems
      .map((item) => {
        const accountId = accountNameToIdMap.get(item.suggestedName);
        if (!accountId) return null;
        return {
          budgetPeriodId,
          accountId,
          budgetedAmount: String(item.budgetedAmount * scaleFactor),
          notes: `From template: ${template.name}`,
        };
      })
      .filter((item): item is NonNullable<typeof item> => item !== null);

    if (itemsToInsert.length > 0) {
      await db.insert(budgetLineItems).values(itemsToInsert);
    }

    revalidatePath("/accounting/budget");
    return { success: true };
  } catch (error) {
    console.error("Error applying budget template:", error);
    return { error: "Failed to apply budget template" };
  }
} 