import { OrganizationSelector } from "./OrganizationSelector";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export default async function OrganizationsPage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    redirect("/signin");
  }

  // If user has no organizations, redirect to create flow
  if (!session.user.organizations || session.user.organizations.length === 0) {
    redirect("/create-organization");
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Welcome back, {session.user.name}!
          </h1>
          <p className="text-muted-foreground">
            Which organization would you like to access?
          </p>
        </div>
        
        <OrganizationSelector 
          organizations={session.user.organizations.map(org => ({
            ...org,
            role:
              org.role === "manager" || org.role === "editor"
                ? "admin"
                : org.role === "viewer"
                ? "member"
                : org.role,
          }))}
          currentUserId={session.user.id}
        />
      </div>
    </div>
  );
} 