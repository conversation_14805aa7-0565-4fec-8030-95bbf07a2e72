"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, Plus, Users, Calendar } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { authClient } from "@/lib/auth-client";

interface OrganizationData {
  organizationId: string;
  organizationName: string;
  role: "owner" | "admin" | "member" | "auditor";
  status: "pending" | "accepted" | "declined" | "expired" | "active" | "removed";
}

interface OrganizationSelectorProps {
  organizations: OrganizationData[];
  currentUserId: string;
}

export function OrganizationSelector({ organizations, currentUserId }: OrganizationSelectorProps) {
  const router = useRouter();

  const handleSelectOrganization = async (organizationId: string) => {
    try {
      // Switch active organization using Better Auth plugin
      const { error } = await authClient.organization.setActive({ organizationId });
      if (error) {
        toast.error("Failed to select organization");
        return;
      }
      // Navigate to organization dashboard
      router.push(`/dashboard`);
      toast.success("Organization selected successfully");
    } catch (error) {
      console.error("Error selecting organization:", error);
      toast.error("Failed to select organization");
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "owner":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "admin":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "member":
        return "bg-green-100 text-green-800 border-green-200";
      case "auditor":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "owner":
        return "👑";
      case "admin":
        return "⚙️";
      case "member":
        return "👤";
      case "auditor":
        return "📊";
      default:
        return "👤";
    }
  };

  return (
    <div className="space-y-6">
      {/* Organization Cards */}
      <div className="space-y-4">
        {organizations
          .filter(org => org.status === "active")
          .map((org) => (
            <Card
              key={org.organizationId}
              className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/30 bg-card text-foreground"
              onClick={() => handleSelectOrganization(org.organizationId)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg">
                      <Building2 className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">
                        {org.organizationName}
                      </CardTitle>
                      <CardDescription className="flex items-center space-x-2 mt-1 text-muted-foreground">
                        <span>{getRoleIcon(org.role)}</span>
                        <span className="capitalize">{org.role}</span>
                      </CardDescription>
                    </div>
                  </div>
                  <Badge
                    className={`${getRoleColor(org.role)} font-medium`}
                    variant="outline"
                  >
                    {org.role.toUpperCase()}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>Member</span>
                    </div>
                  </div>
                  <span className="text-primary font-medium">Access →</span>
                </div>
              </CardContent>
            </Card>
          ))}
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-6 border-t">
        <Button
          variant="outline"
          className="h-12 w-full"
          onClick={() => router.push("/create-organization")}
        >
          <Plus className="h-5 w-5 mr-2" />
          Create New Organization
        </Button>
        
        <Button
          variant="outline"
          className="h-12 w-full"
          onClick={() => router.push("/join-organization")}
        >
          <Users className="h-5 w-5 mr-2" />
          Join Organization
        </Button>
      </div>

      {/* Pending Invitations */}
      {organizations.some(org => org.status === "pending") && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Pending Invitations
          </h3>
          <div className="space-y-3">
            {organizations
              .filter(org => org.status === "pending")
              .map((org) => (
                <Card key={org.organizationId} className="border-amber-200 bg-amber-50 dark:bg-amber-900/20">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-amber-100 dark:bg-amber-900/40 rounded-lg">
                          <Calendar className="h-5 w-5 text-amber-600 dark:text-amber-300" />
                        </div>
                        <div>
                          <p className="font-medium text-foreground">
                            {org.organizationName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Invited as {org.role}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-amber-100 dark:bg-amber-900/40 text-amber-800 dark:text-amber-200 border-amber-200">
                        PENDING
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>
      )}
    </div>
  );
} 