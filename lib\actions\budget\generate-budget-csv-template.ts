"use server";

import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, or } from "drizzle-orm";

export async function generateBudgetCSVTemplate() {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    try {
        const relevantAccounts = await db
            .select({
                accountNumber: accounts.accountNumber,
                name: accounts.name
            })
            .from(accounts)
            .where(and(
                eq(accounts.organizationId, organizationId),
                or(
                    eq(accounts.type, 'revenue'),
                    eq(accounts.type, 'expense')
                ),
                eq(accounts.isHeader, false)
            ))
            .orderBy(accounts.accountNumber);

        let csvContent = "Account,BudgetedAmount,Notes\n";
        relevantAccounts.forEach(acc => {
            const identifier = acc.accountNumber || acc.name;
            csvContent += `"${identifier}",0.00,""\n`;
        });

        return { success: true, data: csvContent };

    } catch (error) {
        console.error("Error generating budget CSV template:", error);
        return { error: "Failed to generate CSV template" };
    }
} 