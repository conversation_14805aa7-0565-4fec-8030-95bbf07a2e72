export const coaTemplatesMetadata = [
  {
    id: 'saas',
    businessType: 'digital',
    industry: 'saas',
    templateName: 'SaaS Business Template',
    description: 'A chart of accounts template for SaaS businesses.',
    isDefault: true,
  },
  {
    id: 'ecommerce',
    businessType: 'digital',
    industry: 'ecommerce',
    templateName: 'E-commerce Business Template',
    description: 'A chart of accounts template for e-commerce businesses.',
    isDefault: false,
  },
  {
    id: 'professional-services',
    businessType: 'digital',
    industry: 'professional_services',
    templateName: 'Professional Services Template',
    description: 'For consultants, agencies, and other service-based businesses.',
    isDefault: false,
  },
  {
    id: 'retail',
    businessType: 'physical',
    industry: 'retail',
    templateName: 'Retail Business Template',
    description: 'A chart of accounts template for retail businesses.',
    isDefault: true,
  },
  {
    id: 'manufacturing',
    businessType: 'physical',
    industry: 'manufacturing',
    templateName: 'Manufacturing Template',
    description: 'A detailed COA for manufacturing and production businesses.',
    isDefault: false,
  },
  {
    id: 'hybrid',
    businessType: 'hybrid',
    industry: 'general',
    templateName: 'Hybrid Business Template',
    description: 'For businesses with both physical products and digital services.',
    isDefault: true,
  },
  {
    id: 'generic',
    businessType: 'other',
    industry: 'general',
    templateName: 'Generic Business Template',
    description: 'A basic chart of accounts for any business type.',
    isDefault: true,
  },
  {
    id: 'food-beverage',
    businessType: 'physical',
    industry: 'food_beverage',
    templateName: 'Food & Beverage Business Template',
    description: 'A chart of accounts template for restaurants, cafes, and food service businesses.',
    isDefault: false,
  },
  {
    id: 'healthcare',
    businessType: 'physical',
    industry: 'healthcare',
    templateName: 'Healthcare Business Template',
    description: 'A chart of accounts template for clinics, hospitals, and healthcare providers.',
    isDefault: false,
  },
  {
    id: 'marketing',
    businessType: 'digital',
    industry: 'marketing',
    templateName: 'Marketing Business Template',
    description: 'A chart of accounts template for marketing agencies and professionals.',
    isDefault: false,
  },
  {
    id: 'nonprofit',
    businessType: 'other',
    industry: 'non_profit',
    templateName: 'Non-profit Business Template',
    description: 'A chart of accounts template for non-profit organizations.',
    isDefault: false,
  },
  {
    id: 'finance',
    businessType: 'physical',
    industry: 'finance',
    templateName: 'Finance Business Template',
    description: 'A chart of accounts template for financial services and institutions.',
    isDefault: false,
  },
  {
    id: 'ultimate',
    businessType: 'all',
    industry: 'all',
    templateName: 'Ultimate Comprehensive Template',
    description: 'The most comprehensive, all-inclusive chart of accounts template.',
    isDefault: false,
  },
];

export async function getTemplateAccounts(templateId: string) {
  switch (templateId) {
    case 'saas':
      return (await import('./saas.json')).default;
    case 'retail':
      return (await import('./retail.json')).default;
    case 'ecommerce':
      return (await import('./ecommerce.json')).default;
    case 'hybrid':
      return (await import('./hybrid.json')).default;
    case 'professional-services':
      return (await import('./professional-services.json')).default;
    case 'manufacturing':
      return (await import('./manufacturing.json')).default;
    case 'generic':
      return (await import('./generic.json')).default;
    case 'ultimate':
      return (await import('./ultimate.json')).default;
    case 'food-beverage':
      return (await import('./food-beverage.json')).default;
    case 'healthcare':
      return (await import('./healthcare.json')).default;
    case 'marketing':
      return (await import('./marketing.json')).default;
    case 'nonprofit':
      return (await import('./nonprofit.json')).default;
    case 'finance':
      return (await import('./finance.json')).default;
    default:
      return null;
  }
} 