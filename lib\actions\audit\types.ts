export type AuditAction =
  | 'create' | 'update' | 'delete' | 'view' | 'export'
  | 'login' | 'logout' | 'invite' | 'accept_invite' | 'remove_member'
  | 'change_role' | 'grant_permission' | 'revoke_permission'
  | 'upload' | 'download' | 'send' | 'approve' | 'reject'
  | 'sync' | 'import' | 'backup' | 'restore';

export type ResourceType =
  | 'organization' | 'user' | 'member' | 'invitation'
  | 'transaction' | 'invoice' | 'bill' | 'client' | 'vendor'
  | 'product' | 'project' | 'account' | 'budget'
  | 'report' | 'setting' | 'integration' | 'backup';

export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

export interface AuditLogEntry {
  action: AuditAction;
  resourceType: ResourceType;
  resourceId?: string;
  description?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  changedFields?: string[];
  metadata?: Record<string, any>;
  riskLevel?: RiskLevel;
  flags?: string[];
}

export interface SecurityContext {
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
    timezone?: string;
  };
} 