import { NextRequest, NextResponse } from "next/server";
import { hasPermission } from "@/lib/permissions";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { permission } = body;

    if (!permission) {
      return NextResponse.json(
        { 
          allowed: false, 
          reason: "Permission name is required" 
        },
        { status: 400 }
      );
    }

    const result = await hasPermission(permission);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error checking permission:", error);
    return NextResponse.json(
      { 
        allowed: false, 
        reason: error instanceof Error ? error.message : "Unknown error" 
      },
      { status: 500 }
    );
  }
} 