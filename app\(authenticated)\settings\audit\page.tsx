import { getAuditLogs } from '@/lib/actions/audit/get-audit-logs';
import { getServerUserContext } from '@/lib/server-auth';
import { AuditLogClient } from './AuditLogClient';

export default async function AuditLogPage() {
  const { organization } = await getServerUserContext();
  const logs = await getAuditLogs({ organizationId: organization.id });

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Audit Log</h1>
        <p className="text-muted-foreground">
          Review all activities that have occurred within your organization.
        </p>
      </div>
      <AuditLogClient initialLogs={logs.filter(log => log.createdAt) as any} />
    </div>
  );
} 