"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { db } from "@/db/drizzle";
import { transactions } from "@/db/schema/schema";
import { eq, desc } from "drizzle-orm";

export async function getBusinessActionHistory(limit: number = 20) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const recentActions = await db.query.transactions.findMany({
      where: eq(transactions.organizationId, orgId),
      orderBy: [desc(transactions.date)],
      limit: limit,
      with: {
        journalEntries: {
          with: {
            account: true,
          },
        },
      },
    });
    
    return {
        success: true,
        history: recentActions,
    };

  } catch (error) {
    console.error("Error getting business action history:", error);
    return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
    };
  }
} 