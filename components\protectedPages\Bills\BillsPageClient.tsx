"use client";

import React, { useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import BillsDataTable from "@/components/protectedPages/Bills/bills-data-table";
import { Bill, columns as baseColumns } from "./columns";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { getBills } from "@/lib/actions/bills/get-bills";
import { deleteBills } from "@/lib/actions/bills/delete-bills";
import { updateBill } from "@/lib/actions/bills/update-bill";
import { createBill } from "@/lib/actions/bills/create-bill";
import { NewBill } from "@/lib/actions/bills/bill.schema";
import { getVendors } from "@/lib/actions/vendors/get-vendors";
import { getProducts } from "@/lib/actions/products/get-products";
import DeleteConfirmationDialog from "@/components/general/DeleteConfirmationDialog";
import { RowSelectionState } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import * as XLSX from "xlsx";
import jsPDF from "jspdf";
import { PlusCircle } from "lucide-react";
import { useState } from "react";
import AddBillSheet from "./AddBillSheet";

interface Vendor { id: string; name: string; }
interface Product { id: string; name: string; }

interface BillsPageClientProps {
  initialBills: Bill[];
  vendorList: Vendor[];
  productList: Product[];
}

// Server action to fetch bill line items
// async function fetchBillLineItemsServer(billId: string) {
//   'use server';
//   return getBillLineItems(billId);
// }

export default function BillsPageClient() {
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editingBillId, setEditingBillId] = React.useState<string | null>(null);
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [billsToDelete, setBillsToDelete] = React.useState<string[]>([]);

  const { data: bills = [], isLoading: isLoadingBills } = useQuery({
    queryKey: ["bills"],
    queryFn: getBills,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: vendors = [], isLoading: isLoadingVendors } = useQuery({
    queryKey: ["vendorsForBills"],
    queryFn: getVendors,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["productsForBills"],
    queryFn: getProducts,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { mutate: mutateCreateUpdate, isPending: isCreatingOrUpdating } = useMutation({
      mutationFn: (data: { id?: string; billData: NewBill }) =>
        data.id ? updateBill(data.id, data.billData) : createBill(data.billData),
      onSuccess: (result) => {
        if (result.success) {
          toast.success(result.message);
          queryClient.invalidateQueries({ queryKey: ["bills"] });
          setDialogOpen(false);
        }
      },
      onError: (error) => toast.error(error.message),
    }
  );

  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (ids: string[]) => deleteBills(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["bills"] });
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
      setDeleteDialogOpen(false);
      setBillsToDelete([]);
    },
    onError: (error) => {
      toast.error(error.message);
      setDeleteDialogOpen(false);
      setBillsToDelete([]);
    },
  });

  const selectedBillIds = useMemo(
    () =>
      Object.keys(rowSelection)
        .map((key) => bills[parseInt(key, 10)]?.id)
        .filter(Boolean),
    [rowSelection, bills]
  );

  const handleSubmit = (billData: NewBill) => {
    mutateCreateUpdate({ id: editingBillId ?? undefined, billData });
  };
  
  const openDeleteDialog = (ids: string[]) => {
    setBillsToDelete(ids);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    mutateDelete(billsToDelete);
  };

  const openEditDialog = (bill: Bill) => {
    setEditingBillId(bill.id);
    setDialogOpen(true);
  };

  const openNewDialog = () => {
    setEditingBillId(null);
    setDialogOpen(true);
  };

  const columns = useMemo((): typeof baseColumns => baseColumns.map((col: any) => {
    if (col.id === 'actions') {
      return {
        ...col,
        cell: ({ row }: { row: { original: Bill }}) => (
          <div className="text-right">
             <Button variant="ghost" size="sm" onClick={() => openEditDialog(row.original)}>Edit</Button>
             <Button variant="ghost" size="sm" onClick={() => openDeleteDialog([row.original.id])} disabled={isDeleting}>Delete</Button>
          </div>
        )
      };
    }
    return col;
  }), [isDeleting]);

  // Export handlers
  const handleExportCSV = () => {
    const rows = [
      ["Number", "Vendor", "Date", "Due", "Total", "Status"],
      ...bills.map(bill => [
        bill.billNumber,
        bill.vendorName,
        bill.date instanceof Date ? bill.date.toISOString().slice(0, 10) : bill.date,
        bill.dueDate instanceof Date ? bill.dueDate.toISOString().slice(0, 10) : bill.dueDate,
        bill.total,
        bill.status,
      ]),
    ];
    const csv = rows.map(r => r.join(",")).join("\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "bills.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success("Exported bills to CSV.");
  };

  const handleExportXLSX = () => {
    const ws = XLSX.utils.json_to_sheet(bills.map(bill => ({
      Number: bill.billNumber,
      Vendor: bill.vendorName,
      Date: bill.date instanceof Date ? bill.date.toISOString().slice(0, 10) : bill.date,
      Due: bill.dueDate instanceof Date ? bill.dueDate.toISOString().slice(0, 10) : bill.dueDate,
      Total: bill.total,
      Status: bill.status,
    })));
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Bills");
    XLSX.writeFile(wb, "bills.xlsx");
    toast.success("Exported bills to XLSX.");
  };

  const handleExportPDF = () => {
    const doc = new jsPDF();
    doc.text("Bills", 10, 10);
    let y = 20;
    doc.text("Number", 10, y);
    doc.text("Vendor", 40, y);
    doc.text("Date", 80, y);
    doc.text("Due", 110, y);
    doc.text("Total", 140, y);
    doc.text("Status", 170, y);
    y += 8;
    bills.forEach(bill => {
      doc.text(String(bill.billNumber), 10, y);
      doc.text(String(bill.vendorName), 40, y);
      doc.text(String(bill.date instanceof Date ? bill.date.toISOString().slice(0, 10) : bill.date), 80, y);
      doc.text(String(bill.dueDate instanceof Date ? bill.dueDate.toISOString().slice(0, 10) : bill.dueDate), 110, y);
      doc.text(String(bill.total), 140, y);
      doc.text(String(bill.status), 170, y);
      y += 8;
      if (y > 280) {
        doc.addPage();
        y = 20;
      }
    });
    doc.save("bills.pdf");
    toast.success("Exported bills to PDF.");
  };
  // Import handler
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    // const file = e.target.files?.[0];
    // if (!file) return;
    // const reader = new FileReader();
    // reader.onload = async (evt) => {
    //   try {
    //     let imported: any[] = [];
    //     if (file.name.endsWith(".csv")) {
    //       const text = evt.target?.result as string;
    //       const lines = text.split(/\r?\n/).filter(Boolean);
    //       const headers = lines[0].split(",");
    //       imported = lines.slice(1).map(line => {
    //         const values = line.split(",");
    //         const obj: any = {};
    //         headers.forEach((h, i) => { obj[h.trim()] = values[i]?.trim(); });
    //         return obj;
    //       });
    //     } else if (file.name.endsWith(".xlsx")) {
    //       const data = new Uint8Array(evt.target?.result as ArrayBuffer);
    //       const workbook = XLSX.read(data, { type: "array" });
    //       const sheet = workbook.Sheets[workbook.SheetNames[0]];
    //       imported = XLSX.utils.sheet_to_json(sheet);
    //     }
    //     // Call server action to import bills
    //     const result = await importBills(imported);
    //     if (result.success && result.count) {
    //       toast.success(`Imported ${result.count} bills.`);
    //       // TODO: Refetch bills from server for full accuracy
    //     } else {
    //       toast.error(result.message || "No valid bills to import.");
    //     }
    //   } catch (error) {
    //     toast.error("Failed to import file.");
    //   } finally {
    //     if(fileInputRef.current) fileInputRef.current.value = "";
    //   }
    // };
    // reader.readAsArrayBuffer(file);
    toast.info("Import functionality is being refactored.");
  };

  if (isLoadingBills || isLoadingVendors || isLoadingProducts) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-28" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
        <div className="flex justify-between items-center">
            <h1 className="text-2xl font-semibold">Bills</h1>
            <div className="flex gap-2">
                <Button onClick={() => fileInputRef.current?.click()}>Import</Button>
                <input type="file" ref={fileInputRef} onChange={handleImport} style={{ display: "none" }} accept=".csv, .xlsx" />
                <Button onClick={openNewDialog}>Add Bill</Button>
            </div>
        </div>

        {selectedBillIds.length > 0 && (
            <div className="flex justify-between items-center p-2 bg-muted rounded-md">
                <span>{selectedBillIds.length} selected</span>
                <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => openDeleteDialog(selectedBillIds)}
                    disabled={isDeleting}
                >
                    Delete Selected
                </Button>
            </div>
        )}

        <BillsDataTable
            columns={columns}
            data={bills}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
        />

        <AddBillSheet
            open={dialogOpen}
            onOpenChange={(open) => {
              if (!open) setEditingBillId(null);
              setDialogOpen(open);
            }}
            onSubmit={handleSubmit}
            isPending={isCreatingOrUpdating}
            billId={editingBillId}
        />

        <DeleteConfirmationDialog
            isOpen={deleteDialogOpen}
            onClose={() => setDeleteDialogOpen(false)}
            onConfirm={handleConfirmDelete}
            isPending={isDeleting}
            count={billsToDelete.length}
        />
    </div>
  );
} 