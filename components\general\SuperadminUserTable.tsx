"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { SuperadminUserOrgsModal } from "@/components/general/SuperadminUserOrgsModal";
import { SuperadminUserDetailsModal } from "@/components/general/SuperadminUserDetailsModal";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { format } from "date-fns";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { MoreVertical } from "lucide-react";
import { createOrganizationForUser } from "@/app/(authenticated)/supermegacoolpanel/create-organization-for-user.action";
import { useFormStatus } from "react-dom";
import { activateUserAction } from "@/app/(authenticated)/supermegacoolpanel/activate-user.action";
import { inactivateUserAction } from "@/app/(authenticated)/supermegacoolpanel/inactivate-user.action";
import { deleteUserAction } from "@/app/(authenticated)/supermegacoolpanel/delete-user.action";
import { resetUserPasswordAction } from "@/app/(authenticated)/supermegacoolpanel/reset-user-password.action";
import { impersonateUserAction } from "@/app/(authenticated)/supermegacoolpanel/impersonate-user.action";
import { sendSystemEmailAction } from "@/app/(authenticated)/supermegacoolpanel/send-system-email.action";

interface User {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
  lastActiveOrganizationId?: string;
}

interface SuperadminUserTableProps {
  initialUsers: User[];
}

export function SuperadminUserTable({ initialUsers }: SuperadminUserTableProps) {
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [orgsModalOpen, setOrgsModalOpen] = useState(false);
  const [orgsModalUser, setOrgsModalUser] = useState<{ id: string; name: string } | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [detailsModalUser, setDetailsModalUser] = useState<{ id: string; name: string } | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<null | {
    user: User;
    action: "activate" | "inactivate";
  }>(null);
  const [resetDialog, setResetDialog] = useState<null | { user: User }>(null);
  const [resetPassword, setResetPassword] = useState("");
  const [resetLoading, setResetLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState<null | { user: User }>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [emailDialog, setEmailDialog] = useState<null | { user: User }>(null);
  const [emailSubject, setEmailSubject] = useState("");
  const [emailBody, setEmailBody] = useState("");
  const [emailLoading, setEmailLoading] = useState(false);
  const [impersonateDialog, setImpersonateDialog] = useState<null | { user: User }>(null);
  const [impersonateLoading, setImpersonateLoading] = useState(false);
  const [impersonateToken, setImpersonateToken] = useState<string | null>(null);
  const [tab, setTab] = useState("users");
  // Audit log state
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [auditLoading, setAuditLoading] = useState(false);
  const [auditError, setAuditError] = useState<string | null>(null);
  const [auditFilters, setAuditFilters] = useState({ userId: "", action: "", riskLevel: "", startDate: "", endDate: "" });
  const [createOrgDialog, setCreateOrgDialog] = useState<null | { user: User }> (null);
  const [orgName, setOrgName] = useState("");
  const [orgEmail, setOrgEmail] = useState("");
  const [createOrgLoading, setCreateOrgLoading] = useState(false);

  async function fetchAuditLogs(filters = {}) {
    setAuditLoading(true);
    setAuditError(null);
    try {
      const res = await fetch("/api/supermegacoolpanel/get-audit-logs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...filters, limit: 50 }),
      });
      if (!res.ok) throw new Error("Failed to fetch audit logs");
      const data = await res.json();
      setAuditLogs(data);
    } catch (err: any) {
      setAuditError(err.message);
    } finally {
      setAuditLoading(false);
    }
  }

  useEffect(() => {
    if (tab === "audit") fetchAuditLogs(auditFilters);
    // No need to fetch users, they come from props
  }, [tab]);

  function handleAuditFilterChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    setAuditFilters((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  }

  function handleAuditFilterSubmit(e: React.FormEvent) {
    e.preventDefault();
    fetchAuditLogs(auditFilters);
  }

  const filteredUsers = users.filter(
    (u) =>
      u.name.toLowerCase().includes(search.toLowerCase()) ||
      u.email.toLowerCase().includes(search.toLowerCase())
  );

  async function handleActivateInactivate(user: User, action: "activate" | "inactivate") {
    setActionLoading(true);
    try {
      const formData = new FormData();
      formData.append("userId", user.id);
      const result = action === "activate"
        ? await activateUserAction(formData)
        : await inactivateUserAction(formData);
      if (result.success) {
        toast.success(`User ${action === "activate" ? "activated" : "inactivated"} successfully.`);
        setUsers((prev) =>
          prev.map((u) =>
            u.id === user.id ? { ...u, isActive: action === "activate" } : u
          )
        );
      } else {
        toast.error(result.message || `Failed to ${action} user.`);
      }
    } catch (err: any) {
      toast.error(err.message || `Failed to ${action} user.`);
    } finally {
      setActionLoading(false);
      setConfirmDialog(null);
    }
  }

  async function handleResetPassword(user: User) {
    setResetLoading(true);
    try {
      const formData = new FormData();
      formData.append("userId", user.id);
      formData.append("newPassword", resetPassword);
      const result = await resetUserPasswordAction(formData);
      if (result.success) {
        toast.success("Password reset successfully.");
        setResetDialog(null);
        setResetPassword("");
      } else {
        toast.error(result.message || "Failed to reset password.");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to reset password.");
    } finally {
      setResetLoading(false);
    }
  }

  async function handleDeleteUser(user: User) {
    setDeleteLoading(true);
    try {
      const formData = new FormData();
      formData.append("userId", user.id);
      const result = await deleteUserAction(formData);
      if (result.success) {
        toast.success("User deleted successfully.");
        setUsers((prev) => prev.filter((u) => u.id !== user.id));
      } else {
        toast.error(result.message || "Failed to delete user.");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to delete user.");
    } finally {
      setDeleteLoading(false);
      setDeleteDialog(null);
    }
  }

  async function handleSendEmail(user: User) {
    setEmailLoading(true);
    try {
      const formData = new FormData();
      formData.append("userId", user.id);
      formData.append("subject", emailSubject);
      formData.append("body", emailBody);
      const result = await sendSystemEmailAction(formData);
      if (result.success) {
        toast.success("Email sent successfully.");
        setEmailDialog(null);
        setEmailSubject("");
        setEmailBody("");
      } else {
        toast.error(result.message || "Failed to send email.");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to send email.");
    } finally {
      setEmailLoading(false);
    }
  }

  async function handleImpersonateUser(user: User) {
    setImpersonateLoading(true);
    setImpersonateToken(null);
    try {
      const formData = new FormData();
      formData.append("userId", user.id);
      const result = await impersonateUserAction(formData);
      if (result.success && result.token) {
        setImpersonateToken(result.token);
      } else {
        toast.error(result.message || "Failed to impersonate user.");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to impersonate user.");
    } finally {
      setImpersonateLoading(false);
    }
  }

  async function handleCreateOrganization(user: User) {
    setCreateOrgLoading(true);
    try {
      const result = await createOrganizationForUser(user.id, orgName, orgEmail);
      if (result.success) {
        toast.success("Organization created successfully.");
        setCreateOrgDialog(null);
        setOrgName("");
        setOrgEmail("");
      } else {
        toast.error(result.message || "Failed to create organization.");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to create organization.");
    } finally {
      setCreateOrgLoading(false);
    }
  }

  return (
    <Tabs value={tab} onValueChange={setTab} className="w-full">
      <TabsList className="mb-6">
        <TabsTrigger value="users">Users</TabsTrigger>
        <TabsTrigger value="audit">Audit Log</TabsTrigger>
      </TabsList>
      <TabsContent value="users">
        <div className="flex items-center mb-4">
          <input
            type="text"
            placeholder="Search users..."
            className="input input-bordered w-full max-w-xs mr-4"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        {loading ? (
          <div>Loading users...</div>
        ) : error ? (
          <div className="text-destructive">{error}</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <span className={user.isActive ? "text-green-600 font-semibold" : "text-red-600 font-semibold"}>
                      {user.isActive ? "Active" : "Inactive"}
                    </span>
                  </TableCell>
                  <TableCell>{format(new Date(user.createdAt), "M/d/yyyy")}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                          <MoreVertical className="h-5 w-5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setImpersonateDialog({ user })}>Impersonate</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setResetDialog({ user })}>Reset Password</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setConfirmDialog({ user, action: user.isActive ? "inactivate" : "activate" })}>
                          {user.isActive ? "Inactivate" : "Activate"}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => { setDetailsModalUser({ id: user.id, name: user.name }); setDetailsModalOpen(true); }}>Details</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => { setOrgsModalUser({ id: user.id, name: user.name }); setOrgsModalOpen(true); }}>Orgs</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setCreateOrgDialog({ user })}>Create Organization</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {/* open audit log for user */}}>Audit</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem variant="destructive" onClick={() => setDeleteDialog({ user })}>Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
        <SuperadminUserOrgsModal
          open={orgsModalOpen}
          onOpenChange={setOrgsModalOpen}
          userId={orgsModalUser?.id || ""}
          userName={orgsModalUser?.name || ""}
        />
        <SuperadminUserDetailsModal
          open={detailsModalOpen}
          onOpenChange={setDetailsModalOpen}
          userId={detailsModalUser?.id || ""}
          userName={detailsModalUser?.name || ""}
        />
        <Dialog open={!!confirmDialog} onOpenChange={(open) => !open && setConfirmDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {confirmDialog?.action === "activate" ? "Activate User" : "Inactivate User"}
              </DialogTitle>
              <DialogDescription>
                Are you sure you want to {confirmDialog?.action} user {confirmDialog?.user.name} ({confirmDialog?.user.email})?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setConfirmDialog(null)}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                variant={confirmDialog?.action === "activate" ? "default" : "destructive"}
                onClick={() => confirmDialog && handleActivateInactivate(confirmDialog.user, confirmDialog.action)}
                disabled={actionLoading}
              >
                {actionLoading ? "Processing..." : confirmDialog?.action === "activate" ? "Activate" : "Inactivate"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        <Dialog open={!!resetDialog} onOpenChange={(open) => !open && setResetDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reset Password</DialogTitle>
              <DialogDescription>
                Enter a new password for user {resetDialog?.user.name} ({resetDialog?.user.email}).
              </DialogDescription>
            </DialogHeader>
            <Input
              type="password"
              placeholder="New password (min 8 chars)"
              value={resetPassword}
              onChange={(e) => setResetPassword(e.target.value)}
              minLength={8}
              disabled={resetLoading}
            />
            <DialogFooter>
              <Button variant="outline" onClick={() => setResetDialog(null)} disabled={resetLoading}>
                Cancel
              </Button>
              <Button
                onClick={() => resetDialog && handleResetPassword(resetDialog.user)}
                disabled={resetLoading || resetPassword.length < 8}
              >
                {resetLoading ? "Resetting..." : "Reset Password"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        <Dialog open={!!deleteDialog} onOpenChange={(open) => !open && setDeleteDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete User</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete user {deleteDialog?.user.name} ({deleteDialog?.user.email})? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialog(null)} disabled={deleteLoading}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={() => deleteDialog && handleDeleteUser(deleteDialog.user)} disabled={deleteLoading}>
                {deleteLoading ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        <Dialog open={!!emailDialog} onOpenChange={(open) => !open && setEmailDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Send System Email</DialogTitle>
              <DialogDescription>
                Send an email to {emailDialog?.user.name} ({emailDialog?.user.email}).
              </DialogDescription>
            </DialogHeader>
            <Input
              type="text"
              placeholder="Subject"
              value={emailSubject}
              onChange={(e) => setEmailSubject(e.target.value)}
              disabled={emailLoading}
            />
            <textarea
              className="input input-bordered w-full min-h-[100px] mt-2"
              placeholder="Email body..."
              value={emailBody}
              onChange={(e) => setEmailBody(e.target.value)}
              disabled={emailLoading}
            />
            <DialogFooter>
              <Button variant="outline" onClick={() => setEmailDialog(null)} disabled={emailLoading}>
                Cancel
              </Button>
              <Button
                onClick={() => emailDialog && handleSendEmail(emailDialog.user)}
                disabled={emailLoading || !emailSubject || !emailBody}
              >
                {emailLoading ? "Sending..." : "Send Email"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        <Dialog open={!!impersonateDialog} onOpenChange={(open) => !open && setImpersonateDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Impersonate User</DialogTitle>
              <DialogDescription>
                You are about to impersonate {impersonateDialog?.user.name} ({impersonateDialog?.user.email}). This will generate a short-lived admin token. Use with caution.
              </DialogDescription>
            </DialogHeader>
            {impersonateToken ? (
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Admin JWT (valid 10 min):</div>
                <div className="bg-muted p-2 rounded break-all select-all text-xs border">
                  {impersonateToken}
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    navigator.clipboard.writeText(impersonateToken);
                    toast.success("Token copied to clipboard.");
                  }}
                >
                  Copy Token
                </Button>
                <div className="text-xs text-destructive mt-2">
                  This token is for superadmin use only. Do not share. Use it in a secure admin session or tool.
                </div>
              </div>
            ) : (
              <DialogFooter>
                <Button variant="outline" onClick={() => setImpersonateDialog(null)} disabled={impersonateLoading}>
                  Cancel
                </Button>
                <Button
                  onClick={() => impersonateDialog && handleImpersonateUser(impersonateDialog.user)}
                  disabled={impersonateLoading}
                >
                  {impersonateLoading ? "Impersonating..." : "Impersonate"}
                </Button>
              </DialogFooter>
            )}
          </DialogContent>
        </Dialog>
        <Dialog open={!!createOrgDialog} onOpenChange={(open) => !open && setCreateOrgDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Organization for User</DialogTitle>
              <DialogDescription>
                Enter organization details for {createOrgDialog?.user.name} ({createOrgDialog?.user.email}).
              </DialogDescription>
            </DialogHeader>
            <Input
              type="text"
              placeholder="Organization Name"
              value={orgName}
              onChange={(e) => setOrgName(e.target.value)}
              disabled={createOrgLoading}
            />
            <Input
              type="email"
              placeholder="Organization Email"
              value={orgEmail}
              onChange={(e) => setOrgEmail(e.target.value)}
              disabled={createOrgLoading}
            />
            <DialogFooter>
              <Button variant="outline" onClick={() => setCreateOrgDialog(null)} disabled={createOrgLoading}>
                Cancel
              </Button>
              <Button
                onClick={() => createOrgDialog && handleCreateOrganization(createOrgDialog.user)}
                disabled={createOrgLoading || !orgName || !orgEmail}
              >
                {createOrgLoading ? "Creating..." : "Create Organization"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </TabsContent>
      <TabsContent value="audit">
        <form className="flex flex-wrap gap-2 mb-4" onSubmit={handleAuditFilterSubmit}>
          <Input name="userId" value={auditFilters.userId} onChange={handleAuditFilterChange} placeholder="User ID" className="w-32" />
          <Input name="action" value={auditFilters.action} onChange={handleAuditFilterChange} placeholder="Action" className="w-32" />
          <Input name="riskLevel" value={auditFilters.riskLevel} onChange={handleAuditFilterChange} placeholder="Risk" className="w-24" />
          <Input name="startDate" type="date" value={auditFilters.startDate} onChange={handleAuditFilterChange} className="w-36" />
          <Input name="endDate" type="date" value={auditFilters.endDate} onChange={handleAuditFilterChange} className="w-36" />
          <Button type="submit" size="sm">Filter</Button>
        </form>
        {auditLoading ? (
          <div>Loading audit logs...</div>
        ) : auditError ? (
          <div className="text-destructive">{auditError}</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Time</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Resource</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Risk</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {auditLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>{log.createdAt ? format(new Date(log.createdAt), "yyyy-MM-dd HH:mm") : ""}</TableCell>
                  <TableCell>{log.userId}</TableCell>
                  <TableCell>{log.action}</TableCell>
                  <TableCell>{log.resourceType}{log.resourceId ? `:${log.resourceId}` : ""}</TableCell>
                  <TableCell>{log.description}</TableCell>
                  <TableCell>{log.riskLevel}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </TabsContent>
    </Tabs>
  );
} 