"use client";

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { VendorsDataTable } from "./vendors-data-table";
import {
  getVendor<PERSON>olumns,
  Vendor,
} from "@/components/protectedPages/Vendors/columns";
import AddVendorDialog from "./AddVendorDialog";
import { updateVendor } from "@/lib/actions/vendors/update-vendor";
import { createVendor } from "@/lib/actions/vendors/create-vendor";
import { getVendors } from "@/lib/actions/vendors/get-vendors";
import { deleteVendors } from "@/lib/actions/vendors/delete-vendors";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { RowSelectionState } from "@tanstack/react-table";
import { NewVendor } from "@/lib/actions/vendors/vendor.schema";
import DeleteConfirmationDialog from "@/components/general/DeleteConfirmationDialog";
import { Skeleton } from "@/components/ui/skeleton";

type ActionResponse = {
  success: boolean;
  message: string;
};

export default function VendorsPageClient() {
  const [editingVendor, setEditingVendor] = useState<Vendor | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [vendorsToDelete, setVendorsToDelete] = useState<string[]>([]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const queryClient = useQueryClient();

  const { data: vendors = [], isLoading: isLoadingVendors } = useQuery({
    queryKey: ["vendors"],
    queryFn: getVendors,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const mutation = useMutation<ActionResponse, Error, { vendorData: NewVendor; id?: string }>({
    mutationFn: (data: { vendorData: NewVendor; id?: string }) => {
      return data.id
        ? updateVendor(data.id, data.vendorData)
        : createVendor(data.vendorData);
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["vendors"] });
        setDialogOpen(false);
        setEditingVendor(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const deleteMutation = useMutation<ActionResponse, Error, string[]>({
    mutationFn: (ids: string[]) => deleteVendors(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["vendors"] });
        setDeleteDialogOpen(false);
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleSubmit = (vendorData: NewVendor, id?: string) => {
    mutation.mutate({ vendorData, id });
  };

  const openDeleteDialog = (ids: string[]) => {
    setVendorsToDelete(ids);
    setDeleteDialogOpen(true);
  };
  
  const selectedVendorIds = Object.keys(rowSelection).filter(
    (key) => rowSelection[key]
  );
  
  const handleConfirmDelete = () => {
    deleteMutation.mutate(vendorsToDelete);
  };

  const columns = useMemo(
    () =>
      getVendorColumns({
        onEdit: (vendor) => {
          setEditingVendor(vendor);
          setDialogOpen(true);
        },
        onDelete: (id) => openDeleteDialog([id]),
      }),
    []
  );

  if (isLoadingVendors) {
    return <Skeleton className="h-96 w-full" />;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-semibold">Vendors</h1>
        <Button onClick={() => setDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Vendor
        </Button>
      </div>

      <div className="flex mb-4">
        {selectedVendorIds.length > 0 && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => openDeleteDialog(selectedVendorIds)}
            disabled={deleteMutation.isPending}
          >
            Delete Selected ({selectedVendorIds.length})
          </Button>
        )}
      </div>

      <VendorsDataTable
        columns={columns}
        data={vendors}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
      <AddVendorDialog
        open={dialogOpen}
        onOpenChange={(open) => {
          if (!open) setEditingVendor(null);
          setDialogOpen(open);
        }}
        onSubmit={handleSubmit}
        isPending={mutation.isPending}
        vendorId={editingVendor?.id}
      />
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        isPending={deleteMutation.isPending}
        count={vendorsToDelete.length}
      />
    </div>
  );
} 