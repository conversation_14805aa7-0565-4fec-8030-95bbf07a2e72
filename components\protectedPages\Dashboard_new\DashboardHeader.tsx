import React from "react";
import DateRangeSelector from "./DateRangeSelector";

interface DateRange {
  from: Date;
  to: Date;
}

interface DashboardHeaderProps {
  onDateRangeChange?: (range: DateRange) => void;
}

const DashboardHeader = ({
  onDateRangeChange = () => {},
}: DashboardHeaderProps) => {
  return (
    <header className="w-full h-20 bg-black/50 border-b border-white/10 px-6 flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-white">
          Financial Dashboard
        </h1>
      </div>

      <div className="flex items-center gap-4">
        <DateRangeSelector onDateRangeChange={onDateRangeChange} />
      </div>
    </header>
  );
};

export default DashboardHeader;
