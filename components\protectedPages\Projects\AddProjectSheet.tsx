"use client";

import React, { useEffect, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetDescription } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { projectFormSchema, NewProject } from "@/lib/actions/projects/project.schema";
import { getProjectById } from "@/lib/actions/projects/get-project-by-id";
import { getClients } from "@/lib/actions/clients/get-clients";
import { <PERSON>older<PERSON><PERSON>, Flag, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

const STATUS_OPTIONS: NewProject["status"][] = [
  "not_started",
  "in_progress",
  "completed",
  "on_hold",
  "canceled",
];

interface Client {
  id: string;
  name: string;
}

interface AddProjectSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (projectData: NewProject) => void;
  isPending: boolean;
  projectId?: string | null;
}

export default function AddProjectSheet({
  open,
  onOpenChange,
  onSubmit,
  isPending,
  projectId,
}: AddProjectSheetProps) {
  const [isFetchingDetails, startFetching] = useTransition();

  // Fetch project details if editing
  const isEdit = !!projectId;
  const { data: project, isLoading: isLoadingProject } = useQuery({
    queryKey: ["project", projectId],
    queryFn: () => projectId ? getProjectById(projectId) : null,
    enabled: !!projectId && open,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  // Fetch clients
  const { data: clients = [], isLoading: isLoadingClients } = useQuery({
    queryKey: ["clients"],
    queryFn: getClients,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<NewProject>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      name: "",
      clientId: undefined,
      description: "",
      status: "not_started",
      startDate: undefined,
      endDate: undefined,
    },
  });

  useEffect(() => {
    if (open && project) {
      startFetching(async () => {
        reset({
          name: project.name,
          clientId: clients.find((c) => c.name === project.clientName)?.id ?? undefined,
          description: project.description ?? "",
          status: project.status,
          startDate: project.startDate ? new Date(project.startDate) : undefined,
          endDate: project.endDate ? new Date(project.endDate) : undefined,
        });
      });
    } else if (open && !project) {
      reset({
        name: "",
        clientId: undefined,
        description: "",
        status: "not_started",
        startDate: undefined,
        endDate: undefined,
      });
    }
  }, [project, open, reset, clients]);

  const formatDateForInput = (date: Date | undefined) => {
    if (!date) return "";
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, "0");
    const day = d.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "not_started":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
      case "in_progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "canceled":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const watchedStatus = watch("status");

  const handleSheetClose = (open: boolean) => {
    if (!isPending && !isFetchingDetails) {
      onOpenChange(open);
    }
  };

  return (
    <Sheet open={open} onOpenChange={handleSheetClose}>
      <SheetContent
        side="right"
        className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
        aria-describedby="project-form-description"
      >
        <SheetHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
          <SheetTitle className="text-lg sm:text-xl md:text-2xl flex items-center gap-2">
            <FolderOpen className="h-5 w-5 sm:h-6 sm:w-6" />
            {isEdit ? "Edit Project" : "Create New Project"}
          </SheetTitle>
          <SheetDescription id="project-form-description">
            {isEdit ? "Modify the project details below" : "Fill out the form below to create a new project"}
          </SheetDescription>
        </SheetHeader>

        {isFetchingDetails ? (
          <div className="flex items-center justify-center py-12 sm:py-16">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 sm:ml-3 text-sm">Loading project details...</span>
          </div>
        ) : (
          <div className="flex-1 overflow-y-auto">
            <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
              <div className="flex-1 px-4 pb-4 sm:px-6 sm:pb-6 space-y-4 sm:space-y-6 mt-4 sm:mt-6">
                
                {/* Project Information Card */}
                <Card>
                  <CardHeader className="pb-3 sm:pb-4">
                    <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                      <FolderOpen className="h-4 w-4 sm:h-5 sm:w-5" />
                      Project Information
                    </CardTitle>
                    <CardDescription className="text-xs sm:text-sm">
                      Basic project details and client assignment
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 sm:space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-xs sm:text-sm font-medium">
                          Project Name *
                        </Label>
                        <Input
                          id="name"
                          placeholder="e.g. Website Redesign"
                          className="text-xs sm:text-sm"
                          {...register("name")}
                        />
                        {errors.name && (
                          <p className="text-xs text-red-600">{errors.name.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="clientId" className="text-xs sm:text-sm font-medium">
                          Client
                        </Label>
                        <Select
                          onValueChange={(value) => setValue("clientId", value === "none" ? undefined : value)}
                          value={watch("clientId") || "none"}
                        >
                          <SelectTrigger id="clientId" className="text-xs sm:text-sm">
                            <SelectValue placeholder="Select a client" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">No client</SelectItem>
                            {clients.map((client) => (
                              <SelectItem key={client.id} value={client.id}>
                                {client.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.clientId && (
                          <p className="text-xs text-red-600">{errors.clientId.message}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-xs sm:text-sm font-medium">
                        Description
                      </Label>
                      <Textarea
                        id="description"
                        placeholder="Project details and requirements..."
                        className="text-xs sm:text-sm min-h-[80px] sm:min-h-[100px]"
                        {...register("description")}
                      />
                      {errors.description && (
                        <p className="text-xs text-red-600">{errors.description.message}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Project Status & Timeline Card */}
                <Card>
                  <CardHeader className="pb-3 sm:pb-4">
                    <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                      <Flag className="h-4 w-4 sm:h-5 sm:w-5" />
                      Status & Timeline
                    </CardTitle>
                    <CardDescription className="text-xs sm:text-sm">
                      Project status and important dates
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 sm:space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="status" className="text-xs sm:text-sm font-medium">
                          Status *
                        </Label>
                        <Select
                          onValueChange={(value) => setValue("status", value as NewProject["status"])}
                          value={watchedStatus}
                        >
                          <SelectTrigger id="status" className="text-xs sm:text-sm">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            {STATUS_OPTIONS.map((status) => (
                              <SelectItem key={status} value={status}>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant="secondary"
                                    className={`text-xs ${getStatusBadgeColor(status)}`}
                                  >
                                    {status.replace(/_/g, " ")}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.status && (
                          <p className="text-xs text-red-600">{errors.status.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="startDate" className="text-xs sm:text-sm font-medium">
                          Start Date
                        </Label>
                        <Input
                          id="startDate"
                          type="date"
                          className="text-xs sm:text-sm"
                          {...register("startDate", {
                            setValueAs: (value) => value ? new Date(value) : undefined,
                          })}
                          value={formatDateForInput(watch("startDate"))}
                        />
                        {errors.startDate && (
                          <p className="text-xs text-red-600">{errors.startDate.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="endDate" className="text-xs sm:text-sm font-medium">
                          End Date
                        </Label>
                        <Input
                          id="endDate"
                          type="date"
                          className="text-xs sm:text-sm"
                          {...register("endDate", {
                            setValueAs: (value) => value ? new Date(value) : undefined,
                          })}
                          value={formatDateForInput(watch("endDate"))}
                        />
                        {errors.endDate && (
                          <p className="text-xs text-red-600">{errors.endDate.message}</p>
                        )}
                      </div>
                    </div>

                    {/* Status Preview */}
                    {watchedStatus && (
                      <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                        <Flag className="h-4 w-4" />
                        <span className="text-xs sm:text-sm text-muted-foreground">Current Status:</span>
                        <Badge className={getStatusBadgeColor(watchedStatus)}>
                          {watchedStatus.replace(/_/g, " ")}
                        </Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Action Buttons */}
              <div className="border-t bg-muted/30 px-4 py-3 sm:px-6 sm:py-4">
                <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSheetClose(false)}
                    disabled={isPending || isFetchingDetails}
                    className="text-xs sm:text-sm"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    size="sm"
                    disabled={isPending || isFetchingDetails}
                    className="text-xs sm:text-sm"
                  >
                    {isPending || isFetchingDetails ? (
                      <>
                        <Loader2 className="mr-1 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                        {isEdit ? "Saving..." : "Creating..."}
                      </>
                    ) : (
                      <>{isEdit ? "Save Project" : "Create Project"}</>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
} 