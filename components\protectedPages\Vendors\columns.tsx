"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { vendors } from "@/db/schema/schema";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import * as React from "react";
import { Checkbox } from "@/components/ui/checkbox";

export type Vendor = typeof vendors.$inferSelect;

function formatAddress(address: any) {
  if (!address || typeof address !== 'object') return "-";
  const { street, city, zip, country } = address;
  return [street, city, zip, country].filter(Boolean).join(", ");
}

interface GetVendorColumnsProps {
  onEdit: (vendor: Vendor) => void;
  onDelete: (vendorId: string) => void;
}

export function getVendorColumns({
  onEdit,
  onDelete,
}: GetVendorColumnsProps): ColumnDef<Vendor>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "address",
      header: "Address",
      cell: ({ row }) => formatAddress(row.original.address),
    },
    {
      accessorKey: "category",
      header: "Category",
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => {
        const vendor = row.original;
        const [dropdownOpen, setDropdownOpen] = React.useState(false);
        
        return (
          <div className="text-right font-medium">
            <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem 
                  onSelect={(event) => {
                    event.preventDefault();
                    setDropdownOpen(false);
                    onEdit(vendor);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDelete(vendor.id)}>
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
}