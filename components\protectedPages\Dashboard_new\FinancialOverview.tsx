import { Card } from "@/components/ui/card";
import { IncomeExpensesChart } from "./charts/IncomeExpensesChart";
import {
  DollarSign,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";

interface FinancialOverviewProps {
  monthlyEarnings?: number;
  monthlyExpenses?: number;
  monthlyGrowth?: number;
  isLoading?: boolean;
  chartData?: {
    month: string;
    income: number;
    expenses: number;
  }[];
}

const FinancialOverview = ({
  monthlyEarnings = 12500,
  monthlyExpenses = 4800,
  monthlyGrowth = 15.6,
  isLoading = false,
  chartData = [
    { month: "Jan", income: 5000, expenses: 3000 },
    { month: "Feb", income: 6000, expenses: 3500 },
    { month: "Mar", income: 4500, expenses: 4000 },
    { month: "Apr", income: 7000, expenses: 3800 },
    { month: "May", income: 8000, expenses: 4200 },
    { month: "Jun", income: 7500, expenses: 4500 },
  ],
}: FinancialOverviewProps) => {
  const isPositiveGrowth = monthlyGrowth >= 0;

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
        <Card className="p-6 bg-black/50 border-white/10 hover:bg-black/60 transition-colors">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-gray-400">Monthly Earnings</p>
              {isLoading ? (
                <Skeleton className="h-8 w-32" />
              ) : (
                <h3 className="text-2xl font-bold text-white">
                  ${monthlyEarnings.toLocaleString()}
                </h3>
              )}
            </div>
            <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-green-500" />
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-black/50 border-white/10 hover:bg-black/60 transition-colors">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-gray-400">Monthly Expenses</p>
              <h3 className="text-2xl font-bold text-white">
                ${monthlyExpenses.toLocaleString()}
              </h3>
            </div>
            <div className="h-12 w-12 rounded-full bg-red-500/10 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-red-500" />
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-black/50 border-white/10 hover:bg-black/60 transition-colors">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-gray-400">Monthly Growth</p>
              <div className="flex items-center gap-2">
                <h3 className="text-2xl font-bold text-white">
                  {monthlyGrowth === 0
                    ? "0"
                    : `${Math.abs(monthlyGrowth).toFixed(1)}%`}
                </h3>
                {monthlyGrowth !== 0 && (
                  <ArrowUpRight
                    className={`h-5 w-5 transform transition-transform ${isPositiveGrowth ? "text-green-500" : "text-red-500 rotate-90"}`}
                  />
                )}
              </div>
            </div>
            <div
              className={`h-12 w-12 rounded-full flex items-center justify-center ${
                monthlyGrowth === 0
                  ? "bg-gray-500/10"
                  : isPositiveGrowth
                    ? "bg-green-500/10"
                    : "bg-red-500/10"
              }`}
            >
              <TrendingUp
                className={`h-6 w-6 ${
                  monthlyGrowth === 0
                    ? "text-gray-500"
                    : isPositiveGrowth
                      ? "text-green-500"
                      : "text-red-500"
                }`}
              />
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6 bg-black/50 border-white/10 mb-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          Income vs Expenses
        </h3>
        <div className="h-[300px]">
          <IncomeExpensesChart data={chartData} />
        </div>
      </Card>
    </div>
  );
};

export default FinancialOverview;
