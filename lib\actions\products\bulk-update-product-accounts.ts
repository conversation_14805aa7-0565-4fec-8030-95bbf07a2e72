"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";
import { revalidatePath } from "next/cache";

interface AccountRemapping {
  revenueAccountId?: string;
  inventoryAccountId?: string;
}

export async function bulkUpdateProductAccounts(
  productIds: string[],
  accountsToRemap: AccountRemapping
) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    if (productIds.length === 0) {
      return { success: false, message: "No products selected." };
    }

    const updateData: Partial<typeof products.$inferInsert> = {};
    if (accountsToRemap.revenueAccountId) {
      updateData.revenueAccountId = accountsToRemap.revenueAccountId;
    }
    if (accountsToRemap.inventoryAccountId) {
      updateData.inventoryAccountId = accountsToRemap.inventoryAccountId;
    }
    
    if (Object.keys(updateData).length === 0) {
        return { success: false, message: "No accounts selected for remapping." };
    }

    await db
      .update(products)
      .set(updateData)
      .where(
        and(
          eq(products.organizationId, orgId),
          inArray(products.id, productIds)
        )
      );

    revalidatePath("/products");

    return {
      success: true,
      message: `Successfully remapped accounts for ${productIds.length} products.`,
    };
  } catch (error) {
    console.error("Failed to bulk remap product accounts:", error);
    return {
      success: false,
      message: "An error occurred while remapping accounts.",
    };
  }
} 