"use server";

import { auth } from "@/lib/auth";
import { getServerUserContext } from "@/lib/server-auth";

export async function hardDeleteOrganization() {
  const { organization } = await getServerUserContext();
  if (!organization.id) {
    return { success: false, message: "No organization found." };
  }
  try {
    await (auth.api as any).organization.delete({ organizationId: organization.id });
    return { success: true, message: "Organization permanently deleted." };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
} 