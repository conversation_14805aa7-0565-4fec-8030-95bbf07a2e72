"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";

export type Bill = {
  id: string;
  billNumber: string;
  vendorName: string | null;
  vendorId: string;
  date: Date;
  dueDate: Date | null;
  total: string; // decimal as string
  status: "draft" | "submitted" | "approved" | "paid" | "void";
  overpaid: string;
};

export const columns: ColumnDef<Bill>[] = [
  { accessorKey: "billNumber", header: "Number", enableSorting: true },
  { accessorKey: "vendorName", header: "Vendor", enableSorting: true },
  { 
    accessorKey: "date", 
    header: "Date",
    enableSorting: true,
    cell: ({ row }) => new Date(row.getValue("date")).toLocaleDateString(),
  },
  { 
    accessorKey: "dueDate", 
    header: "Due",
    enableSorting: true,
    cell: ({ row }) => {
        const dueDate = row.getValue("dueDate");
        return dueDate ? new Date(dueDate as string).toLocaleDateString() : "N/A";
    }
  },
  {
    accessorKey: "total",
    header: "Total",
    enableSorting: true,
    cell: ({ row }) => new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(parseFloat(row.getValue("total")))
  },
  {
    accessorKey: "amountPaid",
    header: "Paid",
    cell: ({ row }) => new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(Number(row.getValue("amountPaid"))),
  },
  {
    accessorKey: "amountRemaining",
    header: "Remaining",
    cell: ({ row }) => new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(Number(row.getValue("amountRemaining"))),
  },
  {
    accessorKey: "overpaid",
    header: "Overpaid",
    cell: ({ row }) => {
      const value = Number(row.getValue("overpaid"));
      if (value > 0) {
        return (
          <div className="flex flex-col gap-1">
            <span className="text-green-600 font-semibold">{new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: "USD",
            }).format(value)}</span>
            <Badge className="bg-green-100 text-green-800 border-green-300">Supplier Credit</Badge>
            <button
              className="mt-1 px-2 py-1 text-xs rounded border border-green-400 text-green-700 hover:bg-green-50"
              onClick={() => {/* TODO: Implement apply/refund supplier credit */}}
            >
              Apply/Refund Credit
            </button>
          </div>
        );
      }
      return "-";
    },
  },
  { 
    accessorKey: "status", 
    header: "Status",
    enableSorting: true,
    cell: ({ row }) => <Badge>{row.getValue("status")}</Badge>
  },
  // TODO: Actions column (edit/delete)
];

// Global filter for searching bills
export function billGlobalFilterFn<RowData extends Bill>(
  row: any,
  columnId: string,
  filterValue: string
): boolean {
  const { billNumber, vendorName, status } = row.original;
  const search = filterValue.toLowerCase();
  return (
    (billNumber && billNumber.toLowerCase().includes(search)) ||
    (vendorName && vendorName.toLowerCase().includes(search)) ||
    (status && status.toLowerCase().includes(search))
  ) ? true : false;
}
