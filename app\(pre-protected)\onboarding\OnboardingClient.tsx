"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { updateOnboardingStep } from "@/lib/actions/onboarding";
import { OnboardingProgress, ONBOARDING_STEPS, getNextStep, getPreviousStep, getStepName } from "@/lib/onboarding-constants";
import { ServerUserContext } from "@/lib/server-auth";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { completeOrgOnboarding } from "@/lib/actions/onboarding/completeOrgOnboarding";

import { BusinessTypeStep } from "@/app/(pre-protected)/onboarding/steps/BusinessTypeStep";
import { ChartOfAccountsStep } from "@/app/(pre-protected)/onboarding/steps/ChartOfAccountsStep";
import { CompletionStep } from "@/app/(pre-protected)/onboarding/steps/CompletionStep";
import { OrganizationDetailsStep } from "@/app/(pre-protected)/onboarding/steps/OrganizationDetailsStep";
import { SubscriptionStep } from "@/app/(pre-protected)/onboarding/steps/SubscriptionStep";
import dynamic from "next/dynamic";
const LegalConsentStep = dynamic(() => import("@/app/(pre-protected)/onboarding/steps/LegalConsentStep").then(mod => mod.LegalConsentStep), { ssr: false });
import { WelcomeStep } from "@/app/(pre-protected)/onboarding/steps/WelcomeStep";
import { OrganizationBasicsStep } from "@/app/(pre-protected)/onboarding/steps/OrganizationBasicsStep";

interface OnboardingClientProps {
  context: ServerUserContext;
  progress: OnboardingProgress;
}

export function OnboardingClient({ 
  context, 
  progress: initialProgress 
}: OnboardingClientProps) {
  const router = useRouter();
  const [progress, setProgress] = useState<OnboardingProgress>(initialProgress);
  const [isLoading, setIsLoading] = useState(false);
  const { data: onboardingData, updateData } = useOnboardingStore();

  // Save userId in onboarding store for fallback
  useEffect(() => {
    if (context.user?.id) {
      updateData({ userId: context.user.id });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Ensure dark theme is applied
  useEffect(() => {
    document.documentElement.classList.add('dark');
    document.documentElement.classList.remove('light');
  }, []);

  // Automatically skip Organization Basics if org already exists
  useEffect(() => {
    if (
      progress.step === ONBOARDING_STEPS.ORGANIZATION_BASICS &&
      (context.organization?.id && context.organization.id !== '')
    ) {
      // If org exists, skip to next step
      handleNextStep();
    }
  }, [progress.step, context.organization?.id]);

  const progressPercentage = (progress.step / progress.totalSteps) * 100;

  const handleNextStep = async (stepData?: Record<string, any>) => {
    setIsLoading(true);
    const nextStepNumber = getNextStep(progress.step);
    if (!nextStepNumber) {
      setIsLoading(false);
      return;
    }

    try {
      const updatedProgress = await updateOnboardingStep(
        context.user.id,
        nextStepNumber,
        stepData
      );

      setProgress(updatedProgress);

      if (updatedProgress.step === ONBOARDING_STEPS.COMPLETION) {
        console.log('Onboarding completed. Submitting final data:', context.organization);
        // Ensure organization is created and marked as completed
        // await completeOrgOnboardingAndPoll();
      }
    } catch (error) {
      console.error("Failed to update onboarding step:", error);
      // Optionally, show an error to the user
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreviousStep = () => {
    const prevStepNumber = getPreviousStep(progress.step);
    if (prevStepNumber) {
      setProgress({ ...progress, step: prevStepNumber });
    }
  };

  // Add a method to navigate to a specific step
  const navigateToStep = async (targetStep: number) => {
    setIsLoading(true);
    try {
      const updatedProgress = await updateOnboardingStep(
        context.user.id,
        targetStep,
        {}
      );
      setProgress(updatedProgress);
    } catch (error) {
      console.error("Failed to navigate to step:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    switch (progress.step) {
      case ONBOARDING_STEPS.WELCOME:
        return <WelcomeStep onNext={() => handleNextStep({})} isLoading={isLoading} />;
      case ONBOARDING_STEPS.ORGANIZATION_BASICS:
        // If org exists, show a loading spinner while skipping
        if (context.organization?.id && context.organization.id !== '') {
          return <div className="flex justify-center items-center min-h-[200px]"><span className="text-gray-300">Loading...</span></div>;
        }
        return <OrganizationBasicsStep 
          isLoading={isLoading}
          userId={context.user.id}
          onNext={() => {
            handleNextStep();
          }}
        />;
      case ONBOARDING_STEPS.ORGANIZATION_DETAILS:
        const orgDetails = onboardingData?.orgDetails || {};
        const organizationData = onboardingData?.organization || {};
        const orgDetailsName = (orgDetails as any)?.name || (organizationData as any)?.name;
        const orgDetailsSlug = (orgDetails as any)?.slug || (organizationData as any)?.slug;
        const orgId = context.organization?.id;
        
        if (!orgId) {
          return (
            <div className="flex flex-col items-center justify-center min-h-[200px]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-200 mb-2" />
              <span className="text-gray-300">Setting up your organization...</span>
            </div>
          );
        }
        
        const orgBusinessType = context.organization?.businessType || undefined;
        return <OrganizationDetailsStep 
          orgId={orgId}
          userId={context.user.id}
          businessType={orgBusinessType}
          orgName={orgDetailsName}
          orgSlug={orgDetailsSlug}
          onNext={(data) => {
            handleNextStep({ orgDetails: data });
          }} 
          onPrevious={handlePreviousStep} 
          isLoading={isLoading} 
        />;
      case ONBOARDING_STEPS.BUSINESS_TYPE:
        return <BusinessTypeStep 
          userId={context.user.id}
          onNext={(data) => {
            handleNextStep({ businessTypeData: data });
          }} 
          onPrevious={handlePreviousStep} 
          isLoading={isLoading} 
        />;
      case ONBOARDING_STEPS.CHART_OF_ACCOUNTS:
        const businessType = (
          onboardingData.businessType === 'physical' ||
          onboardingData.businessType === 'digital' ||
          onboardingData.businessType === 'hybrid'
        ) ? onboardingData.businessType : undefined;
        const industry = typeof onboardingData.orgDetails === 'object' && onboardingData.orgDetails !== null && 'industry' in onboardingData.orgDetails
          ? (onboardingData.orgDetails.industry as string | undefined)
          : undefined;
        return <ChartOfAccountsStep
          businessType={businessType}
          industry={industry}
          onNext={(data) => {
            handleNextStep({ accountsData: data });
          }}
          onPrevious={handlePreviousStep}
          isLoading={isLoading}
        />;
      case ONBOARDING_STEPS.SUBSCRIPTION:
        return <SubscriptionStep
          onNext={(data) => {
            handleNextStep({ subscriptionData: data });
          }}
          onPrevious={handlePreviousStep}
          isLoading={isLoading}
        />;
      case ONBOARDING_STEPS.LEGAL_CONSENT:
        return <LegalConsentStep
          userId={context.user.id}
          onNext={(data) => {
            handleNextStep({ legalConsent: true, legalConsentAcceptedAt: data.legalConsentAcceptedAt });
          }}
          onPrevious={handlePreviousStep}
          isLoading={isLoading}
        />;
      case ONBOARDING_STEPS.COMPLETION:
        return <CompletionStep 
          onboardingData={onboardingData}
          onNext={async (data) => {
            setIsLoading(true);
            try {
              // Merge all relevant onboarding data for backend
              const mergedData = {
                ...onboardingData,
                organization: {
                  ...onboardingData.organization,
                  ...(onboardingData.orgDetails || {}),
                },
                businessDetails: onboardingData.businessDetails,
                chartOfAccounts: onboardingData.chartOfAccounts,
                subscription: onboardingData.subscription,
                legalConsentAcceptedAt: onboardingData.legalConsentAcceptedAt,
              };
              
              const result = await completeOrgOnboarding(context.user.id, mergedData);
              if (!result.success) {
                console.error("Failed to complete onboarding:", result.error);
                // Could add user-facing error message here
                return;
              }
              
              // Mark onboarding as completed in the DB
              await updateOnboardingStep(
                context.user.id,
                ONBOARDING_STEPS.COMPLETION,
                { isCompleted: true, completedAt: new Date().toISOString() }
              );
              
              router.push("/dashboard");
            } catch (error) {
              console.error("Failed to complete organization setup:", error);
              // Could add user-facing error message here
            } finally {
              setIsLoading(false);
            }
          }} 
          isLoading={isLoading} 
        />;
      default:
        return <div className="text-white">Invalid step</div>;
    }
  };

  return (
    <ThemeProvider attribute="class" defaultTheme="dark" forcedTheme="dark">
      <div className="min-h-screen bg-gray-900 dark:bg-gray-900">
        <div className="border-b border-gray-700 bg-gray-800">
          <div className="max-w-6xl mx-auto px-4 py-2 sm:py-3">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h1 className="text-lg sm:text-xl font-bold text-white">Setup Your Business</h1>
                <p className="text-xs sm:text-sm text-gray-300 truncate">Let's get your accounting system ready</p>
              </div>
              <div className="flex items-center space-x-3 sm:space-x-4 flex-shrink-0">
                <div className="text-right">
                  <div className="text-xs sm:text-sm text-gray-300">{getStepName(progress.step)}</div>
                  <div className="text-xs text-gray-400">{Math.round(progressPercentage)}% complete</div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Badge 
                      variant="secondary" 
                      className="bg-gray-700 text-gray-200 border-gray-600 text-xs px-2 py-1 cursor-pointer hover:bg-gray-600 flex items-center"
                    >
                      {progress.step}/{progress.totalSteps}
                      <ChevronDown className="ml-1 w-3 h-3" />
                    </Badge>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
                    {[...Array(progress.totalSteps)].map((_, index) => {
                      const stepNumber = index + 1;
                      return (
                        <DropdownMenuItem 
                          key={stepNumber}
                          onSelect={() => navigateToStep(stepNumber)}
                          className={`
                            cursor-pointer 
                            ${stepNumber === progress.step 
                              ? 'bg-blue-900/50 text-blue-200' 
                              : 'text-gray-300 hover:bg-gray-700'}
                          `}
                        >
                          {getStepName(stepNumber)} {stepNumber === progress.step && '(Current)'}
                        </DropdownMenuItem>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <div className="mt-2">
              <Progress value={progressPercentage} className="h-1.5 sm:h-2 bg-gray-700" />
            </div>
          </div>
        </div>
        <main className="max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
          <Card className="bg-gray-800/60 border-gray-700 shadow-lg">
            <CardContent className="p-6 sm:p-8">
              {renderCurrentStep()}
            </CardContent>
          </Card>
        </main>
      </div>
    </ThemeProvider>
  );
} 