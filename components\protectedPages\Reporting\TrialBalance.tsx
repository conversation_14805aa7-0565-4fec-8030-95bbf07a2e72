"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle } from "lucide-react";

interface TrialBalanceAccount {
  account: {
    id: string;
    name: string;
    account_number: string;
  };
  debit_total: number;
  credit_total: number;
}

interface TrialBalanceData {
  accounts: TrialBalanceAccount[];
  totalDebits: number;
  totalCredits: number;
}

interface TrialBalanceProps {
  data: TrialBalanceData;
  loading?: boolean;
  fromDate?: string;
  toDate?: string;
  onDateRangeChange?: (from: string, to: string) => void;
}

export default function TrialBalance({ data, loading = false, fromDate, toDate, onDateRangeChange }: TrialBalanceProps) {
  const [from, setFrom] = useState(fromDate || "");
  const [to, setTo] = useState(toDate || "");

  const handleDateChange = (type: "from" | "to", value: string) => {
    if (type === "from") setFrom(value);
    else setTo(value);
    if (onDateRangeChange) {
      onDateRangeChange(type === "from" ? value : from, type === "to" ? value : to);
    }
  };

  const handleExportCSV = () => {
    // TODO: Implement CSV export
    alert("Export to CSV coming soon!");
  };
  const handleExportPDF = () => {
    // TODO: Implement PDF export
    alert("Export to PDF coming soon!");
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (data.accounts.length === 0) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-white">Trial Balance</h2>
        <div className="text-center py-8 bg-black/50 rounded-lg border border-white/10">
          <div className="max-w-md mx-auto space-y-4 text-gray-300">
            <p className="text-lg">No accounts found</p>
            <p>To get started with your trial balance:</p>
            <ul className="list-disc list-inside text-left space-y-2">
              <li>Set up your Chart of Accounts</li>
              <li>Add your initial account balances</li>
              <li>Record your transactions</li>
            </ul>
                          <Button asChild className="mt-4">
                <Link href="/accounting/chart-of-accounts">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Set Up Chart of Accounts
                </Link>
              </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white mb-2">Trial Balance</h2>
          <div className="flex gap-2 items-center">
            <label className="text-gray-300">From:</label>
            <input
              type="date"
              className="rounded border px-2 py-1 bg-black/30 text-white"
              value={from}
              onChange={e => handleDateChange("from", e.target.value)}
            />
            <label className="text-gray-300 ml-2">To:</label>
            <input
              type="date"
              className="rounded border px-2 py-1 bg-black/30 text-white"
              value={to}
              onChange={e => handleDateChange("to", e.target.value)}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportCSV}>Export CSV</Button>
          <Button variant="outline" onClick={handleExportPDF}>Export PDF</Button>
        </div>
      </div>

      <Table className="text-gray-300">
        <TableHeader>
          <TableRow>
            <TableHead>Account</TableHead>
            <TableHead>Account Number</TableHead>
            <TableHead className="text-right">Debit</TableHead>
            <TableHead className="text-right">Credit</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.accounts.map((ab) => (
            <TableRow key={ab.account.id}>
              <TableCell>{ab.account.name}</TableCell>
              <TableCell>{ab.account.account_number}</TableCell>
              <TableCell className="text-right">
                {ab.debit_total > 0 ? `$${ab.debit_total.toFixed(2)}` : "-"}
              </TableCell>
              <TableCell className="text-right">
                {ab.credit_total > 0 ? `$${ab.credit_total.toFixed(2)}` : "-"}
              </TableCell>
            </TableRow>
          ))}
          <TableRow className="font-bold text-white">
            <TableCell>Total</TableCell>
            <TableCell></TableCell>
            <TableCell className="text-right">
              ${data.totalDebits.toFixed(2)}
            </TableCell>
            <TableCell className="text-right">
              ${data.totalCredits.toFixed(2)}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      {data.totalDebits !== data.totalCredits && (
        <div className="text-red-500 text-sm mt-2">
          Warning: Trial balance is not balanced. Difference: $
          {Math.abs(data.totalDebits - data.totalCredits).toFixed(2)}
        </div>
      )}
    </div>
  );
} 