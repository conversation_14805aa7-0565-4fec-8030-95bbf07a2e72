"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods, budgetLineItems, accounts, journalEntries, transactions } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, gte, lte, inArray } from "drizzle-orm";

export async function getBudgetVsActual(budgetPeriodId?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    let period;
    
    if (budgetPeriodId) {
        period = await db.query.budgetPeriods.findFirst({
            where: and(
                eq(budgetPeriods.id, budgetPeriodId),
                eq(budgetPeriods.organizationId, organizationId)
            ),
            with: {
                lineItems: {
                    with: {
                        account: true
                    }
                }
            }
        });
    } else {
        // Try to find a default budget period
        period = await db.query.budgetPeriods.findFirst({
            where: and(
                eq(budgetPeriods.organizationId, organizationId),
                eq(budgetPeriods.isDefault, true)
            ),
            with: {
                lineItems: {
                    with: {
                        account: true
                    }
                }
            }
        });
        
        // If no default, try to find any active period
        if (!period) {
            period = await db.query.budgetPeriods.findFirst({
                where: and(
                    eq(budgetPeriods.organizationId, organizationId),
                    eq(budgetPeriods.status, "active")
                ),
                with: {
                    lineItems: {
                        with: {
                            account: true
                        }
                    }
                }
            });
        }
    }

    if (!period) {
        return {
            period: null,
            report: [],
            totals: { budgeted: 0, actual: 0, variance: 0 }
        };
    }

    const accountIds = period.lineItems.map(li => li.accountId).filter((id): id is string => !!id);
    if(accountIds.length === 0) {
        return {
            period,
            report: [],
            totals: { budgeted: 0, actual: 0, variance: 0 }
        };
    }

    const actualsQuery = db
        .select({
            accountId: journalEntries.accountId,
            debitAmount: journalEntries.debitAmount,
            creditAmount: journalEntries.creditAmount,
        })
        .from(journalEntries)
        .leftJoin(transactions, eq(journalEntries.transactionId, transactions.id))
        .where(and(
            eq(transactions.organizationId, organizationId),
            gte(transactions.date, period.startDate),
            lte(transactions.date, period.endDate),
            inArray(journalEntries.accountId, accountIds)
        ));
    
    const actualEntries = await actualsQuery;

    const actuals = new Map<string, number>();
    for (const entry of actualEntries) {
        if (!entry.accountId) continue;
        const account = period.lineItems.find(li => li.accountId === entry.accountId)?.account;
        if(!account) continue;

        const current = actuals.get(entry.accountId) || 0;
        let change = 0;
        if(account.normalBalance === 'debit') {
            change = parseFloat(entry.debitAmount ?? "0") - parseFloat(entry.creditAmount ?? "0");
        } else {
            change = parseFloat(entry.creditAmount ?? "0") - parseFloat(entry.debitAmount ?? "0");
        }
        actuals.set(entry.accountId, current + change);
    }

    const report = period.lineItems.map(li => {
        const budgeted = parseFloat(li.budgetedAmount);
        const actual = li.accountId ? (actuals.get(li.accountId) || 0) : 0;
        const variance = budgeted - actual;
        return {
            accountName: li.account?.name,
            budgeted,
            actual,
            variance
        };
    });

    const totalBudgeted = report.reduce((sum, r) => sum + r.budgeted, 0);
    const totalActual = report.reduce((sum, r) => sum + r.actual, 0);
    const totalVariance = totalBudgeted - totalActual;

    return {
        period,
        report,
        totals: {
            budgeted: totalBudgeted,
            actual: totalActual,
            variance: totalVariance
        }
    };
} 