"use server";

import { db } from "@/db/drizzle";
import { accounts, journalEntries, transactions } from "@/db/schema/schema";
import { eq, and, gte, lte } from "drizzle-orm";

export async function getAccountBalances(organizationId: string, fromDate?: string, toDate?: string) {
  const allAccounts = await db
    .select({
      id: accounts.id,
      name: accounts.name,
      accountNumber: accounts.accountNumber,
      type: accounts.type,
      normalBalance: accounts.normalBalance,
    })
    .from(accounts)
    .where(eq(accounts.organizationId, organizationId));

  const filters = [eq(transactions.organizationId, organizationId)];
  if (fromDate) filters.push(gte(transactions.date, new Date(fromDate)));
  if (toDate) filters.push(lte(transactions.date, new Date(toDate)));

  const journalEntriesQuery = db
    .select({
      accountId: journalEntries.accountId,
      debitAmount: journalEntries.debitAmount,
      creditAmount: journalEntries.creditAmount,
    })
    .from(journalEntries)
    .leftJoin(transactions, eq(journalEntries.transactionId, transactions.id))
    .where(and(...filters));

  const entries = await journalEntriesQuery;
  const balances = new Map<string, { debit: number; credit: number }>();

  for (const entry of entries) {
    if (!entry.accountId) continue;
    const current = balances.get(entry.accountId) || { debit: 0, credit: 0 };
    current.debit += parseFloat(entry.debitAmount ?? "0");
    current.credit += parseFloat(entry.creditAmount ?? "0");
    balances.set(entry.accountId, current);
  }

  const accountsWithBalances = allAccounts.map((acc) => {
    const balance = balances.get(acc.id) || { debit: 0, credit: 0 };
    let finalBalance = 0;
    if (acc.normalBalance === "debit") {
      finalBalance = balance.debit - balance.credit;
    } else {
      finalBalance = balance.credit - balance.debit;
    }
    return {
      account: {
        id: acc.id,
        name: acc.name,
        account_number: acc.accountNumber ?? "",
        type: acc.type,
        normalBalance: acc.normalBalance,
      },
      debit_total: balance.debit,
      credit_total: balance.credit,
      balance: finalBalance,
    };
  });

  return accountsWithBalances;
} 