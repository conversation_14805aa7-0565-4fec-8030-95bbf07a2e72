"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { products } from "@/db/schema/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, ArrowUp, ArrowDown, Minus } from "lucide-react";
import * as React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { ProductWithAnalytics } from "./ProductsPageClient";

// Type for a single product row, inferred from the Drizzle schema.
export type Product = ProductWithAnalytics;

interface GetProductColumnsProps {
  onEdit: (product: Product) => void;
  onDelete: (productId: string) => void;
  onAdjustPrice: (product: Product) => void;
}

export function getProductColumns({
  onEdit,
  onDelete,
  onAdjustPrice,
}: GetProductColumnsProps): ColumnDef<Product>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
        return (
          <div className="flex flex-col">
            <span className="font-medium">{row.original.name}</span>
          </div>
        )
      }
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.original.description;
        return <p className="text-sm text-muted-foreground truncate max-w-xs">{description}</p>
      }
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.getValue("type") as string;
        return <Badge variant="outline">{type.replace(/_/g, ' ')}</Badge>;
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status as 'profitable' | 'loss' | 'break_even';
        
        const statusConfig = {
          profitable: {
            label: "Profitable",
            color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            icon: <ArrowUp className="h-3 w-3 text-green-500" />
          },
          loss: {
            label: "Loss",
            color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            icon: <ArrowDown className="h-3 w-3 text-red-500" />
          },
          break_even: {
            label: "Break Even",
            color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            icon: <Minus className="h-3 w-3 text-yellow-500" />
          },
        };

        const config = statusConfig[status] || statusConfig.break_even;

        return (
          <Badge className={`flex items-center gap-1 ${config.color} hover:${config.color}`}>
            {config.icon}
            <span>{config.label}</span>
          </Badge>
        );
      }
    },
    {
      accessorKey: "price",
      header: () => <div className="text-right">Price</div>,
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("price") as string);
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount);
        return <div className="text-right font-medium">{formatted}</div>;
      },
    },
    {
      accessorKey: "margin",
      header: () => <div className="text-right">Profitability</div>,
      cell: ({ row }) => {
        const margin = parseFloat(row.original.margin as string) || 0;
        const profitPerUnit = parseFloat(row.original.profitPerUnit as string) || 0;
        
        const marginColor = margin > 0 ? "text-green-600" : "text-red-600";
        
        return (
          <div className="text-right">
            <div className={`font-medium ${marginColor}`}>{margin.toFixed(2)}%</div>
            <div className="text-xs text-muted-foreground">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
              }).format(profitPerUnit)}/unit
            </div>
          </div>
        );
      },
    },
    // Optional: Show account IDs if needed
    // {
    //   accessorKey: "revenueAccountId",
    //   header: "Revenue Account ID",
    // },
    // {
    //   accessorKey: "cogsAccountId",
    //   header: "COGS Account ID",
    // },
    // {
    //   accessorKey: "inventoryAccountId",
    //   header: "Inventory Account ID",
    // },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => {
        const product = row.original;
        const [dropdownOpen, setDropdownOpen] = React.useState(false);
        
        return (
          <div className="text-right font-medium">
            <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem 
                  onSelect={(event) => {
                    event.preventDefault();
                    setDropdownOpen(false);
                    onEdit(product);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onAdjustPrice(product)}>
                  Adjust Price
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDelete(product.id)}>
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
}