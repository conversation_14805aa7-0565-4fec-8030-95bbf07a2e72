import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

/**
 * Checks if an account is a leaf (subaccount) — i.e., has no subaccounts beneath it.
 * Use this to enforce that only subaccounts can have transactions.
 *
 * @param accountId The account's ID to check
 * @returns Promise<boolean> true if the account is a leaf (no children), false otherwise
 */
export async function isLeafAccount(accountId: string): Promise<boolean> {
  const children = await db
    .select({ id: accounts.id })
    .from(accounts)
    .where(eq(accounts.parentId, accountId))
    .limit(1);
  return children.length === 0;
} 