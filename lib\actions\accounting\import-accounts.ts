"use server";
import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getTemplateAccounts } from "@/lib/data/coaTemplates";
import { accountFormSchema, NewAccount } from "./account.schema";
import { eq } from "drizzle-orm";
import { randomUUID } from 'crypto';

// Valid enum values for mapping/validation
const VALID_FINANCIAL_SECTIONS = [
  "current_assets",
  "non_current_assets",
  "current_liabilities",
  "non_current_liabilities",
  "equity",
  "operating_revenue",
  "other_revenue",
  "cost_of_goods_sold",
  "operating_expenses",
  "other_expenses",
];

export type ImportAccountsParams = {
  organizationId: string;
  templateId?: string;
  parsedAccounts?: any[];
};

export type ImportAccountsResult = {
  success: boolean;
  importedCount?: number;
  errors?: string[];
  duplicates?: string[];
};

// Utility: Map human-friendly section names to valid enums
function mapFinancialSection(section: string | undefined): string | undefined {
  if (!section) return undefined;
  const normalized = section.trim().toLowerCase().replace(/ /g, "_");
  if (VALID_FINANCIAL_SECTIONS.includes(normalized)) return normalized;
  // Add more mappings as needed
  if (section === "Balance Sheet") return "current_assets"; // Example mapping
  return undefined;
}

// Utility to sanitize ID fields (no longer UUID-specific)
function sanitizeId(value: any): string | null {
  if (!value || typeof value !== "string" || value.trim() === "") return null;
  return value;
}

export async function importAccounts({ organizationId, templateId, parsedAccounts }: ImportAccountsParams): Promise<ImportAccountsResult> {
  // Defensive: ensure organizationId is a valid, non-empty string
  if (!organizationId || typeof organizationId !== "string" || organizationId.trim() === "") {
    throw new Error("Invalid organizationId: must be a non-empty string");
  }
  // 1. Load accounts from template or use provided array
  let accountsToImport: any[] = [];
  if (templateId) {
    const loaded = await getTemplateAccounts(templateId);
    accountsToImport = loaded ? loaded : [];
    if (!accountsToImport.length) return { success: false, errors: ["Template not found or empty."] };
  } else if (parsedAccounts) {
    accountsToImport = parsedAccounts;
  } else {
    return { success: false, errors: ["No templateId or parsedAccounts provided."] };
  }

  // Log the incoming accounts to import
  console.log('accountsToImport:', accountsToImport);

  // 2. Validate and map fields
  const errors: string[] = [];
  const validAccounts: NewAccount[] = [];
  const nameSet = new Set<string>();
  const numberSet = new Set<string>();
  // Map to store parentAccountNumber for second pass
  const parentMap: Record<string, string | undefined> = {};

  for (const acc of accountsToImport) {
    // Log each incoming account
    console.log('Mapping account:', acc);
    // Map enums
    const mappedSection = mapFinancialSection(acc.financialStatementSection);
    // Store parentAccountNumber for later
    if (acc.accountNumber) {
      parentMap[acc.accountNumber] = acc.parentAccountNumber;
    }
    // Remove parentAccountNumber from validation object
    const { parentAccountNumber, ...rest } = acc;
    // Defensive: never allow parentId to be ''
    let parentId: string | null = null;
    if (typeof acc.parentId === 'string' && acc.parentId.trim() !== '') {
      parentId = acc.parentId;
    }
    // Log the parentId and parentAccountNumber for this account
    console.log('Account mapping:', { accountNumber: acc.accountNumber, parentAccountNumber, parentId });
    const data = {
      ...rest,
      parentId,
      organizationId,
      financialStatementSection: mappedSection,
    };
    // Validate
    const parseResult = accountFormSchema.safeParse(data);
    if (!parseResult.success) {
      errors.push(`Account ${acc.name || acc.accountNumber}: ${parseResult.error.message}`);
      continue;
    }
    // Check for duplicates in import batch
    if (nameSet.has(parseResult.data.name.toLowerCase())) {
      errors.push(`Duplicate name in import: ${parseResult.data.name}`);
      continue;
    }
    if (numberSet.has(parseResult.data.accountNumber)) {
      errors.push(`Duplicate account number in import: ${parseResult.data.accountNumber}`);
      continue;
    }
    nameSet.add(parseResult.data.name.toLowerCase());
    numberSet.add(parseResult.data.accountNumber);
    validAccounts.push(parseResult.data);
  }

  // Log the parentMap after mapping
  console.log('parentMap:', parentMap);
  // Log the final validAccounts array
  console.log('validAccounts:', validAccounts);

  if (errors.length > 0) {
    return { success: false, errors };
  }

  // Log any account with parentId === '' before insert
  const offending = validAccounts.filter(acc => acc.parentId === '');
  if (offending.length > 0) {
    console.error('Accounts with parentId as empty string:', offending);
    // Optionally, throw to prevent insert
    throw new Error('Attempted to insert account with parentId as empty string');
  }

  // 3. Check for duplicates in DB
  const existing = await db
    .select({ accountNumber: accounts.accountNumber, name: accounts.name })
    .from(accounts)
    .where(eq(accounts.organizationId, organizationId));
  const existingNumbers = new Set(existing.map(a => a.accountNumber));
  const existingNames = new Set(existing.map(a => a.name.toLowerCase()));
  const duplicates: string[] = [];
  for (const acc of validAccounts) {
    if (existingNumbers.has(acc.accountNumber)) {
      duplicates.push(`Account number already exists: ${acc.accountNumber}`);
    }
    if (existingNames.has(acc.name.toLowerCase())) {
      duplicates.push(`Account name already exists: ${acc.name}`);
    }
  }
  if (duplicates.length > 0) {
    return { success: false, duplicates };
  }

  // 4. Insert accounts, handle parent relationships
  // First pass: insert all accounts without parentId
  const accountNumberToId: Record<string, string> = {};
  const inserted = await db.insert(accounts).values(
    validAccounts.map(acc => ({
      id: randomUUID(),
      ...acc,
      parentId: sanitizeId(acc.parentId),
      organizationId // always a string
    }))
  ).returning({ id: accounts.id, accountNumber: accounts.accountNumber });
  for (const row of inserted) {
    if (row.accountNumber) accountNumberToId[row.accountNumber] = row.id;
  }
  // Second pass: update parentId where needed, using parentMap
  for (const acc of validAccounts) {
    const parentAccountNumber = parentMap[acc.accountNumber];
    const childId = accountNumberToId[acc.accountNumber];
    // Only update if parentAccountNumber is a non-empty string
    if (parentAccountNumber && parentAccountNumber !== "") {
      const parentId = accountNumberToId[parentAccountNumber];
      await db.update(accounts)
        .set({ parentId: parentId || null })
        .where(eq(accounts.id, childId));
    }
  }

  return { success: true, importedCount: validAccounts.length };
} 