"use server";

import { db } from "@/db/drizzle";
import { transactions, journalEntries, auditLogs } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq, and } from "drizzle-orm";
import { revalidatePath } from "next/cache";

async function getUserOrganizationId(userId: string) {
  const { member } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: member.organizationId })
    .from(member)
.where(eq(member.userId, userId));
  return orgs[0]?.organizationId || null;
}

export async function deleteTransaction(transactionId: string) {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;
  if (!userId) return { success: false, message: "Not authenticated." };
  const organizationId = await getUserOrganizationId(userId);
  if (!organizationId) return { success: false, message: "No organization found." };
  try {
    const beforeTx = await db.select().from(transactions).where(and(eq(transactions.id, transactionId), eq(transactions.organizationId, organizationId))).limit(1);
    await db.transaction(async (tx) => {
      await tx.delete(journalEntries).where(eq(journalEntries.transactionId, transactionId));
      await tx.delete(transactions).where(and(eq(transactions.id, transactionId), eq(transactions.organizationId, organizationId)));
    });
    await db.insert(auditLogs).values({
      organizationId,
      userId,
      action: "delete",
      resourceType: "transaction",
      resourceId: transactionId,
      oldValues: beforeTx[0] || null,
      newValues: null,
      createdAt: new Date(),
    });
    revalidatePath("/transactions");
    revalidatePath("/reporting");
    return { success: true, message: "Transaction deleted." };
  } catch (error) {
    return { success: false, message: "Failed to delete transaction." };
  }
} 