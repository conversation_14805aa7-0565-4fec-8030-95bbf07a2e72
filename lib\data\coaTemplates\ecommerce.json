[{"accountNumber": "1000", "name": "Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1000, "subcategory": "Main Category", "gaapCategory": "Assets", "ifrsCategory": "Assets", "description": "Main asset category header", "isActive": true}, {"accountNumber": "1100", "name": "Current Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1100, "subcategory": "Current Assets", "gaapCategory": "Current Assets", "ifrsCategory": "Current Assets", "description": "Assets expected to be converted to cash within one year", "isActive": true}, {"accountNumber": "1110", "name": "Cash and Cash Equivalents", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "n_a", "displayOrder": 1110, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Cash on hand and in bank accounts", "isActive": true}, {"accountNumber": "1112", "name": "Cash", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1112, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Primary business cash account", "isActive": true}, {"accountNumber": "1114", "name": "Payment Processor Funds", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1114, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Funds held by payment processors like Stripe, PayPal, etc.", "isActive": true}, {"accountNumber": "1120", "name": "Accounts Receivable", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1120, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Money owed by customers", "isActive": true}, {"accountNumber": "1130", "name": "Inventory", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1130, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Goods available for sale", "isActive": true}, {"accountNumber": "1200", "name": "Prepaid Expenses", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "prepaid_expenses", "cashFlowCategory": "operating", "displayOrder": 1200, "subcategory": "Prepaid Expenses", "gaapCategory": "Prepaid Expenses and Other Current Assets", "ifrsCategory": "Other Current Assets", "description": "Expenses paid in advance", "isActive": true}, {"accountNumber": "1500", "name": "Fixed Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1500, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Long-term tangible assets", "isActive": true}, {"accountNumber": "1505", "name": "Computer Equipment", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1505, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Computers and related hardware", "isActive": true}, {"accountNumber": "1510", "name": "Accumulated Depreciation - Equipment", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1510, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Contra-asset for equipment depreciation", "isActive": true}, {"accountNumber": "1700", "name": "Intangible Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "intangible_assets", "cashFlowCategory": "investing", "displayOrder": 1700, "subcategory": "Intangible Assets", "gaapCategory": "Intangible Assets", "ifrsCategory": "Intangible Assets", "description": "Long-term non-physical assets", "isActive": true}, {"accountNumber": "1710", "name": "Website Development Costs", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1700", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "intangible_assets", "cashFlowCategory": "investing", "displayOrder": 1710, "subcategory": "Intangible Assets", "gaapCategory": "Intangible Assets", "ifrsCategory": "Intangible Assets", "description": "Capitalized costs for creating the e-commerce website", "isActive": true}, {"accountNumber": "1720", "name": "Accumulated Amortization - Website", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1700", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "intangible_assets", "cashFlowCategory": "investing", "displayOrder": 1720, "subcategory": "Intangible Assets", "gaapCategory": "Intangible Assets", "ifrsCategory": "Intangible Assets", "description": "Amortization of website development costs", "isActive": true}, {"accountNumber": "2000", "name": "Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2000, "subcategory": "Main Category", "gaapCategory": "Liabilities", "ifrsCategory": "Liabilities", "description": "Main liability category header", "isActive": true}, {"accountNumber": "2100", "name": "Current Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2100, "subcategory": "Current Liabilities", "gaapCategory": "Current Liabilities", "ifrsCategory": "Current Liabilities", "description": "Liabilities due within one year", "isActive": true}, {"accountNumber": "2105", "name": "Payroll Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2105, "subcategory": "Payroll", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Accrued Liabilities", "description": "Accrued but unpaid payroll", "isActive": true}, {"accountNumber": "2110", "name": "Accounts Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accounts_payable", "cashFlowCategory": "operating", "displayOrder": 2110, "subcategory": "Accounts Payable", "gaapCategory": "Accounts Payable", "ifrsCategory": "Trade and Other Payables", "description": "Money owed to suppliers", "isActive": true}, {"accountNumber": "2115", "name": "Payroll Taxes Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2115, "subcategory": "Payroll", "gaapCategory": "Payroll Taxes Payable", "ifrsCategory": "Payroll Taxes Payable", "description": "Payroll taxes withheld but not yet remitted", "isActive": true}, {"accountNumber": "2120", "name": "Sales Tax Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accrued_liabilities", "cashFlowCategory": "operating", "displayOrder": 2120, "subcategory": "Taxes", "gaapCategory": "Taxes Payable", "ifrsCategory": "Current Tax Liabilities", "description": "Sales tax collected and due to the government", "isActive": true}, {"accountNumber": "2125", "name": "Employee Benefits Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2125, "subcategory": "Payroll", "gaapCategory": "Benefits Payable", "ifrsCategory": "Benefits Payable", "description": "Employee benefits withheld but not yet remitted", "isActive": true}, {"accountNumber": "2135", "name": "Accrued Expenses", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accrued_liabilities", "cashFlowCategory": "operating", "displayOrder": 2135, "subcategory": "Accrued Expenses", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Accrued Liabilities", "description": "Accrued expenses not yet paid", "isActive": true}, {"accountNumber": "2140", "name": "Unearned Revenue", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "unearned_revenue", "cashFlowCategory": "operating", "displayOrder": 2140, "subcategory": "Unearned Revenue", "gaapCategory": "Unearned Revenue", "ifrsCategory": "Contract Liabilities", "description": "Payments received for goods or services not yet delivered", "isActive": true}, {"accountNumber": "2500", "name": "Long-Term Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2500, "subcategory": "Long-Term Liabilities", "gaapCategory": "<PERSON>-<PERSON><PERSON>", "ifrsCategory": "Non-current Liabilities", "description": "Liabilities due after one year", "isActive": true}, {"accountNumber": "2510", "name": "Loan Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_liabilities", "accountGroup": "long_term_debt", "cashFlowCategory": "financing", "displayOrder": 2510, "subcategory": "Loans", "gaapCategory": "Notes Payable", "ifrsCategory": "Borrowings", "description": "Long-term loans owed", "isActive": true}, {"accountNumber": "3000", "name": "Equity", "type": "equity", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "equity", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 3000, "subcategory": "Main Category", "gaapCategory": "Equity", "ifrsCategory": "Equity", "description": "Main equity category header", "isActive": true}, {"accountNumber": "3100", "name": "Owner's Equity", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "equity", "financialStatementSection": "equity", "accountGroup": "equity", "cashFlowCategory": "financing", "displayOrder": 3100, "subcategory": "Ownership", "gaapCategory": "Contributed Capital", "ifrsCategory": "Issued Capital and Reserves", "description": "Owner's contributions and distributions", "isActive": true}, {"accountNumber": "3200", "name": "Retained Earnings", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "equity", "financialStatementSection": "equity", "accountGroup": "retained_earnings", "cashFlowCategory": "n_a", "displayOrder": 3200, "subcategory": "Earnings", "gaapCategory": "Retained Earnings", "ifrsCategory": "Retained Earnings", "description": "Cumulative net income/loss", "isActive": true}, {"accountNumber": "4000", "name": "Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "other", "cashFlowCategory": "operating", "displayOrder": 4000, "subcategory": "Main Category", "gaapCategory": "Revenue", "ifrsCategory": "Revenue", "description": "Main revenue category header", "isActive": true}, {"accountNumber": "4100", "name": "Sales Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "sales_revenue", "cashFlowCategory": "operating", "displayOrder": 4100, "subcategory": "Product Sales", "gaapCategory": "Sales Revenue", "ifrsCategory": "Sale of Goods", "description": "Revenue from selling physical products.", "isActive": true}, {"accountNumber": "4200", "name": "Service Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "service_revenue", "cashFlowCategory": "operating", "displayOrder": 4200, "subcategory": "Service/Subscription Revenue", "gaapCategory": "Service Revenue", "ifrsCategory": "Rendering of Services", "description": "Revenue from digital services or subscriptions.", "isActive": true}, {"accountNumber": "4500", "name": "Sales Returns and Allowances", "type": "revenue", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "contra_revenue", "financialStatementSection": "operating_revenue", "accountGroup": "sales_revenue", "cashFlowCategory": "operating", "displayOrder": 4500, "subcategory": "Adjustments", "gaapCategory": "Sales Returns and Allowances", "ifrsCategory": "Revenue", "description": "Contra-revenue for customer returns", "isActive": true}, {"accountNumber": "5000", "name": "Cost of Goods Sold", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5000, "subcategory": "Main Category", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Direct costs of producing goods sold", "isActive": true}, {"accountNumber": "5100", "name": "Cost of Materials", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5000", "level": 1, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5100, "subcategory": "Direct Costs", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Cost of raw materials for products sold.", "isActive": true}, {"accountNumber": "5200", "name": "Cost of Services", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5000", "level": 1, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5200, "subcategory": "Direct Costs", "gaapCategory": "Cost of Services", "ifrsCategory": "Cost of Sales", "description": "Direct costs to deliver digital services (e.g., hosting, data APIs).", "isActive": true}, {"accountNumber": "6000", "name": "Operating Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "other", "cashFlowCategory": "operating", "displayOrder": 6000, "subcategory": "Main Category", "gaapCategory": "Operating Expenses", "ifrsCategory": "Operating Expenses", "description": "Expenses incurred in day-to-day business operations", "isActive": true}, {"accountNumber": "6100", "name": "Salaries and Wages", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "personnel_expenses", "cashFlowCategory": "operating", "displayOrder": 6100, "subcategory": "Payroll", "gaapCategory": "Salaries and Wages Expense", "ifrsCategory": "Employee Benefits Expense", "description": "Employee salaries", "isActive": true}, {"accountNumber": "6200", "name": "Marketing and Advertising", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "marketing_and_advertising", "cashFlowCategory": "operating", "displayOrder": 6200, "subcategory": "Marketing", "gaapCategory": "Advertising Expense", "ifrsCategory": "Advertising and Promotion Expense", "description": "Costs for marketing and ads", "isActive": true}, {"accountNumber": "6300", "name": "Rent and Utilities", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "administrative_expenses", "cashFlowCategory": "operating", "displayOrder": 6300, "subcategory": "Office", "gaapCategory": "Rent Expense", "ifrsCategory": "Rental Expense", "description": "Office rent and utilities", "isActive": true}, {"accountNumber": "6400", "name": "Software and Subscriptions", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "technology_expenses", "cashFlowCategory": "operating", "displayOrder": 6400, "subcategory": "Software", "gaapCategory": "Technology", "ifrsCategory": "Other Operating Expenses", "description": "Costs for SaaS and other software", "isActive": true}, {"accountNumber": "6500", "name": "Payroll Taxes", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "payroll_expenses", "cashFlowCategory": "operating", "displayOrder": 6500, "subcategory": "Payroll", "gaapCategory": "Payroll Taxes", "ifrsCategory": "Social Security Costs", "description": "Employer payroll taxes (FICA, FUTA, SUTA, etc.)", "isActive": true}, {"accountNumber": "6600", "name": "Employee Benefits", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "payroll_expenses", "cashFlowCategory": "operating", "displayOrder": 6600, "subcategory": "Payroll", "gaapCategory": "Employee Benefits", "ifrsCategory": "Employee Benefits", "description": "Health, dental, retirement, and other employee benefits", "isActive": true}, {"accountNumber": "6700", "name": "Insurance Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "insurance_expenses", "cashFlowCategory": "operating", "displayOrder": 6700, "subcategory": "Insurance", "gaapCategory": "Insurance Expense", "ifrsCategory": "Insurance Expense", "description": "Insurance premiums and related costs", "isActive": true}, {"accountNumber": "6800", "name": "Travel Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "travel_expenses", "cashFlowCategory": "operating", "displayOrder": 6800, "subcategory": "Travel", "gaapCategory": "Travel Expense", "ifrsCategory": "Travel Expense", "description": "Travel and transportation costs", "isActive": true}, {"accountNumber": "6900", "name": "Professional Fees", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "professional_fees", "cashFlowCategory": "operating", "displayOrder": 6900, "subcategory": "Professional Fees", "gaapCategory": "Professional Fees", "ifrsCategory": "Professional Fees", "description": "Legal, consulting, and other professional services", "isActive": true}, {"accountNumber": "7100", "name": "Depreciation Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "non_current_assets", "accountGroup": "depreciation_amortization", "cashFlowCategory": "operating", "displayOrder": 7100, "subcategory": "Depreciation", "gaapCategory": "Depreciation Expense", "ifrsCategory": "Depreciation and Amortisation Expense", "description": "Depreciation expense for tangible assets", "isActive": true}, {"accountNumber": "7200", "name": "Amortization Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "non_current_assets", "accountGroup": "depreciation_amortization", "cashFlowCategory": "operating", "displayOrder": 7200, "subcategory": "Amortization", "gaapCategory": "Amortization Expense", "ifrsCategory": "Depreciation and Amortisation Expense", "description": "Amortization of intangible assets like website costs", "isActive": true}]