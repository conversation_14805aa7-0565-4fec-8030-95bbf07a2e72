"use client";

import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
// import { UserIcon } from "lucide-react"; // Uncomment if you want a generic icon fallback

interface AvatarDropdownProps {
  name: string;
  email: string;
  image?: string | null;
}

export const AvatarDropdown: React.FC<AvatarDropdownProps> = ({ name, email, image }) => {
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            router.push("/signin");
          },
          onError: (ctx) => {
            console.error("Sign out error:", ctx.error);
            // Even if there's an error, redirect to signin page
            router.push("/signin");
          },
        },
      });
    } catch (error) {
      console.error("Sign out error:", error);
      // Fallback: redirect to signin even if signout fails
      router.push("/signin");
    } finally {
      setIsSigningOut(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer">
          {image ? (
            <AvatarImage src={image} alt={name} />
          ) : (
            // Use first letter of name as fallback, or swap in a logo/icon here in the future
            <AvatarFallback className="bg-primary">{name ? name[0].toUpperCase() : "U"}</AvatarFallback>
            // Or use a generic icon: <UserIcon className="w-5 h-5" />
          )}
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="font-semibold">{name}</div>
          <div className="text-xs text-muted-foreground truncate max-w-[200px]">{email}</div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => {/* TODO: Navigate to profile page */}}>Profile</DropdownMenuItem>
        <DropdownMenuItem onClick={() => {/* TODO: Navigate to settings page */}}>Settings</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleSignOut} 
          disabled={isSigningOut}
          className="text-red-600 focus:text-red-700 disabled:opacity-50"
        >
          {isSigningOut ? (
            <div className="flex items-center gap-2">
              <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10" strokeOpacity="0.2"/>
                <path d="M12 2a10 10 0 0 1 10 10"/>
              </svg>
              Signing out...
            </div>
          ) : (
            "Sign out"
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}; 