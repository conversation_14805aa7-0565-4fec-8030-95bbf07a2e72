"use client";

import { CountrySelector } from "@/components/general/CountrySelector";
import { PhoneNumberInput } from "@/components/general/PhoneNumberInput";
import { useState } from "react";

export default function SearchTestPage() {
  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectedCountryAutoFocus, setSelectedCountryAutoFocus] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneNumberAutoFocus, setPhoneNumberAutoFocus] = useState("");

  return (
    <div className="max-w-2xl mx-auto p-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Search Components Test</h1>
        <p className="text-muted-foreground mt-2">Testing the CountrySelector and PhoneNumberInput components with auto-focus functionality</p>
      </div>
      
      <div className="space-y-8">
        {/* Regular Country Selector */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold">Country Selector (Regular)</h2>
          <label className="block text-sm font-medium">
            Select a country (no auto-focus):
          </label>
          <CountrySelector
            value={selectedCountry}
            onChange={setSelectedCountry}
            className="w-full max-w-md"
          />
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <span className="font-medium">Selected:</span> 
              <span className="ml-2 font-mono">{selectedCountry || "None"}</span>
            </p>
          </div>
        </div>

        {/* Auto-focus Country Selector */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold">Country Selector (Auto-Focus)</h2>
          <label className="block text-sm font-medium">
            Select a country (with auto-focus):
          </label>
          <CountrySelector
            value={selectedCountryAutoFocus}
            onChange={setSelectedCountryAutoFocus}
            className="w-full max-w-md"
            autoFocus
          />
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <span className="font-medium">Selected:</span> 
              <span className="ml-2 font-mono">{selectedCountryAutoFocus || "None"}</span>
            </p>
          </div>
        </div>

        {/* Regular Phone Number Input */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold">Phone Number Input (Regular)</h2>
          <label className="block text-sm font-medium">
            Enter phone number (no auto-focus):
          </label>
          <PhoneNumberInput
            value={phoneNumber}
            onChange={setPhoneNumber}
            className="w-full max-w-md"
          />
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <span className="font-medium">Phone:</span> 
              <span className="ml-2 font-mono">{phoneNumber || "None"}</span>
            </p>
          </div>
        </div>

        {/* Auto-focus Phone Number Input */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold">Phone Number Input (Auto-Focus)</h2>
          <label className="block text-sm font-medium">
            Enter phone number (with auto-focus):
          </label>
          <PhoneNumberInput
            value={phoneNumberAutoFocus}
            onChange={setPhoneNumberAutoFocus}
            className="w-full max-w-md"
            autoFocus
          />
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <span className="font-medium">Phone:</span> 
              <span className="ml-2 font-mono">{phoneNumberAutoFocus || "None"}</span>
            </p>
          </div>
        </div>
      </div>
      
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Test Instructions</h2>
        <ul className="space-y-2 text-sm">
          <li className="flex items-start gap-2">
            <span className="text-primary font-bold">1.</span>
            <span>Click the <strong>regular</strong> country selector - you'll need to manually click the search input</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-primary font-bold">2.</span>
            <span>Click the <strong>auto-focus</strong> country selector - the search input should be automatically focused</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-primary font-bold">3.</span>
            <span>Try the phone number inputs - the auto-focus version should focus the country selector search when opened</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-primary font-bold">4.</span>
            <span>Start typing immediately in the auto-focus versions to test the functionality</span>
          </li>
        </ul>
      </div>
    </div>
  );
} 