"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { getAccountBalances } from "./get-account-balances";

export async function getBalanceSheet(asOfDate?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;
  
    const date = asOfDate ? new Date(asOfDate) : new Date();
  
    const accountsWithBalances = await getAccountBalances(organizationId, undefined, date.toISOString());
  
    const assets = accountsWithBalances.filter((ab) => ab.account.type === "asset");
    const liabilities = accountsWithBalances.filter((ab) => ab.account.type === "liability");
    const equity = accountsWithBalances.filter((ab) => ab.account.type === "equity");
  
    const totalAssets = assets.reduce((sum, ab) => sum + ab.balance, 0);
    const totalLiabilities = liabilities.reduce((sum, ab) => sum + ab.balance, 0);
    const totalEquity = equity.reduce((sum, ab) => sum + ab.balance, 0);
  
    return {
      assets,
      liabilities,
      equity,
      totalAssets,
      totalLiabilities,
      totalEquity,
      totalLiabilitiesAndEquity: totalLiabilities + totalEquity,
      asOfDate: date.toLocaleDateString(),
    };
} 