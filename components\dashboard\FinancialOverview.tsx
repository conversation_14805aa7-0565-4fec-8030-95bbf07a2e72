import React from "react";
import { Card } from "@/components/ui/card";
import IncomeExpensesChart from "./charts/IncomeExpensesChart";
import {
  DollarSign,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface FinancialOverviewProps {
  monthlyEarnings?: number;
  monthlyExpenses?: number;
  monthlyGrowth?: number;
  isLoading?: boolean;
  chartData?: {
    date: string;
    income: number;
    expenses: number;
  }[];
}

const FinancialOverview = ({
  monthlyEarnings = 12500,
  monthlyExpenses = 4800,
  monthlyGrowth = 15.6,
  isLoading = false,
  chartData = Array.from({ length: 30 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (30 - i));
    return {
      date: date.toISOString().split("T")[0],
      income: Math.floor(Math.random() * (2500 - 500 + 1)) + 500,
      expenses: Math.floor(Math.random() * (1500 - 300 + 1)) + 300,
    };
  }),
}: FinancialOverviewProps) => {
  const isPositiveGrowth = monthlyGrowth >= 0;

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        {/* Monthly Earnings Card */}
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Monthly Earnings</p>
              {isLoading ? (
                <Skeleton className="h-8 w-32" />
              ) : (
                <h3 className="text-2xl font-bold">
                  ${monthlyEarnings.toLocaleString('en-US')}
                </h3>
              )}
            </div>
            <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-green-500" />
            </div>
          </div>
        </Card>

        {/* Monthly Expenses Card */}
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Monthly Expenses</p>
              {isLoading ? (
                <Skeleton className="h-8 w-28" />
              ) : (
                <h3 className="text-2xl font-bold">
                  ${monthlyExpenses.toLocaleString('en-US')}
                </h3>
              )}
            </div>
            <div className="h-12 w-12 rounded-full bg-red-500/10 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-red-500" />
            </div>
          </div>
        </Card>

        {/* Monthly Growth Card */}
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Monthly Growth</p>
              {isLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <div className="flex items-center gap-2">
                  <h3 className="text-2xl font-bold">
                    {monthlyGrowth === 0
                      ? "0"
                      : `${Math.abs(monthlyGrowth).toFixed(1)}%`}
                  </h3>
                  {monthlyGrowth !== 0 &&
                    (isPositiveGrowth ? (
                      <ArrowUpRight className="h-5 w-5 text-green-500" />
                    ) : (
                      <ArrowDownRight className="h-5 w-5 text-red-500" />
                    ))}
                </div>
              )}
            </div>
            <div
              className={`h-12 w-12 rounded-full flex items-center justify-center ${
                monthlyGrowth === 0
                  ? "bg-gray-500/10"
                  : isPositiveGrowth
                  ? "bg-green-500/10"
                  : "bg-red-500/10"
              }`}
            >
              <TrendingUp
                className={`h-6 w-6 ${
                  monthlyGrowth === 0
                    ? "text-gray-500"
                    : isPositiveGrowth
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              />
            </div>
          </div>
        </Card>
      </div>

      {/* Income vs Expenses Chart Card */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">
          Income vs Expenses
        </h3>
        <div className="h-[300px]">
          {isLoading ? <Skeleton className="h-full w-full" /> : <IncomeExpensesChart data={chartData.map(d => ({...d, date: d.date}))} />}
        </div>
      </Card>
    </div>
  );
};

export default FinancialOverview; 