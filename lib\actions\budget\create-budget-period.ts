"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { randomUUID } from 'crypto';

export async function createBudgetPeriod(data: {
  name: string;
  description?: string;
  startDate: string;
  endDate: string;
  isDefault?: boolean;
}) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    if (data.isDefault) {
      await db
        .update(budgetPeriods)
        .set({ isDefault: false })
        .where(eq(budgetPeriods.organizationId, organizationId));
    }

    const [budgetPeriod] = await db
      .insert(budgetPeriods)
      .values({
        id: randomUUID(),
        organizationId,
        name: data.name,
        description: data.description || null,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        isDefault: data.isDefault || false,
        status: "draft",
      })
      .returning();

    revalidatePath("/accounting/budget");
    return { success: true, data: budgetPeriod };
  } catch (error) {
    console.error("Error creating budget period:", error);
    return { error: "Failed to create budget period" };
  }
} 