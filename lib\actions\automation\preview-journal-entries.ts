"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { BusinessAction } from "@/lib/actions/automation/types";
import { createJournalAutomationService } from "@/lib/actions/automation/journal-automation";

export async function previewJournalEntries(data: BusinessAction): Promise<{
  success: boolean;
  entries?: any[];
  error?: string;
}> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const journalService = await createJournalAutomationService(orgId);
    
    // Use the processBusinessAction method of the journal service
    const previewResult = await journalService.processBusinessAction(data);

    if (previewResult.success) {
      return {
        success: true,
        entries: previewResult.journalEntries
      };
    } else {
      return {
        success: false,
        error: previewResult.error
      };
    }
  } catch (error) {
    console.error("Error previewing journal entries:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 