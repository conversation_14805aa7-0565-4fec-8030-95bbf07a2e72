import {
    Body,
    Container,
    Head,
    Heading,
    Html,
    Img,
    Preview,
    Text,
  } from "@react-email/components";
  import * as React from "react";
  
  interface TwoFactorAuthEmailProps {
    userName: string;
    otpCode: string;
  }
  
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
  
  export const TwoFactorAuthEmail = ({
    userName,
    otpCode,
  }: TwoFactorAuthEmailProps) => (
    <Html>
      <Head />
      <Preview>Your Two-Factor Authentication Code for NextGen Business</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${baseUrl}/logo.png`} // Assuming you have a logo in your public folder
            width="40"
            height="33"
            alt="NextGen Business"
            style={logo}
          />
          <Heading style={heading}>Your 2FA Code</Heading>
          <Text style={paragraph}>Hello {userName},</Text>
          <Text style={paragraph}>
            Use the following code to complete your sign-in process. This code is valid for 5 minutes.
          </Text>
          <Container style={codeContainer}>
            <Text style={code}>{otpCode}</Text>
          </Container>
          <Text style={paragraph}>
            If you did not request this code, you can safely ignore this email.
          </Text>
          <Text style={paragraph}>
            — The NextGen Business team
          </Text>
        </Container>
      </Body>
    </Html>
  );
  
  export default TwoFactorAuthEmail;
  
  const main = {
    backgroundColor: "#ffffff",
    fontFamily:
      '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
  };
  
  const container = {
    margin: "0 auto",
    padding: "20px 0 48px",
    width: "580px",
  };
  
  const logo = {
    margin: "0 auto",
  };
  
  const heading = {
    fontSize: "28px",
    fontWeight: "bold",
    marginTop: "48px",
    textAlign: "center" as const,
  };
  
  const paragraph = {
    fontSize: "16px",
    lineHeight: "24px",
    color: "#484848",
  };

  const codeContainer = {
    border: "1px solid #eee",
    borderRadius: "5px",
    margin: "16px 0",
    padding: "10px",
    textAlign: "center" as const,
    backgroundColor: "#f4f4f4",
  };
  
  const code = {
    fontSize: "32px",
    fontWeight: "bold",
    letterSpacing: "4px",
    color: "#000",
  }; 