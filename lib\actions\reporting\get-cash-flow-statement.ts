"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { getBalanceSheet } from "./get-balance-sheet";
import { getIncomeStatement } from "./get-income-statement";

export async function getCashFlowStatement(fromDateStr?: string, toDateStr?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    const fromDate = fromDateStr ? new Date(fromDateStr) : new Date(new Date().getFullYear(), 0, 1);
    const toDate = toDateStr ? new Date(toDateStr) : new Date();

    // 1. Net Income
    const incomeStatement = await getIncomeStatement(fromDate.toISOString(), toDate.toISOString());
    let netCashFlow = incomeStatement.netIncome;

    // 2. Adjustments for non-cash items (e.g., depreciation)
    // This part is complex and requires tracking depreciation accounts, which we assume for now.
    // Placeholder for depreciation adjustment
    const depreciation = 0; 
    netCashFlow += depreciation;

    // 3. Changes in working capital
    const prevPeriodEndDate = new Date(fromDate.getTime() - ********); // Day before start date

    const currentBalanceSheet = await getBalanceSheet(toDate.toISOString());
    const previousBalanceSheet = await getBalanceSheet(prevPeriodEndDate.toISOString());
    
    const getChange = (assetName: string, current: any, previous: any) => {
        const currentAssets = current.assets.filter((a:any) => a.account.name.toLowerCase().includes(assetName));
        const previousAssets = previous.assets.filter((a:any) => a.account.name.toLowerCase().includes(assetName));
        const currentTotal = currentAssets.reduce((sum: number, a: any) => sum + a.balance, 0);
        const previousTotal = previousAssets.reduce((sum: number, a: any) => sum + a.balance, 0);
        return currentTotal - previousTotal;
    };
    
    const changeInAR = getChange('accounts receivable', currentBalanceSheet, previousBalanceSheet);
    const changeInInventory = getChange('inventory', currentBalanceSheet, previousBalanceSheet);
    const changeInAP = getChange('accounts payable', currentBalanceSheet, previousBalanceSheet);

    netCashFlow -= changeInAR; // Increase in AR is a use of cash
    netCashFlow -= changeInInventory; // Increase in Inventory is a use of cash
    netCashFlow += changeInAP; // Increase in AP is a source of cash

    const cashFromOperations = netCashFlow;

    // 4. Cash from Investing Activities (e.g., buying/selling assets) - Placeholder
    const cashFromInvesting = 0;

    // 5. Cash from Financing Activities (e.g., loans, equity) - Placeholder
    const cashFromFinancing = 0;

    // 6. Net change in cash
    const netChangeInCash = cashFromOperations + cashFromInvesting + cashFromFinancing;

    // 7. Beginning and End Cash Balance
    const cashAccounts = ['cash', 'checking', 'savings'];
    const getCashBalance = (bs: any) => {
        return bs.assets
            .filter((a:any) => cashAccounts.some(name => a.account.name.toLowerCase().includes(name)))
            .reduce((sum: number, a: any) => sum + a.balance, 0);
    };

    const beginningCash = getCashBalance(previousBalanceSheet);
    const endingCash = getCashBalance(currentBalanceSheet);

    return {
        operatingActivities: [], // Placeholder, add breakdown if available
        investingActivities: [], // Placeholder
        financingActivities: [], // Placeholder
        netCashFromOperating: cashFromOperations,
        netCashFromInvesting: cashFromInvesting,
        netCashFromFinancing: cashFromFinancing,
        netChangeInCash,
        beginningCash,
        endingCash,
        period: `From ${fromDate.toLocaleDateString()} to ${toDate.toLocaleDateString()}`
    };
} 