"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";

const remapAccountsSchema = z.object({
  remapRevenue: z.boolean().optional(),
  revenueAccountId: z.string().optional(),
  remapCogs: z.boolean().optional(),
  cogsAccountId: z.string().optional(),
  remapInventory: z.boolean().optional(),
  inventoryAccountId: z.string().optional(),
}).refine(data => data.remapRevenue || data.remapCogs || data.remapInventory, {
    message: "You must select at least one account type to remap.",
    path: ["remapRevenue"],
});

export type RemapAccountsFormData = z.infer<typeof remapAccountsSchema>;

interface BulkRemapAccountsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: RemapAccountsFormData) => void;
  isPending: boolean;
  accounts: Awaited<ReturnType<typeof getAccounts>>;
  productCount: number;
}

export function BulkRemapAccountsDialog({
  isOpen,
  onClose,
  onSubmit,
  isPending,
  accounts,
  productCount,
}: BulkRemapAccountsDialogProps) {
  const form = useForm<RemapAccountsFormData>({
    resolver: zodResolver(remapAccountsSchema),
    defaultValues: {
      remapRevenue: false,
      remapCogs: false,
      remapInventory: false,
    }
  });

  const watch = form.watch();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Bulk Remap Accounts</DialogTitle>
          <DialogDescription>
            Remap accounts for the {productCount} selected products.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="remapRevenue"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Remap Revenue Account
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                {watch.remapRevenue && (
                    <FormField
                        control={form.control}
                        name="revenueAccountId"
                        render={({ field }) => (
                            <FormItem>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger><SelectValue placeholder="Select a revenue account" /></SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {accounts.filter(a => a.type === 'revenue').map((account) => (
                                            <SelectItem key={account.id} value={account.id}>{account.name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        )}
                    />
                )}
            </div>

            <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="remapCogs"
                  render={({ field }) => (
                     <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Remap COGS Account
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                {watch.remapCogs && (
                    <FormField
                        control={form.control}
                        name="cogsAccountId"
                        render={({ field }) => (
                            <FormItem>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger><SelectValue placeholder="Select a COGS account" /></SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {accounts.filter(a => a.type === 'expense').map((account) => (
                                            <SelectItem key={account.id} value={account.id}>{account.name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        )}
                    />
                )}
            </div>

            <div className="space-y-2">
                 <FormField
                  control={form.control}
                  name="remapInventory"
                  render={({ field }) => (
                     <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Remap Inventory Account
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                {watch.remapInventory && (
                    <FormField
                        control={form.control}
                        name="inventoryAccountId"
                        render={({ field }) => (
                            <FormItem>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger><SelectValue placeholder="Select an inventory account" /></SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {accounts.filter(a => a.type === 'asset').map((account) => (
                                            <SelectItem key={account.id} value={account.id}>{account.name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        )}
                    />
                )}
            </div>
            
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Remapping..." : "Remap Accounts"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 