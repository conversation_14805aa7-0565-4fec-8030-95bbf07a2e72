"use server";

import { db } from "@/db/drizzle";
import { vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

export async function getVendorById(vendorId: string) {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  const vendor = await db.query.vendors.findFirst({
    where: and(eq(vendors.id, vendorId), eq(vendors.organizationId, orgId)),
  });
  if (!vendor) throw new Error("Vendor not found or you do not have permission to view it.");
  return vendor;
} 