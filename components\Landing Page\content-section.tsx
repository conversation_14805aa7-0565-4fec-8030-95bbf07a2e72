import { But<PERSON> } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'

export default function ContentSection() {
    return (
        <section className="py-16 md:py-32">
            <div className="mx-auto max-w-5xl px-6">
                <div className="grid gap-6 md:grid-cols-2 md:gap-12">
                    <h2 className="text-4xl font-medium">Not just a software, it's a partner for your success. That's what drives us every day.</h2>
                    <div className="space-y-6">
                        <p>Tired of Wrestling with Finances? You're Not Alone.</p>
                        <p>
                        As a small or medium-sized business owner, <span className="font-bold"> your passion lies in your work, not in endless financial tasks.</span> — Manual accounting, complex reports, and tracking inventory can steal valuable time and create unnecessary stress. If you're feeling overwhelmed by the numbers, NextGenBusiness is here to change that.
                        </p>
                        <Button asChild variant="secondary" size="sm" className="gap-1 pr-1.5">
                            <Link href="#">
                                <span>Learn More</span>
                                <ChevronRight className="size-2" />
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </section>
    )
}
