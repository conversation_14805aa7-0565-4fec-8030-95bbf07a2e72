"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { projects } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";

export async function deleteProjects(ids: string[]) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    if (ids.length === 0) {
      return { success: false, message: "No project IDs provided." };
    }

    const deletedCount = await db
      .delete(projects)
      .where(
        and(inArray(projects.id, ids), eq(projects.organizationId, orgId))
      );

    revalidatePath("/projects");
    return { success: true, message: `${ids.length} project(s) deleted.` };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to delete projects.",
    };
  }
} 