"use client";

import React, { useEffect, useTransition } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { billFormSchema, NewBill } from "@/lib/actions/bills/bill.schema";
import { getBillDetails } from "@/lib/actions/bills/get-bill-details";
import { getBillById } from "@/lib/actions/bills/get-bill-by-id";
import { toast } from "sonner";
import { Plus, Trash, FileText, User, Calendar, DollarSign, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getVendors } from "@/lib/actions/vendors/get-vendors";
import { getProducts } from "@/lib/actions/products/get-products";
import { getBills } from "@/lib/actions/bills/get-bills";
import { refundSupplierCredit } from "@/lib/actions/bills/refund-supplier-credit";

const STATUS_OPTIONS = ["draft", "submitted", "approved", "paid", "void"] as const;

interface Product { 
  id: string; 
  name: string; 
}

interface AddBillSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (billData: NewBill) => void;
  isPending: boolean;
  billId?: string | null;
}

export default function AddBillSheet({ 
  open, 
  onOpenChange, 
  onSubmit, 
  isPending, 
  billId 
}: AddBillSheetProps) {
  const [isFetchingDetails, startFetching] = useTransition();
  const [supplierCredit, setSupplierCredit] = React.useState<number>(0);
  const [appliedCredit, setAppliedCredit] = React.useState<number>(0);

  // Fetch bill details if editing
  const isEdit = !!billId;
  const { data: bill, isLoading: isLoadingBill } = useQuery({
    queryKey: ["bill", billId],
    queryFn: () => billId ? getBillById(billId) : null,
    enabled: !!billId && open,
  });

  // Fetch vendors
  const { data: vendors = [], isLoading: isLoadingVendors } = useQuery({
    queryKey: ["vendors"],
    queryFn: getVendors,
  });

  // Fetch products
  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
  });

  const today = new Date();
  const defaultDueDate = new Date();
  defaultDueDate.setDate(today.getDate() + 30); // 30 days from today

  const {
    register,
    handleSubmit,
    control,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<NewBill>({
    resolver: zodResolver(billFormSchema),
    defaultValues: {
      date: today,
      dueDate: defaultDueDate,
      status: "draft",
      lineItems: [{ description: "", quantity: 1, unitPrice: 0, totalPrice: 0 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "lineItems",
  });

  useEffect(() => {
    if (open && bill) {
      startFetching(async () => {
        const result = await getBillDetails(bill.id);
        if (result.success && result.data) {
          const billData = result.data;
          // Omit fields not in the bill form schema (like overpaid)
          const { overpaid, ...formSafeBillData } = billData;
          reset({
            ...formSafeBillData,
            status: billData.status ?? 'draft',
            date: billData.date ? new Date(billData.date) : today,
            dueDate: billData.dueDate ? new Date(billData.dueDate) : defaultDueDate,
            tax: typeof billData.tax === 'number' ? billData.tax : undefined,
            lineItems: billData.lineItems.map(item => ({
              productId: typeof item.productId === 'string' ? item.productId : undefined,
              description: item.description,
              quantity: item.quantity,
              unitPrice: Number(item.unitPrice),
              totalPrice: Number((item as any).totalPrice ?? 0),
            }))
          });
        } else {
          toast.error(result.message || "Failed to fetch bill details.");
          onOpenChange(false);
        }
      });
    } else if (open && !bill) {
      reset({
        vendorId: "",
        billNumber: "",
        date: today,
        dueDate: defaultDueDate,
        status: "draft",
        lineItems: [{ description: "", quantity: 1, unitPrice: 0, totalPrice: 0, productId: undefined }],
      });
    }
  }, [bill, open, reset, onOpenChange]);

  // Watch for vendor selection and fetch overpaid credit
  useEffect(() => {
    const vendorId = watch("vendorId");
    if (vendorId) {
      getBills().then((allBills) => {
        const credit = allBills
          .filter(bill => bill.vendorId === vendorId && Number(bill.overpaid) > 0)
          .reduce((sum, bill) => sum + Number(bill.overpaid), 0);
        setSupplierCredit(credit);
        setAppliedCredit(0);
      });
    } else {
      setSupplierCredit(0);
      setAppliedCredit(0);
    }
  }, [watch("vendorId")]);

  const handleApplyCredit = () => {
    setAppliedCredit(supplierCredit > total ? total : supplierCredit);
  };

  const handleRefundCredit = async () => {
    const vendorId = watch("vendorId");
    if (!vendorId || supplierCredit <= 0) return;
    const result = await refundSupplierCredit(vendorId, supplierCredit);
    toast.success(result.message);
    // Refresh available credit
    getBills().then((allBills) => {
      const credit = allBills
        .filter(bill => bill.vendorId === vendorId && Number(bill.overpaid) > 0)
        .reduce((sum, bill) => sum + Number(bill.overpaid), 0);
      setSupplierCredit(credit);
      setAppliedCredit(0);
    });
  };

  const lineItems = watch("lineItems");
  const total = lineItems.reduce(
    (sum: number, item: { quantity: number; unitPrice: number }) => sum + (item.quantity || 0) * (item.unitPrice || 0),
    0
  );
  
  const formatDateForInput = (date: Date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "submitted":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "paid":
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300";
      case "void":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const handleSheetClose = (open: boolean) => {
    if (!isPending && !isFetchingDetails) {
      onOpenChange(open);
    }
  };

  return (
    <Sheet open={open} onOpenChange={handleSheetClose}>
      <SheetContent 
        side="right" 
        className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
        aria-describedby="bill-form-description"
      >
        <SheetHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
          <SheetTitle className="text-lg sm:text-xl md:text-2xl flex items-center gap-2">
            <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
            {isEdit ? "Edit Bill" : "Create New Bill"}
          </SheetTitle>
          <SheetDescription id="bill-form-description">
            {isEdit ? "Modify the bill details below" : "Fill out the form below to create a new bill"}
          </SheetDescription>
        </SheetHeader>

        {isFetchingDetails ? (
          <div className="flex items-center justify-center py-12 sm:py-16">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 sm:ml-3 text-sm">Loading bill details...</span>
          </div>
        ) : (
          <div className="flex-1 overflow-y-auto">
            <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col">
              <div className="flex-1 px-4 pb-4 sm:px-6 sm:pb-6 space-y-4 sm:space-y-6 mt-4 sm:mt-6">
                {/* Supplier Credit Card */}
                {supplierCredit > 0 && (
                  <Card className="mb-4 border-green-300 bg-green-50">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-green-700 text-base flex items-center gap-2">
                        Supplier Credit Available
                        <Badge className="bg-green-100 text-green-800 border-green-300">${supplierCredit.toFixed(2)}</Badge>
                      </CardTitle>
                      <CardDescription className="text-green-700 text-xs">
                        You have overpaid previous bills to this vendor. You can apply this credit to reduce the amount due or refund it.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex gap-2">
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        disabled={appliedCredit > 0}
                        onClick={handleApplyCredit}
                      >
                        Apply Credit
                      </Button>
                      <Button
                        type="button"
                        size="sm"
                        variant="destructive"
                        onClick={handleRefundCredit}
                        className="ml-2"
                      >
                        Refund Credit
                      </Button>
                      {appliedCredit > 0 && (
                        <span className="ml-4 text-green-700 font-semibold">Applied: ${appliedCredit.toFixed(2)}</span>
                      )}
                    </CardContent>
                  </Card>
                )}
                
                {/* Bill Information Card */}
                <Card className="shadow-sm">
                  <CardHeader className="pb-3 sm:pb-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                      <CardTitle className="text-base sm:text-lg">Bill Information</CardTitle>
                    </div>
                    <CardDescription className="text-xs sm:text-sm">
                      Enter the basic bill details and vendor information
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3 sm:space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                      {/* Vendor Selection */}
                      <div className="space-y-1 sm:space-y-2">
                        <Label className="text-xs sm:text-sm font-medium">Vendor *</Label>
                        <Select 
                          onValueChange={(value) => setValue("vendorId", value)} 
                          value={watch("vendorId")}
                          disabled={isLoadingVendors}
                        >
                          <SelectTrigger className="h-8 sm:h-10">
                            <SelectValue placeholder="Select vendor" />
                          </SelectTrigger>
                          <SelectContent>
                            {vendors.map((vendor) => (
                              <SelectItem key={vendor.id} value={vendor.id}>
                                {vendor.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.vendorId && (
                          <p className="text-red-500 text-xs">{errors.vendorId.message}</p>
                        )}
                      </div>

                      {/* Bill Number */}
                      <div className="space-y-1 sm:space-y-2">
                        <Label className="text-xs sm:text-sm font-medium">Bill Number *</Label>
                        <Input 
                          {...register("billNumber")} 
                          className="h-8 sm:h-10"
                          placeholder="Enter bill number"
                        />
                        {errors.billNumber && (
                          <p className="text-red-500 text-xs">{errors.billNumber.message}</p>
                        )}
                      </div>

                      {/* Date */}
                      <div className="space-y-1 sm:space-y-2">
                        <Label className="text-xs sm:text-sm font-medium">Date *</Label>
                        <Input 
                          type="date" 
                          value={formatDateForInput(watch("date") instanceof Date ? watch("date") : today)}
                          onChange={(e) => setValue("date", new Date(e.target.value), { shouldValidate: true })}
                          className="h-8 sm:h-10"
                        />
                        {errors.date && (
                          <p className="text-red-500 text-xs">{errors.date.message}</p>
                        )}
                      </div>

                      {/* Due Date */}
                      <div className="space-y-1 sm:space-y-2">
                        <Label className="text-xs sm:text-sm font-medium">Due Date *</Label>
                        <Input 
                          type="date" 
                          value={formatDateForInput(watch("dueDate") || defaultDueDate)}
                          onChange={(e) => setValue("dueDate", new Date(e.target.value), { shouldValidate: true })}
                          className="h-8 sm:h-10"
                        />
                        {errors.dueDate && (
                          <p className="text-red-500 text-xs">{errors.dueDate.message}</p>
                        )}
                      </div>

                      {/* Status */}
                      <div className="space-y-1 sm:space-y-2 md:col-span-2">
                        <Label className="text-xs sm:text-sm font-medium">Status</Label>
                        <div className="flex items-center gap-2">
                          <Select 
                            onValueChange={(value) => setValue("status", value as typeof STATUS_OPTIONS[number])} 
                            value={watch("status")}
                          >
                            <SelectTrigger className="h-8 sm:h-10 w-auto min-w-32">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              {STATUS_OPTIONS.map(status => (
                                <SelectItem key={status} value={status}>
                                  <span className="capitalize">{status}</span>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Badge className={`${getStatusBadgeColor(watch("status"))} text-xs px-2 py-1`}>
                            {watch("status")}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Line Items Card */}
                <Card className="shadow-sm">
                  <CardHeader className="pb-3 sm:pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                        <CardTitle className="text-base sm:text-lg">Line Items</CardTitle>
                      </div>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => append({ description: "", quantity: 1, unitPrice: 0, totalPrice: 0, productId: undefined })}
                        className="h-7 sm:h-8 px-2 sm:px-3"
                      >
                        <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        <span className="text-xs sm:text-sm">Add Item</span>
                      </Button>
                    </div>
                    <CardDescription className="text-xs sm:text-sm">
                      Add items and services for this bill
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {/* Line Items Header - Desktop Only */}
                    <div className="hidden md:grid md:grid-cols-12 gap-2 text-xs font-medium text-gray-500 dark:text-gray-400 pb-2 border-b">
                      <div className="col-span-5">Description</div>
                      <div className="col-span-2 text-center">Quantity</div>
                      <div className="col-span-2 text-center">Unit Price</div>
                      <div className="col-span-2 text-right">Total</div>
                      <div className="col-span-1"></div>
                    </div>

                    {/* Line Items */}
                    <div className="space-y-3">
                      {fields.map((field, index) => (
                        <div key={field.id} className="grid grid-cols-1 md:grid-cols-12 gap-2 sm:gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border">
                          {/* Description */}
                          <div className="md:col-span-5 space-y-1">
                            <Label className="text-xs text-gray-600 md:hidden">Description</Label>
                            <Input 
                              {...register(`lineItems.${index}.description`)} 
                              placeholder="Item description"
                              className="h-8 sm:h-9"
                            />
                          </div>

                          {/* Quantity */}
                          <div className="md:col-span-2 space-y-1">
                            <Label className="text-xs text-gray-600 md:hidden">Quantity</Label>
                            <Input 
                              type="number" 
                              {...register(`lineItems.${index}.quantity`, { valueAsNumber: true })} 
                              placeholder="Qty"
                              className="h-8 sm:h-9"
                              min="0"
                              step="1"
                            />
                          </div>

                          {/* Unit Price */}
                          <div className="md:col-span-2 space-y-1">
                            <Label className="text-xs text-gray-600 md:hidden">Unit Price</Label>
                            <Input 
                              type="number" 
                              {...register(`lineItems.${index}.unitPrice`, { valueAsNumber: true })} 
                              placeholder="0.00"
                              className="h-8 sm:h-9"
                              min="0"
                              step="0.01"
                            />
                          </div>

                          {/* Total */}
                          <div className="md:col-span-2 space-y-1">
                            <Label className="text-xs text-gray-600 md:hidden">Total</Label>
                            <div className="h-8 sm:h-9 flex items-center justify-end text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100">
                              ${((lineItems[index]?.quantity || 0) * (lineItems[index]?.unitPrice || 0)).toFixed(2)}
                            </div>
                          </div>

                          {/* Delete Button */}
                          <div className="md:col-span-1 flex justify-end md:justify-center">
                            <Button 
                              type="button" 
                              variant="ghost" 
                              size="sm"
                              onClick={() => remove(index)} 
                              disabled={fields.length === 1}
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                            >
                              <Trash className="h-3 w-3 sm:h-4 sm:w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Total Summary */}
                    <Separator />
                    <div className="flex justify-between items-center pt-2">
                      <span className="text-sm sm:text-base font-medium text-gray-600 dark:text-gray-400">
                        Total Amount:
                      </span>
                      <span className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100">
                        ${appliedCredit > 0 ? (total - appliedCredit).toFixed(2) : total.toFixed(2)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Action Buttons */}
              <div className="border-t bg-gray-50 dark:bg-gray-900/50 px-4 py-3 sm:px-6 sm:py-4">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 sm:justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={isPending || isFetchingDetails}
                    className="flex-1 sm:flex-none px-4 py-2 text-sm"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isPending || isFetchingDetails}
                    className="flex-1 sm:flex-none px-4 py-2 text-sm"
                  >
                    {isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        {isEdit ? "Update Bill" : "Create Bill"}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
} 