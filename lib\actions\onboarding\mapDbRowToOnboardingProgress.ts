import { OnboardingProgress } from "@/lib/onboarding-constants";

/**
 * Maps a database row from the onboarding table to the OnboardingProgress type.
 * Use this to ensure consistent mapping of onboarding progress data.
 *
 * @param row The raw DB row
 * @returns OnboardingProgress
 */
export function mapDbRowToOnboardingProgress(row: any): OnboardingProgress {
  return {
    id: row.id,
    userId: row.userId,
    step: row.step,
    totalSteps: row.totalSteps ?? 6,
    welcomeData: row.orgBasics,
    businessTypeData: row.businessDetails,
    organizationData: row.orgBasics,
    addressData: row.orgDetails,
    contactData: row.orgDetails,
    accountsData: null, // No direct mapping, set to null or map if available
    subscriptionData: row.subscription,
    completionData: { isCompleted: row.isCompleted, completedAt: row.completedAt },
    lastActiveStep: row.step ?? 1,
    isCompleted: row.isCompleted ?? false,
    createdAt: row.createdAt ?? new Date(),
    updatedAt: row.updatedAt ?? new Date(),
  };
} 