import { getProductById } from "@/lib/actions/products/get-product-by-id";
import { notFound } from "next/navigation";
import { ProductDetailClient } from "@/components/protectedPages/Products/ProductDetailClient";
import { CostCalculationService } from "@/lib/services/cost-calculation.service";
import { getProductTrends } from "@/lib/actions/products/get-product-trends";
import { getServerUserContext } from "@/lib/server-auth";

interface ProductDetailPageProps {
  params: {
    productId: string;
  };
}

export default async function ProductDetailPage({
  params,
}: ProductDetailPageProps) {
  const { organization } = await getServerUserContext();
  const product = await getProductById(params.productId);

  if (!product) {
    notFound();
  }

  const costService = new CostCalculationService(organization.id);
  const costData = await costService.getProductCost(product.id, {});
  const trendsResult = await getProductTrends(product.id);
  const trendData = trendsResult.success ? trendsResult.data : [];

  return (
    <ProductDetailClient
      product={product}
      costData={costData}
      trendData={trendData}
    />
  );
} 