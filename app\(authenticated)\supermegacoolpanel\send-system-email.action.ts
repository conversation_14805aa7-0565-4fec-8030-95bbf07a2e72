"use server";
import { auth } from "@/lib/auth";
import { sendSystemEmail } from "@/lib/actions/superadmin";
import { headers } from "next/headers";

export async function sendSystemEmailAction(formData: FormData) {
  const userId = formData.get("userId") as string;
  const subject = formData.get("subject") as string;
  const body = formData.get("body") as string;
  const session = await auth.api.getSession({ headers: await headers() });
  return sendSystemEmail(session, userId, subject, body);
} 