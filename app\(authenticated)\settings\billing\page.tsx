import { BillingPageClient } from "./BillingPageClient";
import { getServerUserContext } from "@/lib/server-auth";
import { redirect } from 'next/navigation';
import { enforcePermission } from "@/lib/permissions";

export default async function BillingPage() {
  await enforcePermission("org:billing:manage");
  const { organization } = await getServerUserContext();

  if (!organization) {
    // This case should theoretically be handled by getServerUserContext's redirects,
    // but as a fallback, we'll redirect.
    redirect('/organizations');
  }
  
  return <BillingPageClient organization={organization} />;
} 