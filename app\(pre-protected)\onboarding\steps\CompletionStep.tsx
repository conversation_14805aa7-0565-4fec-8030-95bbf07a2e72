"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, ArrowRight, Users, BarChart3, Shield } from "lucide-react";

interface CompletionStepProps {
  onboardingData: any;
  onNext: (data: any) => void;
  isLoading: boolean;
}

export function CompletionStep({ onboardingData, onNext, isLoading }: CompletionStepProps) {
  return (
    <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6 md:space-y-8 px-4 sm:px-6">
      {/* Success Header */}
      <div className="text-center space-y-4 sm:space-y-6">
        <div className="flex justify-center">
          <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
            <CheckCircle className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-white" />
          </div>
        </div>
        
        <div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4">
            🎉 Welcome to your NextGen Business dashboard!
          </h1>
          <p className="text-base sm:text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">
            <strong>{onboardingData?.organizationName}</strong> is now set up for worry-free management. 
            Focus on what you do best while we handle the complexity.
          </p>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
        <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border-blue-500/30">
          <CardContent className="p-4 sm:p-6 text-center space-y-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
              <BarChart3 className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h3 className="font-semibold text-sm sm:text-base text-blue-300">Smart Analytics</h3>
            <p className="text-xs sm:text-sm text-blue-200">
              Real-time insights into your business performance
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/20 to-green-600/20 border-green-500/30">
          <CardContent className="p-4 sm:p-6 text-center space-y-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto">
              <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h3 className="font-semibold text-sm sm:text-base text-green-300">Secure & Reliable</h3>
            <p className="text-xs sm:text-sm text-green-200">
              Bank-level security for your financial data
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border-purple-500/30">
          <CardContent className="p-4 sm:p-6 text-center space-y-3">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto">
              <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h3 className="font-semibold text-sm sm:text-base text-purple-300">Team Collaboration</h3>
            <p className="text-xs sm:text-sm text-purple-200">
              Work together seamlessly across your organization
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Ready Message */}
      <Card className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 border-blue-500/30">
        <CardContent className="p-4 sm:p-6 md:p-8 text-center space-y-3 sm:space-y-4">
          <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-white">
            Your business is ready to take off! 🚀
          </h2>
          <p className="text-sm sm:text-base text-gray-300 max-w-2xl mx-auto">
            Everything is configured and ready to go. Start managing your business with confidence, 
            knowing that NextGen Business has your back every step of the way.
          </p>
          
          <Button 
            onClick={() => {
              onNext(onboardingData);
            }}
            disabled={isLoading}
            size="lg"
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 sm:px-8 py-2.5 sm:py-3 text-base sm:text-lg font-semibold w-full sm:w-auto mt-4 sm:mt-6"
          >
            {isLoading ? "Setting up..." : "Go to Dashboard"}
            <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
          </Button>
          
          <p className="text-xs sm:text-sm text-gray-400 mt-3">
            Ready to transform how you manage your business
          </p>
        </CardContent>
      </Card>
    </div>
  );
} 