"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CheckCircle, Mail } from "lucide-react";

export default function WaitlistPage() {
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Integrate with backend/email service
    setSubmitted(true);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center px-4 py-12">
      <div className="max-w-2xl w-full">
        <Card className="shadow-xl border-primary/20">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-primary mb-2">
              NextGen Business
            </CardTitle>
            <p className="text-lg text-muted-foreground mb-4">
              All-in-One Business Management Platform
            </p>
            <p className="text-base text-foreground">
              NextGen Business is a modern, intelligent business management platform designed to make financial management, project tracking, and team collaboration effortless - even for users with no accounting background. It automates complex accounting, provides real-time insights, and empowers business owners, freelancers, nonprofits and teams to focus on growth, not paperwork.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 my-8">
              <ol className="grid gap-6 text-base text-foreground list-decimal list-inside [--list-marker-color:theme(colors.primary)] marker:text-primary">
                <li>
                  <span className="font-semibold text-primary">Effortless Financial Management</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>Automated accounting - no accounting jargon, just record what happened, and the system handles the rest.</li>
                    <li>Smart transaction categorization - no need to manually categorize transactions after an intiuitive initial setup.</li>
                    <li>Custom chart of accounts - Tailor your financial structure to your business needs or use our industry-ready templates</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">Invoicing</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>Create, send, and manage invoices with custom branding</li>
                    <li>Automated invoice management and reconciliation - Link transactions to invoices and clients for seamless reconciliation.</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">Products & Services Intelligence</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                  <li>Track physical and digital products, link to accounts, and monitor profitability.</li>
                    <li>Profitability Analytics - Real-time margin tracking, cost of goods sold, and performance metrics.</li>
                    <li>Industry benchmarking and performance metrics</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">Project & Client Management</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>Integrated project hub with financials and budgets</li>
                    <li>CRM for clients and vendors</li>
                    <li>Easily track your finances related to projects and clients</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">Advanced Reporting & Analytics</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>Instantly see your business health with income, expense, and cash flow trends.</li>
                    <li>Standard & advanced financial reports ready on a click of a button</li>
                    <li>Export options: PDF and CSV</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">Team & Organization Management</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>Multi-organization support - Manage multiple businesses or clients from one account.</li>
                    <li>Granular permissions and role-based access - Control who can access what.</li>
                    <li>Team management and usage tracking - Track team member activity and usage.</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">Security & Compliance</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>Enterprise-grade security and audit logging - Protect your data with advanced security features.</li>
                    <li>Two-factor authentication (2FA) - Secure your account with an extra layer of protection.</li>
                    <li>Easy data export for compliance - Export your data in PDF or CSV format for compliance.</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold text-primary">International & Multilingual</span>
                  <ul className="list-disc list-inside ml-5 mt-1">
                    <li>English, Bosnian, and German support - Your language, your data.</li>
                    <li>Localized currency and date formatting - Format your data to your locale.</li>
                  </ul>
                </li>
              </ol>
            </div>
            <div className="my-8 text-center">
              <h2 className="text-2xl font-bold mb-2 text-primary">Join the Waitlist</h2>
              <p className="text-base text-muted-foreground mb-4">
                Be the first to experience NextGen Business. Get early access, shape the product, and unlock exclusive offers!
              </p>
              {submitted ? (
                <div className="flex flex-col items-center gap-2">
                  <CheckCircle className="w-10 h-10 text-green-500 mb-2" />
                  <span className="text-lg font-semibold text-green-600">Thank you! You're on the list.</span>
                  <span className="text-sm text-muted-foreground">We'll keep you updated with early access and special offers.</span>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row items-center gap-3 justify-center mt-4">
                  <div className="relative w-full sm:w-auto">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground w-5 h-5" />
                    <Input
                      type="email"
                      required
                      placeholder="Your email address"
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full sm:w-72 bg-card border border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary"
                    />
                  </div>
                  <Button type="submit" size="lg" className="w-full sm:w-auto mt-2 sm:mt-0">
                    Join Waitlist
                  </Button>
                </form>
              )}
            </div>
            <div className="mt-8 text-center text-sm text-muted-foreground">
              <span>Ready to simplify your business and supercharge your growth? <span className="text-primary font-semibold">Join the waitlist and get early access to NextGen Business!</span></span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 