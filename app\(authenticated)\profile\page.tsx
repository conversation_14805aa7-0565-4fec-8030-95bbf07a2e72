"use client";

import React, { useState, useEffect, useRef } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  changeUserPassword,
  resendVerificationEmail,
  updateUserCustomFields,
  updateUserName,
  uploadAvatarAction,
  getCurrentUserInfo,
} from "@/lib/actions/user";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { useRouter } from "next/navigation";
import { authClient } from "@/lib/auth-client";

const profileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  jobTitle: z.string().optional(),
  phone: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

const passwordSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required."),
    newPassword: z
      .string()
      .min(8, "New password must be at least 8 characters."),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type PasswordFormData = z.infer<typeof passwordSchema>;

export default function ProfilePage() {
  const [user, setUser] = useState<any>(null);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [signOutCountdown, setSignOutCountdown] = useState<number | null>(null);
  const signOutTimerRef = useRef<NodeJS.Timeout | null>(null);
  const router = useRouter();

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      jobTitle: "",
      phone: "",
    },
  });

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    const fetchUser = async () => {
      const userData = await getCurrentUserInfo();
      if (userData) {
        setUser(userData);
        profileForm.reset({
          name: userData.name || "",
          jobTitle: userData.jobTitle || "",
          phone: userData.phone || "",
        });
      }
    };
    fetchUser();
  }, [profileForm]);

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    const formData = new FormData();
    formData.append("avatar", file);
    formData.append("userId", user.id);

    const result = await uploadAvatarAction(formData);
    if (result.error) {
      toast.error(result.error);
    } else {
      toast.success(result.success);
      setUser((prev: any) => ({ ...prev, image: result.newImageUrl }));
    }
  };

  const onProfileSubmit = async (data: ProfileFormData) => {
    if (!user) return;
    const updateNamePromise = updateUserName(user.id, data.name);
    const updateCustomFieldsPromise = updateUserCustomFields(
      user.id,
      data.jobTitle || "",
      data.phone || ""
    );

    const [nameResult, customFieldsResult] = await Promise.all([
      updateNamePromise,
      updateCustomFieldsPromise,
    ]);

    if (nameResult.error || customFieldsResult.error) {
      toast.error(nameResult.error || customFieldsResult.error || "Failed to update profile.");
    } else {
      toast.success("Profile updated successfully!");
      setUser((prev: any) => ({ ...prev, ...data }));
    }
  };

  const onPasswordSubmit = async (data: PasswordFormData) => {
    if (!user) return;
    const result = await changeUserPassword(
      user.id,
      data.currentPassword,
      data.newPassword
    );

    if (result.error) {
      toast.error(result.error);
    } else {
      toast.success(
        "Password changed successfully! You will be logged out in 5 seconds."
      );
      setSignOutCountdown(5);
    }
  };

  useEffect(() => {
    if (signOutCountdown === null) return;
    if (signOutCountdown === 0) {
      authClient.signOut().then(() => {
        router.push("/signin");
      });
      return;
    }
    signOutTimerRef.current = setTimeout(
      () => setSignOutCountdown((c) => (c !== null ? c - 1 : null)),
      1000
    );
    return () => {
      if (signOutTimerRef.current) clearTimeout(signOutTimerRef.current);
    };
  }, [signOutCountdown, router]);

  if (!user) {
    return (
      <div className="flex justify-center items-center h-screen">
        Loading...
      </div>
    );
  }
  
  const completionPercent =
    ((user.name ? 1 : 0) +
      (user.emailVerified ? 1 : 0) +
      (user.image ? 1 : 0) +
      (user.jobTitle ? 1 : 0) +
      (user.phone ? 1 : 0)) /
    5 * 100;


  return (
    <div className="container mx-auto max-w-2xl py-8">
      <h1 className="text-2xl font-bold mb-6">Your Profile</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Profile Completion</CardTitle>
          <CardDescription>
            Complete your profile to get the most out of our platform.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Progress value={completionPercent} className="w-full" />
            <span className="text-sm font-medium">{completionPercent}%</span>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Avatar</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center gap-4">
          <Avatar className="h-20 w-20">
            <AvatarImage src={user.image || ""} alt={user.name} />
            <AvatarFallback>{user.name?.[0]}</AvatarFallback>
          </Avatar>
          <div>
            <Label htmlFor="avatar-upload" className="cursor-pointer">
              <Button asChild>
                <span>Change Avatar</span>
              </Button>
            </Label>
            <Input
              id="avatar-upload"
              type="file"
              className="hidden"
              onChange={handleAvatarUpload}
              accept="image/png, image/jpeg, image/webp"
            />
            <p className="text-xs text-muted-foreground mt-2">
              JPG, PNG, or WEBP. 10MB max.
            </p>
          </div>
        </CardContent>
      </Card>

      <Form {...profileForm}>
        <form
          onSubmit={profileForm.handleSubmit(onProfileSubmit)}
          className="space-y-8"
        >
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={profileForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Your full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormItem>
                <FormLabel>Email</FormLabel>
                <Input value={user.email} disabled />
                {!user.emailVerified && (
                  <Alert variant="destructive" className="mt-2">
                    <Terminal className="h-4 w-4" />
                    <AlertTitle>Email not verified!</AlertTitle>
                    <AlertDescription>
                      Please check your inbox to verify your email.{" "}
                      <Button
                        variant="link"
                        className="p-0 h-auto"
                        onClick={async () => {
                          await resendVerificationEmail();
                          toast.info("Verification email sent!");
                        }}
                      >
                        Resend
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}
              </FormItem>
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                disabled={profileForm.formState.isSubmitting}
              >
                {profileForm.formState.isSubmitting
                  ? "Saving..."
                  : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Work Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={profileForm.control}
                name="jobTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., CEO, Developer" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="Your phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                disabled={profileForm.formState.isSubmitting}
              >
                {profileForm.formState.isSubmitting
                  ? "Saving..."
                  : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Security</CardTitle>
        </CardHeader>
        <CardContent>
          <Dialog
            open={isPasswordModalOpen}
            onOpenChange={setIsPasswordModalOpen}
          >
            <DialogTrigger asChild>
              <Button variant="outline">Change Password</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Change Your Password</DialogTitle>
              </DialogHeader>
              <Form {...passwordForm}>
                <form
                  onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={passwordForm.control}
                    name="currentPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Password</FormLabel>
                        <FormControl>
                          <Input type="password" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={passwordForm.control}
                    name="newPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Password</FormLabel>
                        <FormControl>
                          <Input type="password" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={passwordForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm New Password</FormLabel>
                        <FormControl>
                          <Input type="password" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button variant="ghost">Cancel</Button>
                    </DialogClose>
                    <Button
                      type="submit"
                      disabled={passwordForm.formState.isSubmitting}
                    >
                      {passwordForm.formState.isSubmitting
                        ? "Changing..."
                        : "Change Password"}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    </div>
  );
} 