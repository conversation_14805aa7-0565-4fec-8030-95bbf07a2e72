"use client";

import { <PERSON>, TableBody, Table<PERSON>ell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>rollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  type ColumnDef,
  useReactTable,
  getCoreRowModel,
  flexRender,
  RowSelectionState,
  getSortedRowModel,
  SortingState,
  getFilteredRowModel,
  ColumnFiltersState,
} from "@tanstack/react-table";
import * as React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { ChevronRight, ChevronDown } from "lucide-react";
import { accountGlobalFilterFn } from "./columns";
import { Badge } from "@/components/ui/badge";

interface AccountsDataTableProps<TData extends { accountNumber?: string; name?: string; description?: string }, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  rowSelection: RowSelectionState;
  setRowSelection: React.Dispatch<React.SetStateAction<RowSelectionState>>;
  sorting: SortingState;
  setSorting: React.Dispatch<React.SetStateAction<SortingState>>;
  openEditDialog: (row: TData) => void;
  openDeleteDialog: (ids: string[]) => void;
}

export function AccountsDataTable<TData extends { id: string; parentId?: string | null; isHeader?: boolean; level?: number; accountNumber?: string; name?: string; description?: string; status?: string }, TValue>({
  columns,
  data,
  rowSelection,
  setRowSelection,
  sorting,
  setSorting,
  openEditDialog,
  openDeleteDialog,
}: AccountsDataTableProps<TData, TValue>) {
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [expanded, setExpanded] = React.useState<Record<string, boolean>>({});

  // Build a tree from the flat data
  const tree = React.useMemo(() => {
    const map: Record<string, TData & { children?: TData[] }> = {};
    const roots: (TData & { children?: TData[] })[] = [];
    data.forEach(acc => {
      map[acc.id] = { ...acc, children: [] };
    });
    data.forEach(acc => {
      if (acc.parentId && map[acc.parentId]) {
        map[acc.parentId].children!.push(map[acc.id]);
      } else {
        roots.push(map[acc.id]);
      }
    });
    // Recursively sort children arrays by accountNumber
    function sortTree(nodes: (TData & { children?: TData[] })[]) {
      nodes.sort((a, b) => Number(a.accountNumber) - Number(b.accountNumber));
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          sortTree(node.children);
        }
      });
    }
    sortTree(roots);
    return roots;
  }, [data]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      rowSelection,
      sorting,
      columnFilters,
      globalFilter,
    },
    globalFilterFn: accountGlobalFilterFn,
  });

  // Recursive row renderer
  function renderRows(nodes: (TData & { children?: TData[] })[], depth = 0): React.ReactNode[] {
    // Build a map of id -> real TanStack Table row
    const realRowMap = new Map<string, any>(
      table.getRowModel().rows.map(r => [r.original.id, r])
    );
    return nodes.flatMap(node => {
      const hasChildren = node.children && node.children.length > 0;
      const isExpanded = expanded[node.id] ?? true;
      const row = (
        <TableRow key={node.id} className="hover:bg-muted/50">
          {columns.map((col, colIdx) => {
            const colKey = (col as any).accessorKey || col.id || colIdx;
            // Selection column: use real TanStack Table row
            if (col.id === "select" && realRowMap.has(node.id)) {
              const realRow = realRowMap.get(node.id);
              return (
                <TableCell
                  key={colKey}
                  className="py-2 whitespace-nowrap"
                  style={{ minWidth: getColumnMinWidth(colKey) }}
                >
                  <input
                    type="checkbox"
                    checked={realRow?.getIsSelected?.() ?? false}
                    onChange={() => realRow?.toggleSelected?.()}
                    aria-label="Select row"
                  />
                </TableCell>
              );
            }
            // Status column: render badge inline
            if (col.id === "status") {
              return (
                <TableCell key={colKey} className="py-2 whitespace-nowrap" style={{ minWidth: getColumnMinWidth(colKey) }}>
                  <span className="inline-block"><Badge>{node.status}</Badge></span>
                </TableCell>
              );
            }
            // Actions column: render Edit/Delete buttons inline
            if (col.id === "actions") {
              return (
                <TableCell key={colKey} className="py-2 whitespace-nowrap text-right" style={{ minWidth: getColumnMinWidth(colKey) }}>
                  <div className="flex gap-2 justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditDialog(node)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openDeleteDialog([node.id])}
                    >
                      Delete
                    </Button>
                  </div>
                </TableCell>
              );
            }
            // Only indent the first non-selection column
            if (colIdx === 0 || (colIdx === 1 && columns[0].id === "select")) {
              return (
                <TableCell
                  key={colKey}
                  className="py-2 whitespace-nowrap"
                  style={{ minWidth: getColumnMinWidth(colKey) }}
                >
                  <div style={{ paddingLeft: `${depth * 24}px` }} className="flex items-center gap-1">
                    {hasChildren ? (
                      <button
                        type="button"
                        aria-label={isExpanded ? "Collapse" : "Expand"}
                        onClick={() => setExpanded(e => ({ ...e, [node.id]: !isExpanded }))}
                        className="mr-1"
                      >
                        {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                      </button>
                    ) : null}
                    {colKey !== "children" ? (node[colKey as keyof typeof node] as React.ReactNode) ?? null : null}
                  </div>
                </TableCell>
              );
            }
            // Other columns: render value directly
            return (
              <TableCell
                key={colKey}
                className="py-2 whitespace-nowrap"
                style={{ minWidth: getColumnMinWidth(colKey) }}
              >
                {colKey !== "children" ? (node[colKey as keyof typeof node] as React.ReactNode) ?? null : null}
              </TableCell>
            );
          })}
        </TableRow>
      );
      if (hasChildren && isExpanded) {
        return [row, ...renderRows(node.children!, depth + 1)];
      }
      return [row];
    });
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search accounts..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="max-w-sm"
          />
          {globalFilter && (
            <Button
              variant="ghost"
              onClick={() => setGlobalFilter("")}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} of {table.getCoreRowModel().rows.length} account(s)
        </div>
      </div>

      {/* Scrollable Table */}
      <div className="rounded-md border">
        <ScrollArea className="w-full">
          <div className="min-w-[1000px]">
            <Table className="relative">
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead 
                        key={header.id}
                        className="sticky top-0 z-10 bg-background border-b whitespace-nowrap"
                        style={{ minWidth: getColumnMinWidth(header.id) }}
                      >
                        {header.isPlaceholder ? null : (
                          <div 
                            className={header.column.getCanSort() ? "cursor-pointer select-none" : ""}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(header.column.columnDef.header, header.getContext())}
                            {{
                              asc: " ↑",
                              desc: " ↓",
                            }[header.column.getIsSorted() as string] ?? null}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {tree.length ? (
                  renderRows(tree)
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No accounts found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
    </div>
  );
}

// Helper function to set minimum column widths for better display
function getColumnMinWidth(columnId: string): string {
  const widthMap: Record<string, string> = {
    select: "50px",
    accountNumber: "100px",
    name: "150px",
    type: "80px",
    classification: "110px",
    financialStatementSection: "160px",
    accountGroup: "120px",
    normalBalance: "70px",
    isActive: "80px",
    actions: "100px",
  };
  
  return widthMap[columnId] || "auto";
}
