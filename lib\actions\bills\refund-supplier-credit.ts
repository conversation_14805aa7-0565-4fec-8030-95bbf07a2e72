"use server";

import { db } from "@/db/drizzle";
import { bills } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

export async function refundSupplierCredit(vendorId: string, refundAmount: number) {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  // Find all overpaid bills for this vendor, oldest first
  const overpaidBills = await db.query.bills.findMany({
    where: and(
      eq(bills.vendorId, vendorId),
      eq(bills.organizationId, orgId),
    ),
    orderBy: [bills.date],
  });
  let remainingCredit = refundAmount;
  for (const b of overpaidBills) {
    let over = Number(b.overpaid);
    if (over > 0 && remainingCredit > 0) {
      const toRefund = Math.min(over, remainingCredit);
      await db.update(bills)
        .set({ overpaid: (over - toRefund).toFixed(2) })
        .where(eq(bills.id, b.id));
      remainingCredit -= toRefund;
    }
    if (remainingCredit <= 0) break;
  }
  // Stub: actual payment/refund integration would go here
  return { success: true, message: `Refunded $${refundAmount.toFixed(2)} supplier credit.` };
} 