"use client";

import { useEffect, useState, useCallback } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, XCircle, Clock, Users, LogOut } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import { getInvitationDetailsAction, acceptInvitationAction } from "@/lib/actions/team/invitation.actions";

// Define a more specific type for the invitation data returned by the action
type InvitationDetails = Awaited<ReturnType<typeof getInvitationDetailsAction>>['invitation'];

export default function AcceptInviteClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get("token");

  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [user, setUser] = useState<any>(null); // Using any for simplicity as user type is complex

  // --- Data Fetching and Actions ---

  const loadInvitationDetails = useCallback(async () => {
    if (!token) {
      setError("Invalid invitation link. No token provided.");
      setLoading(false);
      return;
    }

    setLoading(true);
    const result = await getInvitationDetailsAction(token);

    if (result.success) {
      setInvitation(result.invitation ?? null);
      setError(null);
    } else {
      setError(result.message ?? "An unknown error occurred.");
    }
    setLoading(false);
  }, [token]);

  const handleAccept = async () => {
    if (!token) return;
    
    setIsAccepting(true);
    const result = await acceptInvitationAction(token);
    
    if (result.success) {
      // On success, redirect to the dashboard
      router.push("/dashboard?invite_accepted=true");
    } else {
      // Handle specific errors from the accept action
      setError(result.message ?? "Failed to accept the invitation.");
    }
    setIsAccepting(false);
  };

  // --- Effects ---

  useEffect(() => {
    // Initial check for user session
    authClient.getSession().then((session) => {
      setUser(session?.data?.user ?? null);
    });
  }, []);

  useEffect(() => {
    // Fetch invitation details once token is available
    loadInvitationDetails();
  }, [loadInvitationDetails]);

  useEffect(() => {
    // UX Improvement: Handle email mismatch automatically
    if (user && invitation && user.email !== invitation.email) {
      const handleSignOut = async () => {
        await authClient.signOut();
        // Redirect to sign-in, preserving the original invitation link
        router.push(`/signin?callbackUrl=${encodeURIComponent(window.location.href)}`);
      };
      
      // Give user a moment to see the message before signing out
      setError("This invitation is for a different email. Signing you out to switch accounts...");
      setTimeout(handleSignOut, 3000);
    }
  }, [user, invitation, router]);

  // --- Render Functions for different states ---

  const renderLoading = () => (
    <div className="text-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
      <p className="mt-2 text-sm text-gray-600">Loading invitation...</p>
    </div>
  );

  const renderError = (errorMessage: string) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-red-600">
          <XCircle className="w-5 h-5 mr-2" />
          Invitation Error
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Alert variant="destructive">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push("/")} className="w-full mt-4" variant="outline">
          Go to Homepage
        </Button>
      </CardContent>
    </Card>
  );

  const renderAuthActions = () => (
    <div className="space-y-4">
      <Alert>
        <AlertDescription>
          This invitation is for <strong>{invitation?.email}</strong>. Please sign in or create an account with this email to accept.
        </AlertDescription>
      </Alert>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Button onClick={() => router.push(`/signin?callbackUrl=${encodeURIComponent(window.location.href)}`)} className="w-full">
          Sign In
        </Button>
        <Button onClick={() => router.push(`/register?callbackUrl=${encodeURIComponent(window.location.href)}`)} className="w-full" variant="secondary">
          Sign Up
        </Button>
      </div>
    </div>
  );

  const renderAcceptanceUI = () => (
    <div className="space-y-4">
      <Alert>
        <AlertDescription>
          You're signed in as <strong>{user.email}</strong>.
          <span className="text-green-600 ml-2 flex items-center">
            <CheckCircle className="w-4 h-4 mr-1" /> Email matches invitation
          </span>
        </AlertDescription>
      </Alert>
      <Button
        onClick={handleAccept}
        disabled={isAccepting}
        className="w-full"
      >
        {isAccepting ? "Accepting..." : "Join Organization & Go to Dashboard"}
      </Button>
    </div>
  );
  
  // --- Main Component Render ---

  if (loading) {
    return renderLoading();
  }

  if (error) {
    return renderError(error);
  }

  if (!invitation) {
    return null; // Or some fallback UI
  }

  return (
    <Card className="w-full max-w-lg">
      <CardHeader className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Users className="w-8 h-8 text-primary" />
        </div>
        <CardTitle>You're Invited to Join an Organization</CardTitle>
        <CardDescription>
          <strong>{invitation.inviterName || 'Someone'}</strong> has invited you to join <strong>{invitation.organizationName || 'their team'}</strong>.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600">
            You've been invited to the <strong>{invitation.organizationName}</strong> organization as a <strong>{invitation.role}</strong>.
          </p>
          <p className="text-xs text-gray-500 mt-2">
            Invited by: {invitation.inviterName} ({invitation.inviterEmail})
          </p>
        </div>

        {user ? renderAcceptanceUI() : renderAuthActions()}

        <div className="text-center text-xs text-gray-500">
          Invitation expires on {new Date(invitation.expiresAt!).toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  );
} 