"use client";

import { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";

interface UserDetails {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  onboarded: boolean;
  jobTitle?: string;
  phone?: string;
  twoFactorEnabled: boolean;
  lastActiveOrganizationId?: string;
  defaultOrganizationId?: string;
}

interface SuperadminUserDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
  userName: string;
}

export function SuperadminUserDetailsModal({ open, onOpenChange, userId, userName }: SuperadminUserDetailsModalProps) {
  const [details, setDetails] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!open) return;
    setLoading(true);
    setError(null);
    fetch(`/api/supermegacoolpanel/get-user-details?userId=${userId}`)
      .then(res => {
        if (!res.ok) throw new Error("Failed to fetch user details");
        return res.json();
      })
      .then(setDetails)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, [open, userId]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg w-full">
        <DialogTitle>User Details: {userName}</DialogTitle>
        {loading ? (
          <div>Loading user details...</div>
        ) : error ? (
          <div className="text-destructive">{error}</div>
        ) : !details ? (
          <div className="text-muted-foreground">No details found for this user.</div>
        ) : (
          <div className="space-y-2">
            <div><b>Email:</b> {details.email}</div>
            <div><b>Email Verified:</b> {details.emailVerified ? "Yes" : "No"}</div>
            <div><b>Created:</b> {new Date(details.createdAt).toLocaleString()}</div>
            <div><b>Updated:</b> {new Date(details.updatedAt).toLocaleString()}</div>
            <div><b>Onboarded:</b> {details.onboarded ? "Yes" : "No"}</div>
            <div><b>Job Title:</b> {details.jobTitle || <span className="text-muted-foreground">N/A</span>}</div>
            <div><b>Phone:</b> {details.phone || <span className="text-muted-foreground">N/A</span>}</div>
            <div><b>2FA Enabled:</b> {details.twoFactorEnabled ? "Yes" : "No"}</div>
            <div><b>Last Active Org ID:</b> {details.lastActiveOrganizationId || <span className="text-muted-foreground">N/A</span>}</div>
            <div><b>Default Org ID:</b> {details.defaultOrganizationId || <span className="text-muted-foreground">N/A</span>}</div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
} 