"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { invoices, invoiceItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

export async function deleteInvoice(invoiceId: string) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    // Delete invoice items first (due to foreign key constraint)
    await db
      .delete(invoiceItems)
      .where(eq(invoiceItems.invoiceId, invoiceId));
      
    // Then delete the invoice
    await db
      .delete(invoices)
      .where(
        and(
          eq(invoices.id, invoiceId),
          eq(invoices.organizationId, organizationId)
        )
      );

    revalidatePath("/invoices");
    return { success: true, message: "Invoice deleted." };
  } catch (error) {
    console.error(error);
    return { success: false, message: "Failed to delete invoice." };
  }
} 