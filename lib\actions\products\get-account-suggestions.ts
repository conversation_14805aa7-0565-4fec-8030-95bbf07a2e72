/**
 * Product Intelligence System - Smart Account Suggestions
 * Provides intelligent account mappings based on business type and product type
 */

import { db } from "@/db/drizzle";
import { accounts, organization } from "@/db/schema/schema";
import { eq, and, like, or } from "drizzle-orm";

export type BusinessType = 
  | "consulting" 
  | "retail" 
  | "saas" 
  | "manufacturing" 
  | "professional_services" 
  | "nonprofit" 
  | "real_estate"
  | "healthcare"
  | "other";

export type ProductType = 
  | "service" 
  | "digital_service" 
  | "physical_product" 
  | "digital_product" 
  | "subscription" 
  | "bundle";

export interface AccountSuggestion {
  accountId: string;
  accountName: string;
  accountCode?: string;
  confidence: number; // 0-100
  reason: string;
  isSystemGenerated?: boolean;
  accountType?: 'revenue' | 'expense' | 'asset';
}

export interface ProductSetupSuggestions {
  revenueAccount: AccountSuggestion[];
  cogsAccount: AccountSuggestion[];
  inventoryAccount?: AccountSuggestion[];
  defaultCostStructure?: {
    estimatedMargin: number;
    typicalCostFactors: string[];
  };
}

/**
 * Business type templates with intelligent defaults
 */
export const businessTypeTemplates = {
  consulting: {
    commonCategories: ["Consulting Services", "Training", "Strategy", "Advisory"],
    revenueAccountNames: ["Service Revenue", "Consulting Revenue", "Professional Services"],
    revenueAccountCodes: ["4120", "4100", "4110"],
    cogsAccountNames: ["Direct Labor", "Contractor Costs", "Project Costs"],
    cogsAccountCodes: ["5200", "5210", "5220"],
    typicalMargin: 65,
    costFactors: ["Labor hours", "Travel expenses", "Materials"]
  },
  retail: {
    commonCategories: ["Physical Products", "Accessories", "Merchandise"],
    revenueAccountNames: ["Product Sales", "Retail Sales", "Merchandise Sales"],
    revenueAccountCodes: ["4110", "4100", "4120"],
    cogsAccountNames: ["Cost of Goods Sold", "Inventory Costs", "Product Costs"],
    cogsAccountCodes: ["5100", "5110", "5120"],
    inventoryAccountNames: ["Inventory", "Merchandise Inventory", "Product Inventory"],
    inventoryAccountCodes: ["1130", "1120", "1140"],
    typicalMargin: 45,
    costFactors: ["Product cost", "Shipping", "Storage"]
  },
  saas: {
    commonCategories: ["Subscriptions", "Professional Services", "Setup", "Support"],
    revenueAccountNames: ["Subscription Revenue", "SaaS Revenue", "Service Revenue"],
    revenueAccountCodes: ["4120", "4130", "4100"],
    cogsAccountNames: ["Technology Costs", "Server Costs", "Platform Costs"],
    cogsAccountCodes: ["5220", "5230", "5200"],
    typicalMargin: 80,
    costFactors: ["Server costs", "Third-party services", "Support time"]
  },
  manufacturing: {
    commonCategories: ["Manufactured Products", "Components", "Assembly"],
    revenueAccountNames: ["Product Sales", "Manufacturing Revenue", "Goods Sales"],
    revenueAccountCodes: ["4110", "4100", "4120"],
    cogsAccountNames: ["Raw Materials", "Manufacturing Costs", "Production Costs"],
    cogsAccountCodes: ["5100", "5110", "5120"],
    inventoryAccountNames: ["Raw Materials", "Work in Process", "Finished Goods"],
    inventoryAccountCodes: ["1130", "1135", "1140"],
    typicalMargin: 35,
    costFactors: ["Raw materials", "Labor", "Manufacturing overhead"]
  },
  professional_services: {
    commonCategories: ["Professional Services", "Legal", "Accounting", "Medical"],
    revenueAccountNames: ["Service Revenue", "Professional Fees", "Service Income"],
    revenueAccountCodes: ["4120", "4100", "4110"],
    cogsAccountNames: ["Direct Labor", "Professional Costs", "Service Costs"],
    cogsAccountCodes: ["5200", "5210", "5220"],
    typicalMargin: 70,
    costFactors: ["Professional time", "Research", "Materials"]
  },
  nonprofit: {
    commonCategories: ["Donations", "Grants", "Programs"],
    revenueAccountNames: ["Contributions", "Grant Revenue"],
    revenueAccountCodes: ["4500", "4510"],
    cogsAccountNames: ["Program Expenses", "Fundraising Costs"],
    cogsAccountCodes: ["6100", "6200"],
    typicalMargin: 0,
    costFactors: ["Program staff", "Materials"]
  },
  real_estate: {
    commonCategories: ["Rental Income", "Property Sales"],
    revenueAccountNames: ["Rental Revenue", "Sales Revenue"],
    revenueAccountCodes: ["4200", "4210"],
    cogsAccountNames: ["Property Management Fees", "Maintenance Costs"],
    cogsAccountCodes: ["5300", "5310"],
    typicalMargin: 50,
    costFactors: ["Mortgage interest", "Property taxes", "Repairs"]
  },
  healthcare: {
    commonCategories: ["Patient Services", "Lab Fees"],
    revenueAccountNames: ["Patient Service Revenue", "Medical Fees"],
    revenueAccountCodes: ["4300", "4310"],
    cogsAccountNames: ["Medical Supplies", "Lab Costs"],
    cogsAccountCodes: ["5400", "5410"],
    typicalMargin: 60,
    costFactors: ["Medical staff salaries", "Supplies", "Insurance"]
  },
  other: {
    commonCategories: ["General", "Miscellaneous"],
    revenueAccountNames: ["General Revenue", "Other Income"],
    revenueAccountCodes: ["4900", "4910"],
    cogsAccountNames: ["General Expenses", "Other Costs"],
    cogsAccountCodes: ["5900", "5910"],
    inventoryAccountNames: ["Inventory"],
    inventoryAccountCodes: ["1130"],
    typicalMargin: 50,
    costFactors: ["Various"]
  }
} as const;

type BusinessTemplate = typeof businessTypeTemplates[keyof typeof businessTypeTemplates];

/**
 * Smart account suggestion engine
 */
export class AccountSuggestionEngine {
  private organizationId: string;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
  }

  /**
   * Get intelligent account suggestions for a product setup
   */
  async getProductAccountSuggestions(
    productType: ProductType,
    businessType?: BusinessType,
    productName?: string
  ): Promise<ProductSetupSuggestions> {
    // Get organization's existing accounts
    const orgAccounts = await this.getOrganizationAccounts();
    
    // Determine business type if not provided
    const inferredBusinessType = businessType || await this.inferBusinessType();
    
    // Get template for business type
    const template = businessTypeTemplates[inferredBusinessType] || businessTypeTemplates.consulting;
    
    return {
      revenueAccount: this.suggestRevenueAccounts(template, orgAccounts, productType),
      cogsAccount: this.suggestCogsAccounts(template, orgAccounts, productType),
      inventoryAccount: productType === 'physical_product' 
        ? this.suggestInventoryAccounts(template, orgAccounts)
        : undefined,
      defaultCostStructure: {
        estimatedMargin: template.typicalMargin,
        typicalCostFactors: [...template.costFactors] // Make mutable
      }
    };
  }

  /**
   * Suggest revenue accounts based on business type and existing accounts
   */
  private suggestRevenueAccounts(
    template: BusinessTemplate,
    orgAccounts: { id: string; name: string; type: string | null; accountNumber: string | null; }[],
    productType: ProductType
  ): AccountSuggestion[] {
    const suggestions: AccountSuggestion[] = [];

    // Look for existing revenue accounts that match template
    for (const accountName of template.revenueAccountNames) {
      const matches = orgAccounts.filter(acc => 
        acc.name.toLowerCase().includes(accountName.toLowerCase()) &&
        acc.type === 'revenue'
      );
      
      matches.forEach(acc => {
        suggestions.push({
          accountId: acc.id,
          accountName: acc.name,
          accountCode: acc.accountNumber ?? undefined,
          confidence: 95,
          reason: `Matches ${template.revenueAccountNames[0]} pattern for your business type`
        });
      });
    }

    // Look for accounts by code
    for (const code of template.revenueAccountCodes) {
      const match = orgAccounts.find(acc => acc.accountNumber === code);
      if (match && !suggestions.find(s => s.accountId === match.id)) {
        suggestions.push({
          accountId: match.id,
          accountName: match.name,
          accountCode: match.accountNumber ?? undefined,
          confidence: 90,
          reason: `Standard revenue account code ${code} for ${productType} products`
        });
      }
    }

    // Generic revenue account fallback
    const genericRevenue = orgAccounts.find(acc => 
      acc.type === 'revenue' && 
      (acc.name.toLowerCase().includes('revenue') || acc.name.toLowerCase().includes('income'))
    );
    
    if (genericRevenue && !suggestions.find(s => s.accountId === genericRevenue.id)) {
      suggestions.push({
        accountId: genericRevenue.id,
        accountName: genericRevenue.name,
        accountCode: genericRevenue.accountNumber ?? undefined,
        confidence: 75,
        reason: "General revenue account"
      });
    }

    return suggestions.slice(0, 3); // Top 3 suggestions
  }

  /**
   * Suggest COGS accounts based on business type and product type
   */
  private suggestCogsAccounts(
    template: BusinessTemplate,
    orgAccounts: { id: string; name: string; type: string | null; accountNumber: string | null; }[],
    productType: ProductType
  ): AccountSuggestion[] {
    const suggestions: AccountSuggestion[] = [];

    // For services, COGS might not be required
    if (productType === 'service' || productType === 'digital_service') {
      const serviceCogsAccounts = orgAccounts.filter(acc => 
        acc.type === 'expense' && 
        (acc.name.toLowerCase().includes('labor') || 
         acc.name.toLowerCase().includes('contractor') ||
         acc.name.toLowerCase().includes('service'))
      );
      
      serviceCogsAccounts.forEach(acc => {
        suggestions.push({
          accountId: acc.id,
          accountName: acc.name,
          accountCode: acc.accountNumber ?? undefined,
          confidence: 85,
          reason: `Direct cost account for ${productType} type products`
        });
      });
    } else {
      // For physical products, look for COGS accounts
      for (const accountName of template.cogsAccountNames) {
        const matches = orgAccounts.filter(acc => 
          acc.name.toLowerCase().includes(accountName.toLowerCase()) &&
          acc.type === 'expense'
        );
        
        matches.forEach(acc => {
          suggestions.push({
            accountId: acc.id,
            accountName: acc.name,
            accountCode: acc.accountNumber ?? undefined,
            confidence: 95,
            reason: `Standard COGS account for your business type`
          });
        });
      }
    }

    return suggestions.slice(0, 3);
  }

  /**
   * Suggest inventory accounts for physical products
   */
  private suggestInventoryAccounts(
    template: BusinessTemplate,
    orgAccounts: { id: string; name: string; type: string | null; accountNumber: string | null; }[]
  ): AccountSuggestion[] {
    const suggestions: AccountSuggestion[] = [];

    const inventoryAccountNames = 'inventoryAccountNames' in template ? template.inventoryAccountNames : [];
    const inventoryAccountCodes = 'inventoryAccountCodes' in template ? template.inventoryAccountCodes : [];

    // Look for existing inventory accounts that match template
    for (const accountName of inventoryAccountNames) {
      const matches = orgAccounts.filter(acc => 
        acc.name.toLowerCase().includes(accountName.toLowerCase()) &&
        acc.type === 'asset'
      );
      
      matches.forEach(acc => {
        suggestions.push({
          accountId: acc.id,
          accountName: acc.name,
          accountCode: acc.accountNumber ?? undefined,
          confidence: 95,
          reason: `Matches ${inventoryAccountNames[0]} pattern for your business type`
        });
      });
    }

    // Look for accounts by code
    for (const code of inventoryAccountCodes) {
      const match = orgAccounts.find(acc => acc.accountNumber === code);
      if (match && !suggestions.find(s => s.accountId === match.id)) {
        suggestions.push({
          accountId: match.id,
          accountName: match.name,
          accountCode: match.accountNumber ?? undefined,
          confidence: 90,
          reason: `Standard inventory account code ${code} for physical products`
        });
      }
    }

    // Generic inventory account fallback
    const genericInventory = orgAccounts.find(acc => 
      acc.type === 'asset' && 
      acc.name.toLowerCase().includes('inventory')
    );
    
    if (genericInventory && !suggestions.find(s => s.accountId === genericInventory.id)) {
      suggestions.push({
        accountId: genericInventory.id,
        accountName: genericInventory.name,
        accountCode: genericInventory.accountNumber ?? undefined,
        confidence: 75,
        reason: "General inventory account"
      });
    }

    return suggestions.slice(0, 3);
  }

  /**
   * Get organization's existing accounts
   */
  private async getOrganizationAccounts() {
    return db
      .select({
        id: accounts.id,
        name: accounts.name,
        type: accounts.type,
        accountNumber: accounts.accountNumber,
      })
      .from(accounts)
      .where(eq(accounts.organizationId, this.organizationId));
  }

  /**
   * Infer business type from organization name or existing data
   */
  private async inferBusinessType(): Promise<BusinessType> {
    const org = await db
      .select({ businessType: organization.businessType })
.from(organization)
.where(eq(organization.id, this.organizationId))
      .limit(1);

    return (org[0]?.businessType as BusinessType) || 'other';
  }

  /**
   * Create missing accounts based on business type, if they don't exist
   * This function prepares a list of accounts to be created, but does not execute creation.
   * It also handles potential conflicts with existing account numbers.
   */
  async getMissingAccountSuggestions(
    businessType: BusinessType,
  ): Promise<AccountSuggestion[]> {
    const template = businessTypeTemplates[businessType] || businessTypeTemplates.consulting;
    const orgAccounts = await this.getOrganizationAccounts();
    const existingAccountNumbers = new Set(orgAccounts.map(acc => acc.accountNumber));
    const suggestions: AccountSuggestion[] = [];

    const checkAndSuggest = (
      accountNames: readonly string[], 
      accountCodes: readonly string[], 
      accountType: 'revenue' | 'expense' | 'asset'
    ) => {
      let isAccountPresent = false;
      // First, check if any account with the suggested names or codes already exists.
      for (const account of orgAccounts) {
        if (
          (account.type === accountType) &&
          (accountNames.some(name => account.name.toLowerCase().includes(name.toLowerCase())) || 
           (account.accountNumber && accountCodes.includes(account.accountNumber)))
        ) {
          isAccountPresent = true;
          break;
        }
      }

      // If no suitable account is found, suggest creating one.
      if (!isAccountPresent) {
        let suggestedNumber = accountCodes[0];
        let suffix = 1;
        // Ensure the suggested account number is unique
        while (existingAccountNumbers.has(suggestedNumber)) {
          suggestedNumber = `${accountCodes[0]}-${suffix++}`;
        }

        suggestions.push({
          accountId: `new_${suggestedNumber}`,
          accountName: accountNames[0],
          accountCode: suggestedNumber,
          confidence: 100,
          reason: `A standard ${accountType} account for a ${businessType} business.`,
          isSystemGenerated: true,
          accountType: accountType,
        });
      }
    };

    // Suggest Revenue Account
    checkAndSuggest(template.revenueAccountNames, template.revenueAccountCodes, 'revenue');
    
    // Suggest COGS Account
    checkAndSuggest(template.cogsAccountNames, template.cogsAccountCodes, 'expense');

    // Suggest Inventory Account for relevant businesses
    if ('inventoryAccountNames' in template && 'inventoryAccountCodes' in template) {
      const inventoryAccountNames = template.inventoryAccountNames;
      const inventoryAccountCodes = template.inventoryAccountCodes;

      if (Array.isArray(inventoryAccountNames) && Array.isArray(inventoryAccountCodes)) {
        checkAndSuggest(
          inventoryAccountNames, 
          inventoryAccountCodes, 
          'asset'
        );
      }
    }

    return suggestions;
  }

  /**
   * This function is a placeholder and should be replaced by a proper server action
   * that takes an array of account data and creates them in the database.
   * For now, it just logs the intent.
   */
  async createMissingAccounts(
    businessType: BusinessType,
    productType: ProductType
  ): Promise<{ revenue?: string, cogs?: string, inventory?: string }> {
    console.log("Intending to create missing accounts for", { businessType, productType });
    // In a real implementation, this would call a 'createAccounts' server action
    // with the suggestions generated by 'getMissingAccountSuggestions'.
    return {};
  }
}

/**
 * Convenience function to get product setup suggestions
 */
export async function getProductSetupSuggestions(
  organizationId: string,
  productType: ProductType,
  businessType?: BusinessType,
  productName?: string
): Promise<ProductSetupSuggestions> {
  const engine = new AccountSuggestionEngine(organizationId);
  return await engine.getProductAccountSuggestions(productType, businessType, productName);
}

/**
 * Get business type from organization
 */
export async function getOrganizationBusinessType(organizationId: string): Promise<BusinessType> {
  const org = await db
    .select({ businessType: organization.businessType })
.from(organization)
.where(eq(organization.id, organizationId))
    .limit(1);

  return (org[0]?.businessType as BusinessType) || 'other';
} 