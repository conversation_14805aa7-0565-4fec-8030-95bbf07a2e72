"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { verify2FACode } from "@/lib/actions/user/verify-2fa";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const verify2FASchema = z.object({
  code: z.string().length(6, "Code must be 6 characters."),
  rememberDevice: z.boolean().optional(),
});

type Verify2FAFormData = z.infer<typeof verify2FASchema>;

export default function Verify2FAPage() {
  const router = useRouter();
  const form = useForm<Verify2FAFormData>({
    resolver: zodResolver(verify2FASchema),
    defaultValues: {
      code: "",
      rememberDevice: true,
    },
  });

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
  } = form;

  const onSubmit = async (data: Verify2FAFormData) => {
    const result = await verify2FACode({
        code: data.code,
        rememberDevice: data.rememberDevice ?? false
    });
    if (result.success) {
      toast.success("Verification successful! Signing you in.");
      router.push("/dashboard");
    } else {
      toast.error(result.error || "An unexpected error occurred.");
      form.reset({ ...data, code: "" });
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Check your email</CardTitle>
          <CardDescription>We've sent a 6-digit code to your email address.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={control}
                name="code"
                render={({ field }) => (
                  <FormItem className="space-y-2 text-center">
                    <Label htmlFor="otp-code">Enter your code</Label>
                    <FormControl>
                      <Input
                        id="otp-code"
                        {...field}
                        maxLength={6}
                        placeholder="- - - - - -"
                        className="text-center text-2xl font-semibold"
                        autoFocus
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="rememberDevice"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <Label htmlFor="remember-device" className="text-sm font-normal">
                        Remember this device for 30 days
                      </Label>
                    </div>
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Verifying..." : "Verify"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
} 