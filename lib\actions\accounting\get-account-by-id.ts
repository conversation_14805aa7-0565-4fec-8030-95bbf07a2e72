"use server";

import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { Account } from "@/components/protectedPages/Accounting/columns";

export async function getAccountById(accountId: string): Promise<Account | null> {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  const accountRow = await db
    .select()
    .from(accounts)
    .where(and(eq(accounts.id, accountId), eq(accounts.organizationId, orgId)))
    .limit(1);
  return accountRow[0] ?? null;
} 