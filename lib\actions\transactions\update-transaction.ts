"use server";

import { z } from "zod";
import { db } from "@/db/drizzle";
import { transactions, journalEntries, accounts, auditLogs, bills } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq, and, ne } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { randomUUID } from 'crypto';

const transactionFormSchema = z.object({
    transactionType: z.enum(["income", "expense"]),
    amount: z.coerce.number().positive(),
    accountId: z.string().min(1),
    date: z.string().min(1),
    description: z.string().min(1),
});

async function getUserOrganizationId(userId: string) {
    const { member } = await import("@/db/schema/schema");
    const orgs = await db
        .select({ organizationId: member.organizationId })
.from(member)
.where(eq(member.userId, userId));
    return orgs[0]?.organizationId || null;
}

async function getDefaultAccount(organizationId: string, type: "revenue" | "expense") {
    const result = await db
        .select()
        .from(accounts)
        .where(and(eq(accounts.organizationId, organizationId), eq(accounts.type, type)))
        .limit(1);
    return result[0];
}

export async function updateTransaction(transactionId: string, formData: FormData) {
    const session = await auth.api.getSession({ headers: await headers() });
    const userId = session?.user?.id;
    if (!userId) return { success: false, message: "Not authenticated." };
    const organizationId = await getUserOrganizationId(userId);
    if (!organizationId) return { success: false, message: "No organization found." };

    const parsed = transactionFormSchema.safeParse({
        transactionType: formData.get("transactionType"),
        amount: formData.get("amount"),
        accountId: formData.get("accountId"),
        date: formData.get("date"),
        description: formData.get("description"),
    });

    if (!parsed.success) {
        return { success: false, message: "Invalid input." };
    }
    const { transactionType, amount, accountId, date, description } = parsed.data;

    const beforeTx = await db.select().from(transactions).where(and(eq(transactions.id, transactionId), eq(transactions.organizationId, organizationId))).limit(1);

    try {
        await db.delete(journalEntries).where(eq(journalEntries.transactionId, transactionId));

        const defaultAccount = await getDefaultAccount(organizationId, transactionType === "income" ? "revenue" : "expense");
        if (!defaultAccount) {
            return { success: false, message: `No default ${transactionType} account found.` };
        }

        if (transactionType === "income") {
            await db.insert(journalEntries).values([
                { id: randomUUID(), transactionId, accountId, debitAmount: amount.toString(), creditAmount: "0.00", description },
                { id: randomUUID(), transactionId, accountId: defaultAccount.id, debitAmount: "0.00", creditAmount: amount.toString(), description },
            ]);
        } else {
            await db.insert(journalEntries).values([
                { id: randomUUID(), transactionId, accountId: defaultAccount.id, debitAmount: amount.toString(), creditAmount: "0.00", description },
                { id: randomUUID(), transactionId, accountId, debitAmount: "0.00", creditAmount: amount.toString(), description },
            ]);
        }

        const billId = formData.get("billId") || undefined;
        const [updatedTx] = await db.update(transactions).set({
            date: new Date(date),
            description,
            totalAmount: amount.toString(),
            updatedAt: new Date(),
            ...(billId ? { billId: String(billId) } : {}),
        }).where(eq(transactions.id, transactionId)).returning();

        if (billId) {
            await db.update(bills)
                .set({ transactionId })
                .where(eq(bills.id, String(billId)));
        }

        await db.insert(auditLogs).values({
            id: randomUUID(),
            organizationId,
            userId,
            action: "update",
            resourceType: "transaction",
            resourceId: transactionId,
            oldValues: beforeTx[0] || null,
            newValues: updatedTx,
            createdAt: new Date(),
        });
        revalidatePath("/transactions");
        return { success: true, message: "Transaction updated." };
    } catch (error) {
        return { success: false, message: "Failed to update transaction." };
    }
} 