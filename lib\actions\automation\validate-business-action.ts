"use server";

import { z } from "zod";
import { 
  customerPaymentReceivedSchema,
  businessExpensePaidSchema,
  productSoldSchema,
  serviceProvidedSchema
} from "@/lib/business-actions/types";

const actionSchemaMap: Record<string, z.ZodSchema<any>> = {
  customer_payment_received: customerPaymentReceivedSchema,
  business_expense_paid: businessExpensePaidSchema,
  product_sold: productSoldSchema,
  service_provided: serviceProvidedSchema,
};

export async function validateBusinessAction(
  actionType: string, 
  data: any
): Promise<{ isValid: boolean; errors?: string[] }> {
  try {
    const schema = actionSchemaMap[actionType];
    if (!schema) {
      return { isValid: false, errors: ["Invalid action type"] };
    }

    const result = schema.safeParse(data);

    if (result.success) {
      return { isValid: true };
    } else {
      return { 
        isValid: false, 
        errors: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`) 
      };
    }
  } catch (error) {
    console.error("Error validating business action:", error);
    return { 
      isValid: false, 
      errors: [error instanceof Error ? error.message : "Unknown validation error"] 
    };
  }
} 