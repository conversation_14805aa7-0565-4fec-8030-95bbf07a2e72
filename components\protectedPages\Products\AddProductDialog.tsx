"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Product } from "./columns";
import { NewProduct, productFormSchema } from "@/lib/actions/products/product.schema";
import { useQuery } from "@tanstack/react-query";
import { getProductById } from "@/lib/actions/products/get-product-by-id";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";
import {
  Form,
} from "@/components/ui/form";

interface Account {
  id: string;
  name: string;
}

interface AddProductDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (productData: NewProduct) => void;
  isPending: boolean;
  productId?: string | null;
}

export default function AddProductDialog({
  open,
  onOpenChange,
  onSubmit,
  isPending,
  productId,
}: AddProductDialogProps) {
  const isEdit = !!productId;
  const { data: product, isLoading: isLoadingProduct } = useQuery({
    queryKey: ["product", productId],
    queryFn: () => productId ? getProductById(productId) : null,
    enabled: !!productId && open,
  });
  const { data: accounts = [], isLoading: isLoadingAccounts } = useQuery({
    queryKey: ["accounts"],
    queryFn: getAccounts,
  });
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<NewProduct>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      type: "service",
    },
  });

  useEffect(() => {
    if (product) {
      reset({
        name: product.name,
        description: product.description ?? "",
        price: parseFloat(product.price),
        type: product.type,
        revenueAccountId: product.revenueAccountId ?? undefined,
        cogsAccountId: product.cogsAccountId ?? undefined,
        inventoryAccountId: product.inventoryAccountId ?? undefined,
      });
    } else {
      reset({
        name: "",
        description: "",
        price: 0,
        type: "service",
        revenueAccountId: undefined,
        cogsAccountId: undefined,
        inventoryAccountId: undefined,
      });
    }
  }, [product, reset]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {product ? "Edit Product" : "Add Product"}
          </DialogTitle>
          <DialogDescription>
            {product ? "Update the product information below." : "Create a new product with pricing and account settings."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="product-name">Name</Label>
              <Input id="product-name" {...register("name")} />
              {errors.name && (
                <p className="text-red-500 text-xs">{errors.name.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="product-price">Price</Label>
              <Input
                id="product-price"
                type="number"
                step="0.01"
                {...register("price", { valueAsNumber: true })}
              />
              {errors.price && (
                <p className="text-red-500 text-xs">{errors.price.message}</p>
              )}
            </div>
          </div>
          <div>
            <Label htmlFor="product-description">Description</Label>
            <Textarea id="product-description" {...register("description")} />
          </div>
          <div>
            <Label htmlFor="product-type">Type</Label>
            <Select
              onValueChange={(value) =>
                setValue("type", value as NewProduct["type"])
              }
              value={watch("type")}
            >
              <SelectTrigger id="product-type">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="service">Service</SelectItem>
                <SelectItem value="non-inventory">Non-Inventory</SelectItem>
                <SelectItem value="inventory">Inventory</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="revenue-account">Revenue Account</Label>
              <Select
                onValueChange={(value) => setValue("revenueAccountId", value)}
                value={watch("revenueAccountId")}
              >
                <SelectTrigger id="revenue-account">
                  <SelectValue placeholder="Select account" />
                </SelectTrigger>
                <SelectContent>
                  {accounts.map((acc: Account) => (
                    <SelectItem key={acc.id} value={acc.id}>
                      {acc.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="cogs-account">COGS Account</Label>
              <Select
                onValueChange={(value) => setValue("cogsAccountId", value)}
                value={watch("cogsAccountId")}
              >
                <SelectTrigger id="cogs-account">
                  <SelectValue placeholder="Select account" />
                </SelectTrigger>
                <SelectContent>
                  {accounts.map((acc: Account) => (
                    <SelectItem key={acc.id} value={acc.id}>
                      {acc.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="inventory-account">Inventory Account</Label>
              <Select
                onValueChange={(value) => setValue("inventoryAccountId", value)}
                value={watch("inventoryAccountId")}
              >
                <SelectTrigger id="inventory-account">
                  <SelectValue placeholder="Select account" />
                </SelectTrigger>
                <SelectContent>
                  {accounts.map((acc: Account) => (
                    <SelectItem key={acc.id} value={acc.id}>
                      {acc.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="ghost"
              onClick={() => onOpenChange(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
