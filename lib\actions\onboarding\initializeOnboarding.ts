"use server";

import { requireAuth } from "@/lib/server-auth";
import { getOnboardingProgress } from ".";
import { resetOnboarding } from "./resetOnboarding";

/**
 * Gets the onboarding progress for the current user.
 * If progress does not exist, it initializes it first.
 * @returns The user's onboarding progress.
 */
export async function initializeOnboarding() {
  const userId = await requireAuth();

  let progress = await getOnboardingProgress(userId);

  if (!progress) {
    progress = await resetOnboarding(userId);
  }

  return progress;
} 