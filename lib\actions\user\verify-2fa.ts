"use server";

import { auth } from "@/lib/auth";
import { headers } from "next/headers";

type Verify2FAInput = {
    code: string;
    rememberDevice: boolean;
};

export async function verify2FACode({ code, rememberDevice }: Verify2FAInput) {
  const session = await auth.api.getSession({ headers: await headers() });
  if (!session?.user?.email) {
    return { success: false, error: "Authentication session expired. Please sign in again." };
  }

  try {
    await auth.api.verifyTwoFactorOTP({
      body: {
        code,
        trustDevice: rememberDevice,
      },
    });

    return { success: true };
  } catch (error) {
    if (error instanceof Error && error.message.toLowerCase().includes("invalid otp")) {
      return { success: false, error: "Invalid or expired code. Please try again." };
    }
    
    console.error("2FA verification error:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
} 