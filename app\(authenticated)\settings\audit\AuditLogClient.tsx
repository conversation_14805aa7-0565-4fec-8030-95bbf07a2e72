'use client';

import { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// This would typically come from the database schema types
type AuditLog = {
  id: string;
  action: string;
  resourceType: string;
  resourceId: string | null;
  description: string | null;
  userId: string | null;
  createdAt: Date;
  riskLevel: string | null;
};

interface AuditLogClientProps {
  initialLogs: AuditLog[];
}

export function AuditLogClient({ initialLogs }: AuditLogClientProps) {
  const [logs, setLogs] = useState(initialLogs);
  const [searchTerm, setSearchTerm] = useState('');
  const [riskLevelFilter, setRiskLevelFilter] = useState('all');

  const filteredLogs = useMemo(() => {
    return logs.filter((log) => {
      const searchTermMatch =
        searchTerm.length === 0 ||
        log.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.resourceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.userId?.toLowerCase().includes(searchTerm.toLowerCase());

      const riskLevelMatch =
        riskLevelFilter === 'all' || log.riskLevel === riskLevelFilter;

      return searchTermMatch && riskLevelMatch;
    });
  }, [logs, searchTerm, riskLevelFilter]);

  const getRiskLevelVariant = (riskLevel: string | null) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Recent Activities</CardTitle>
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
            <Select value={riskLevelFilter} onValueChange={setRiskLevelFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by risk" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Risk Levels</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setRiskLevelFilter('all');
              }}
            >
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Action</TableHead>
                <TableHead>Resource</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    <Badge variant={getRiskLevelVariant(log.riskLevel)}>
                      {log.action}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {log.resourceType}
                    {log.resourceId
                      ? ` (${log.resourceId.substring(0, 8)}...)`
                      : ''}
                  </TableCell>
                  <TableCell>
                    {log.userId
                      ? `${log.userId.substring(0, 12)}...`
                      : 'System'}
                  </TableCell>
                  <TableCell>{log.description}</TableCell>
                  <TableCell>
                    {new Date(log.createdAt).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
} 