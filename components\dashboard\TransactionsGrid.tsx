import React from "react";
import TransactionCard, { Transaction } from "./TransactionCard";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface TransactionsGridProps {
  transactions?: Transaction[];
  isLoading?: boolean;
}

const mockTransactions: Transaction[] = [
    {
      id: "1",
      type: "income",
      amount: 2500,
      description: "Website Development",
      date: "2024-07-15",
      category: "Client Project",
    },
    {
      id: "2",
      type: "expense",
      amount: 450,
      description: "Software Subscription",
      date: "2024-07-14",
      category: "Business Tools",
    },
    {
      id: "3",
      type: "income",
      amount: 1200,
      description: "Logo Design",
      date: "2024-07-12",
      category: "Freelance Work",
    },
     {
      id: "4",
      type: "expense",
      amount: 85.50,
      description: "Office Supplies",
      date: "2024-07-11",
      category: "Operations",
    },
];

const TransactionsGrid = ({
  transactions = mockTransactions,
  isLoading = false,
}: TransactionsGridProps) => {
  const totalIncome = transactions
    .filter((t) => t.type === "income")
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactions
    .filter((t) => t.type === "expense")
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <Card>
       <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
          <CardTitle>Recent Transactions</CardTitle>
          <div className="flex flex-wrap gap-4">
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Total Income</p>
              <p className="text-lg font-semibold text-green-500">
                +${totalIncome.toLocaleString('en-US')}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Total Expenses</p>
              <p className="text-lg font-semibold text-red-500">
                -${totalExpenses.toLocaleString('en-US')}
              </p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-3">
          {isLoading
            ? Array(4)
                .fill(0)
                .map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                ))
            : transactions.map((transaction) => (
                <TransactionCard key={transaction.id} transaction={transaction} />
              ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionsGrid; 