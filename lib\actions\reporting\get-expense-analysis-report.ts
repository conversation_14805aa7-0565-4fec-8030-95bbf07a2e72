"use server";

import { db } from "@/db/drizzle";
import { accounts, journalEntries, transactions } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, gte, lte, desc, sql, sum } from "drizzle-orm";

export async function getExpenseAnalysisReport(fromDate?: string, toDate?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    let filter: any[] = [
        eq(transactions.organizationId, organizationId),
        eq(accounts.type, 'expense')
    ];
    if (fromDate) filter.push(gte(transactions.date, new Date(fromDate)));
    if (toDate) filter.push(lte(transactions.date, new Date(toDate)));

    const expenseByCategory = await db.select({
        category: accounts.name,
        total: sum(sql<number>`cast(${journalEntries.debitAmount} as numeric)`)
    })
    .from(journalEntries)
    .leftJoin(transactions, eq(journalEntries.transactionId, transactions.id))
    .leftJoin(accounts, eq(journalEntries.accountId, accounts.id))
    .where(and(...filter))
    .groupBy(accounts.name)
    .orderBy(desc(sum(sql<number>`cast(${journalEntries.debitAmount} as numeric)`)));

    const totalExpenses = expenseByCategory.reduce((acc, row) => acc + (Number(row.total) || 0), 0);

    const report = expenseByCategory.map(row => ({
        category: row.category,
        total: Number(row.total) || 0,
        percentage: totalExpenses > 0 ? ((Number(row.total) || 0) / totalExpenses) * 100 : 0
    }));

    return {
        report,
        totalExpenses
    };
} 