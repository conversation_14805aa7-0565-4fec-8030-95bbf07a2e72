"use server";

import { db } from "@/db/drizzle";
import { invoices } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

export async function refundClientCredit(clientId: string, refundAmount: number) {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  // Find all overpaid invoices for this client, oldest first
  const overpaidInvoices = await db.query.invoices.findMany({
    where: and(
      eq(invoices.clientId, clientId),
      eq(invoices.organizationId, orgId),
    ),
    orderBy: [invoices.date],
  });
  let remaining = refundAmount;
  for (const inv of overpaidInvoices) {
    const overpaid = Number(inv.overpaid);
    if (overpaid > 0 && remaining > 0) {
      const toRefund = Math.min(overpaid, remaining);
      await db.update(invoices).set({
        overpaid: (overpaid - toRefund).toFixed(2),
      }).where(eq(invoices.id, inv.id));
      remaining -= toRefund;
    }
    if (remaining <= 0) break;
  }
  // TODO: Integrate with payment processor to actually send refund
  return { success: true, message: `Refunded $${refundAmount.toFixed(2)} to client (stub).` };
} 