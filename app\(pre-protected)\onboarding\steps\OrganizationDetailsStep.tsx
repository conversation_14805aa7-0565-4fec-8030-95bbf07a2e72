"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Building2, MapPin, Phone, ArrowLeft, Check, X, UploadCloud } from "lucide-react";
import { useEffect, useState, useRef, useMemo } from "react";
import { useForm, useF<PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { z } from "zod";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import currenciesData from "@/lib/data/currencies.json";
import { taxIdInfo } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { CountrySelector } from "@/components/general/CountrySelector";
import { PhoneNumberInput } from "@/components/general/PhoneNumberInput";
import { countries } from "@/lib/country-utils";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { updateOnboardingStep } from "@/lib/actions/onboarding/updateOnboardingStep";
import { ONBOARDING_STEPS } from "@/lib/onboarding-constants";
import { getTimeZones } from '@vvo/tzdb';
import { useDebouncedCallback } from 'use-debounce';

// Define OrganizationDetailsStepProps interface
interface OrganizationDetailsStepProps {
  isLoading: boolean;
  orgId: string;
  userId: string;
  onNext: (data: any) => void;
  onPrevious: () => void;
  businessType?: string;
  orgName?: string;
  orgSlug?: string;
}

// Define the form schema type for useForm
interface OrganizationDetailsFormValues {
  email: string;
  industry: string;
  legalEntity: string;
  currency: string;
  taxId: string;
  taxIdType?: string;
  website: string;
  phone: string;
  timeZone: string;
  addresses: {
    type: string;
    street: string;
    address2?: string;
    city: string;
    zip: string;
    country: string;
    timeZone?: string;
  }[];
}

const { countryCurrencyMap, currencies } = currenciesData as {
  countryCurrencyMap: Record<string, string>;
  currencies: Record<string, { name: string; symbol: string }>;
};

// Add Zod schema for validation
const OrganizationDetailsSchema = z.object({
  email: z.string().min(1, { message: 'Email is required' }).email({ message: 'Invalid email address' }),
  industry: z.string().or(z.literal('')),
  legalEntity: z.string().min(1, 'Legal entity is required'),
  currency: z.string().min(1, 'Currency is required'),
  taxId: z.string().min(1, 'Tax ID is required'),
  taxIdType: z.string().or(z.literal('')).optional(),
  // Accept website with or without protocol, and allow empty string
  website: z
    .string()
    .or(z.literal(''))
    .refine(
      (val) => {
        if (!val || val.trim() === '') return true;
        // Accept if starts with http(s):// or is a valid domain
        return /^https?:\/\//.test(val) || /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(val);
      },
      { message: 'Invalid website URL' }
    ),
  phone: z.string().min(1, 'Phone is required'),
  timeZone: z.string().min(1, 'Time zone is required'),
  addresses: z.array(z.object({
    type: z.string(),
    street: z.string().min(1, 'Street is required'),
    address2: z.string().or(z.literal('')).optional(),
    city: z.string().min(1, 'City is required'),
    zip: z.string().min(1, 'ZIP code is required'),
    country: z.string().min(1, 'Country is required'),
    timeZone: z.string().or(z.literal('')).optional(),
  })).min(1, 'At least one address is required'),
});

// Remove legacy Zod schema and types
// Remove all references to mounted
// Remove legacy useEffect for country/currency/phone
// Remove OrgDetails type if not used
// Remove uploadLogoToImageKit if not used
// Ensure all form fields use addresses array structure

export function OrganizationDetailsStep({ isLoading, orgId, userId, onNext, onPrevious, businessType, orgName, orgSlug }: OrganizationDetailsStepProps) {
  // Memoize the timezone options for performance (must be inside component for SSR)
  const TIMEZONE_OPTIONS = useMemo(() => {
    const tzs = getTimeZones();
    // List of popular cities to prioritize
    const popularCities = [
      'Europe/London', 'Europe/Berlin', 'Europe/Paris', 'Europe/Madrid', 'Europe/Rome',
      'Europe/Amsterdam', 'Europe/Zurich', 'Europe/Prague', 'Europe/Warsaw',
      'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
      'America/Toronto', 'America/Vancouver', 'America/Sao_Paulo',
      'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Singapore', 'Asia/Hong_Kong',
      'Australia/Sydney'
    ];
    // Helper to get offset string
    function getOffsetString(offsetMinutes: number) {
      const sign = offsetMinutes >= 0 ? '+' : '-';
      const absMinutes = Math.abs(offsetMinutes);
      const hours = Math.floor(absMinutes / 60);
      const minutes = absMinutes % 60;
      return `GMT${sign}${hours}${minutes !== 0 ? ':' + minutes.toString().padStart(2, '0') : ''}`;
    }
    // Build options
    const options = tzs.map((tz: any) => {
      const offsetStr = getOffsetString(tz.currentTimeOffsetInMinutes);
      // Use main city if available, else fallback to tz name
      const city = tz.mainCities && tz.mainCities.length > 0 ? tz.mainCities[0] : tz.name.split('/').pop();
      return {
        value: tz.name,
        label: `${city} (${offsetStr})`,
        offset: tz.currentTimeOffsetInMinutes,
        isPopular: popularCities.includes(tz.name)
      };
    });
    // Sort: popular cities first, then by offset, then by city name
    options.sort((a, b) => {
      if (a.isPopular && !b.isPopular) return -1;
      if (!a.isPopular && b.isPopular) return 1;
      if (a.offset !== b.offset) return a.offset - b.offset;
      return a.label.localeCompare(b.label);
    });
    return options;
  }, []);

  // Only get onboarding data once on mount for initial values
  const { data: initialOnboardingData, updateData } = useOnboardingStore();

  // Always merge with defaults to avoid undefined fields
  const defaultOrgDetails: OrganizationDetailsFormValues = {
    email: '',
    industry: '',
    legalEntity: '',
    currency: '',
    taxId: '',
    taxIdType: '',
    website: '',
    phone: '',
    timeZone: '',
    addresses: [{ type: 'primary', street: '', address2: '', city: '', zip: '', country: '', timeZone: '' }],
  };
  const orgDetailsRaw: Partial<OrganizationDetailsFormValues> = initialOnboardingData.orgDetails || {};
  const orgDetails: OrganizationDetailsFormValues = {
    ...defaultOrgDetails,
    ...orgDetailsRaw,
    addresses: Array.isArray(orgDetailsRaw?.addresses) && orgDetailsRaw.addresses.length > 0
      ? orgDetailsRaw.addresses
      : defaultOrgDetails.addresses,
  };

  const form = useForm<OrganizationDetailsFormValues>({
    resolver: zodResolver(OrganizationDetailsSchema),
    defaultValues: orgDetails,
  });

  // Debounced auto-save: save form data to onboarding store 2s after user stops typing
  const debouncedAutoSave = useDebouncedCallback((values: OrganizationDetailsFormValues) => {
    const mainAddress = values.addresses[0] || {};
    const orgDetailsUpdate = {
      email: values.email,
      industry: values.industry,
      legalEntity: values.legalEntity,
      website: values.website,
      phone: values.phone,
      taxId: values.taxId,
      taxIdType: values.taxIdType,
      currency: values.currency,
      timeZone: values.timeZone,
      address: {
        streetAddress: mainAddress.street,
        address2: mainAddress.address2,
        city: mainAddress.city,
        zipCode: mainAddress.zip,
        country: mainAddress.country,
        timeZone: mainAddress.timeZone,
      },
      addresses: values.addresses,
    };
    updateData({ orgDetails: orgDetailsUpdate });
  }, 2000); // 2 seconds debounce

  // Watch all form values and trigger debounced auto-save
  useEffect(() => {
    const subscription = form.watch((values) => {
      debouncedAutoSave(values as OrganizationDetailsFormValues);
    });
    return () => subscription.unsubscribe();
  }, [form, debouncedAutoSave]);

  // Do NOT subscribe to onboarding store changes after mount
  // If you want to support "reset" or "reload" from the store, use a button to trigger form.reset()

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "addresses"
  });

  // Avoid using form.watch in render body to prevent lag
  const [showAdditionalAddresses, setShowAdditionalAddresses] = useState(() => {
    const addresses = form.getValues("addresses") as any[];
    return addresses && addresses.length > 1;
  });

  useEffect(() => {
    const subscription = form.watch((values) => {
      const addresses = (values as OrganizationDetailsFormValues).addresses;
      setShowAdditionalAddresses(addresses && addresses.length > 1);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Use orgDetails for all address/contact fields
  // const orgDetails: OrgDetails = onboardingData.orgDetails || {};

  // const form = useForm({
  //   resolver: zodResolver(OrganizationDetailsSchema),
  //   mode: "onBlur",
  //   defaultValues: {
  //     email: onboardingData.organization?.email || "",
  //     industry: onboardingData.organization?.industry || "",
  //     website: orgDetails.website || "",
  //     legalEntity: onboardingData.organization?.legalEntity || "",
  //     street: orgDetails.address || "",
  //     city: orgDetails.city || "",
  //     zip: orgDetails.zipCode || "",
  //     country: orgDetails.country || "US",
  //     timeZone: orgDetails.timeZone || "",
  //     phoneNumber: orgDetails.phone || "",
  //   },
  // });

  // useEffect(() => {
  //   if (onboardingData) {
  //     form.reset({
  //       email: onboardingData.organization?.email || "",
  //       industry: onboardingData.organization?.industry || "",
  //       website: orgDetails.website || "",
  //       legalEntity: onboardingData.organization?.legalEntity || "",
  //       street: orgDetails.address || "",
  //       city: orgDetails.city || "",
  //       zip: orgDetails.zipCode || "",
  //       country: orgDetails.country || "US",
  //       timeZone: orgDetails.timeZone || "",
  //       phoneNumber: orgDetails.phone || "",
  //     });
  //   }
  // }, [onboardingData, form]);

  // Remove the real-time slug generation useEffect

  const [currentTaxIdInfo, setCurrentTaxIdInfo] = useState(taxIdInfo.US);

  const watchCountry = form.watch("addresses.0.country");

  useEffect(() => {
    // Update tax id info based on country
    const countryCode = watchCountry as keyof typeof taxIdInfo;
    setCurrentTaxIdInfo(taxIdInfo[countryCode] || taxIdInfo.default);

    // Update currency and phone based on country
    if (watchCountry) {
      const currency = countryCurrencyMap[watchCountry] || "USD";
      form.setValue("currency", currency);

      // Find the corresponding country for phone number
      const matchingCountry = countries.find(country => country.code === watchCountry);
      if (matchingCountry) {
        // Update phone number to match the new country's phone code
        const currentPhoneNumber = form.getValues("phone");
        const newPhoneNumber = currentPhoneNumber 
          ? currentPhoneNumber.replace(/^\+\d+/, `+${matchingCountry.phone}`)
          : `+${matchingCountry.phone}`;
        form.setValue("phone", newPhoneNumber);
      }
    }
  }, [watchCountry, form]);

  // Remove checkSlugAvailability and all slug-related logic from this file

  // On submit, collect all fields (root + addresses) and save to orgDetails
  const onSubmit = async (values: OrganizationDetailsFormValues) => {
    const mainAddress = values.addresses[0] || {};
    // Normalize website: prepend https:// if missing and not empty
    let website = values.website || '';
    if (website && !/^https?:\/\//.test(website)) {
      website = `https://${website}`;
    }
    const orgDetailsUpdate = {
      email: values.email,
      industry: values.industry,
      legalEntity: values.legalEntity,
      website,
      phone: values.phone,
      taxId: values.taxId,
      taxIdType: values.taxIdType,
      currency: values.currency,
      timeZone: values.timeZone,
      address: {
        streetAddress: mainAddress.street,
        address2: mainAddress.address2,
        city: mainAddress.city,
        zipCode: mainAddress.zip,
        country: mainAddress.country,
        timeZone: mainAddress.timeZone,
      },
      addresses: values.addresses,
    };
    try {
      await updateOnboardingStep(
        userId,
        ONBOARDING_STEPS.ORGANIZATION_DETAILS,
        orgDetailsUpdate
      );
      updateData({ orgDetails: orgDetailsUpdate });
      onNext(orgDetailsUpdate);
    } catch (err) {
      console.error('Failed to update onboarding org details:', err);
    }
  };

  const industries = ["Accounting", "Agriculture", "Automotive", "Construction", "Consulting", "E-commerce", "Education", "Finance", "Food & Beverage", "Healthcare", "Manufacturing", "Marketing", "Non-profit", "Real Estate", "Retail", "SaaS", "Technology", "Transportation", "Other"];
  const legalEntities = ["Sole Proprietorship", "Partnership", "LLC", "Corporation", "Non-profit"];
  const currencyList = Object.entries(currencies).map(([code, { name }]) => ({ code, name: `${name} (${code})` }));

  return (
    <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6 md:space-y-8 px-4 sm:px-6">
      <div className="text-center space-y-2 sm:space-y-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-white">Tell us about your organization</h2>
        <p className="text-sm sm:text-base text-gray-300 max-w-2xl mx-auto">We'll use this information to set up your business profile and customize your experience.</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card className="bg-gray-800/50 border-gray-600">
            <CardHeader className="pb-4 sm:pb-6">
              <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl text-white"><Building2 className="w-4 h-4 sm:w-5 sm:h-5" /><span>Organization Information</span></CardTitle>
              <CardDescription className="text-sm text-gray-300">
                {/* {mounted ? `Basic details about your ${normalizedBusinessType}` : 'Basic details about your business'} */}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Email *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                          onBlur={(e) => {
                            updateData({
                              organization: {
                                ...(initialOnboardingData.organization || {}),
                                email: e.target.value
                              }
                            });
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="industry" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || ''}
                    >
                      <FormControl>
                        <div
                          tabIndex={-1}
                          onBlur={() => {
                            updateData({
                              organization: {
                                ...(initialOnboardingData.organization || {}),
                                industry: field.value
                              }
                            });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select your industry" />
                          </SelectTrigger>
                        </div>
                      </FormControl>
                      <SelectContent>
                        {industries.map(i => <SelectItem key={i} value={i}>{i}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField control={form.control} name="legalEntity" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Legal Entity *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || ''}
                    >
                      <FormControl>
                        <div
                          tabIndex={-1}
                          onBlur={() => {
                            updateData({
                              organization: {
                                ...(initialOnboardingData.organization || {}),
                                legalEntity: field.value
                              }
                            });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select your legal entity" />
                          </SelectTrigger>
                        </div>
                      </FormControl>
                      <SelectContent>
                        {legalEntities.map(e => <SelectItem key={e} value={e}>{e}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="website" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="www.alvecomm.com"
                        {...field}
                        onBlur={(e) => {
                          updateData({
                            organization: {
                              ...(initialOnboardingData.organization || {}),
                              website: e.target.value
                            }
                          });
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              {/* Moved from Contact Information section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField control={form.control} name="taxId" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax ID *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Tax ID"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="taxIdType" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax ID Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || ''}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tax ID type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="EIN">EIN</SelectItem>
                        <SelectItem value="SSN">SSN</SelectItem>
                        <SelectItem value="ITIN">ITIN</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-600">
            <CardHeader className="pb-4 sm:pb-6">
              <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl text-white"><MapPin className="w-4 h-4 sm:w-5 sm:h-5" /><span>Business Address</span></CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6">
              {/* Time Zone Selector */}
              <Controller
                control={form.control}
                name="timeZone"
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select time zone" />
                    </SelectTrigger>
                    <SelectContent className="max-h-96 overflow-y-auto">
                      {TIMEZONE_OPTIONS.map((tz) => (
                        <SelectItem key={tz.value} value={tz.value}>{tz.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />

              {/* Main Address (always present) */}
              <h3>Main Address</h3>
              <Input
                {...form.register('addresses.0.street')}
                placeholder="Street"
              />
              <Input
                {...form.register('addresses.0.address2')}
                placeholder="Address Line 2"
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField control={form.control} name="addresses.0.city" render={({ field }) => (
                  <FormItem>
                    <FormLabel>City *</FormLabel>
                    <FormControl>
                      <Input {...form.register('addresses.0.city')} placeholder="City" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="addresses.0.zip" render={({ field }) => (
                  <FormItem>
                    <FormLabel>ZIP Code *</FormLabel>
                    <FormControl>
                      <Input {...form.register('addresses.0.zip')} placeholder="ZIP Code" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField control={form.control} name="addresses.0.country" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country *</FormLabel>
                    <FormControl>
                      <CountrySelector
                        value={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="phone" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number *</FormLabel>
                    <FormControl>
                      <PhoneNumberInput
                        value={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              {/* Currency field moved here */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField control={form.control} name="currency" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Currency *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || ''}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select your currency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {currencyList.map(c => <SelectItem key={c.code} value={c.code}>{c.name}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button variant="outline" onClick={onPrevious} disabled={isLoading}>Previous</Button>
            <Button type="submit" disabled={isLoading}>{isLoading ? 'Saving...' : 'Next'}</Button>
          </div>
        </form>
      </Form>
    </div>
  );
}