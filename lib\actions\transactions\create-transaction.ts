"use server";

import { z } from "zod";
import { db } from "@/db/drizzle";
import { transactions, journalEntries, accounts, auditLogs, bills } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq, and } from "drizzle-orm";
import { randomUUID } from 'crypto';

const transactionFormSchema = z.object({
  transactionType: z.enum(["income", "expense"]),
  amount: z.coerce.number().positive(),
  accountId: z.string().min(1),
  date: z.string().min(1),
  description: z.string().min(1),
});

async function getUserOrganizationId(userId: string) {
  const { member } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: member.organizationId })
.from(member)
.where(eq(member.userId, userId));
  return orgs[0]?.organizationId || null;
}

async function getDefaultAccount(organizationId: string, type: "revenue" | "expense") {
  const result = await db
    .select()
    .from(accounts)
    .where(and(eq(accounts.organizationId, organizationId), eq(accounts.type, type)))
    .limit(1);
  return result[0];
}

export async function createTransaction(formData: FormData) {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;
  if (!userId) return { success: false, message: "Not authenticated." };
  const organizationId = await getUserOrganizationId(userId);
  if (!organizationId) return { success: false, message: "No organization found." };

  const parsed = transactionFormSchema.safeParse({
    transactionType: formData.get("transactionType"),
    amount: formData.get("amount"),
    accountId: formData.get("accountId"),
    date: formData.get("date"),
    description: formData.get("description"),
  });
  if (!parsed.success) {
    return { success: false, message: "Invalid input." };
  }
  const { transactionType, amount, accountId, date, description } = parsed.data;

  const defaultAccount = await getDefaultAccount(organizationId, transactionType === "income" ? "revenue" : "expense");
  if (!defaultAccount) {
    return { success: false, message: `No default ${transactionType} account found.` };
  }

  try {
    const invoiceId = formData.get("invoiceId") || undefined;
    const billId = formData.get("billId") || undefined;
    const [tx] = await db
      .insert(transactions)
      .values({
        id: randomUUID(),
        organizationId: organizationId,
        currency: "USD",
        date: new Date(date),
        description,
        totalAmount: amount.toString(),
        status: "posted",
        type: transactionType === "income" ? "simple_income" : "simple_expense",
        createdAt: new Date(),
        updatedAt: new Date(),
        ...(invoiceId ? { invoiceId: String(invoiceId) } : {}),
        ...(billId ? { billId: String(billId) } : {}),
      })
      .returning();
    if (!tx) throw new Error("Failed to create transaction");

    // If billId is present, update the bill's transactionId and payment fields
    if (billId) {
      const billIdStr = String(billId);
      await db.update(bills)
        .set({ transactionId: tx.id })
        .where(eq(bills.id, billIdStr));
      // Sum all payments for this bill
      const payments = await db.query.transactions.findMany({
        where: eq(transactions.billId, billIdStr),
      });
      const totalPaid = payments.reduce((sum, t) => sum + Number(t.totalAmount), 0);
      // Get the bill
      const bill = await db.query.bills.findFirst({ where: eq(bills.id, billIdStr) });
      if (bill) {
        const total = Number(bill.total);
        const amountPaid = totalPaid;
        const amountRemaining = Math.max(0, total - amountPaid);
        const overpaid = Math.max(0, amountPaid - total);
        let status = bill.status;
        if (amountRemaining === 0 && overpaid === 0) status = "paid";
        // Do not set status to 'partial' (not in enum), just keep as 'paid' or original
        await db.update(bills)
          .set({
            amountPaid: amountPaid.toFixed(2),
            amountRemaining: amountRemaining.toFixed(2),
            overpaid: overpaid.toFixed(2),
            status,
          })
          .where(eq(bills.id, billIdStr));
      }
    }

    if (transactionType === "income") {
      await db.insert(journalEntries).values([
        {
          id: randomUUID(),
          transactionId: tx.id,
          accountId,
          debitAmount: amount.toString(),
          creditAmount: "0.00",
          description: `Income: ${description}`,
        },
        {
          id: randomUUID(),
          transactionId: tx.id,
          accountId: defaultAccount.id,
          debitAmount: "0.00",
          creditAmount: amount.toString(),
          description: `Income: ${description}`,
        },
      ]);
    } else {
      await db.insert(journalEntries).values([
        {
          id: randomUUID(),
          transactionId: tx.id,
          accountId: defaultAccount.id,
          debitAmount: amount.toString(),
          creditAmount: "0.00",
          description: `Expense: ${description}`,
        },
        {
          id: randomUUID(),
          transactionId: tx.id,
          accountId,
          debitAmount: "0.00",
          creditAmount: amount.toString(),
          description: `Expense: ${description}`,
        },
      ]);
    }
    await db.insert(auditLogs).values({
      id: randomUUID(),
      organizationId,
      userId,
      action: "create",
      resourceType: "transaction",
      resourceId: tx.id,
      oldValues: null,
      newValues: tx,
      createdAt: new Date(),
    });
    return { success: true, message: "Transaction created." };
  } catch (error) {
    return { success: false, message: "Failed to create transaction." };
  }
} 