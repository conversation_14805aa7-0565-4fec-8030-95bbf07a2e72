"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { invoices, invoiceItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import {
  invoiceFormSchema,
  lineItemSchema,
} from "@/lib/actions/invoices/invoice.schema";
import { randomUUID } from 'crypto';

type InvoiceFormData = z.infer<typeof invoiceFormSchema>;
type LineItem = z.infer<typeof lineItemSchema>;

export async function updateInvoice(invoiceId: string, data: InvoiceFormData) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  const validatedFields = invoiceFormSchema.safeParse(data);

  if (!validatedFields.success) {
    throw new Error("Invalid fields provided.");
  }

  const { lineItems, totalAmount, issueDate, ...invoiceData } = validatedFields.data;

  try {
    // Update the invoice first
    await db
      .update(invoices)
      .set({
        ...invoiceData,
        total: totalAmount.toString(),
        subtotal: totalAmount.toString(),
        tax: invoiceData.tax?.toString() || "0.00",
        date: issueDate,
      })
      .where(
        and(
          eq(invoices.id, invoiceId),
          eq(invoices.organizationId, organizationId)
        )
      );

    // Delete existing line items
    await db
      .delete(invoiceItems)
      .where(eq(invoiceItems.invoiceId, invoiceId));

    // Insert new line items if any exist
    if (lineItems && lineItems.length > 0) {
      await db.insert(invoiceItems).values(
        lineItems.map((item: LineItem) => ({
          id: randomUUID(),
          productId: item.productId || null,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice.toString(),
          total: item.totalPrice.toString(),
          invoiceId: invoiceId,
        }))
      );
    }

    revalidatePath("/invoices");
    return { success: true, message: "Invoice updated successfully." };
  } catch (error) {
    console.error(error);
    return { success: false, message: "Failed to update invoice." };
  }
} 