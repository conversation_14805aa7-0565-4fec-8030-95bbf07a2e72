"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";

export async function getBudgetPeriods() {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    return await db
      .select()
      .from(budgetPeriods)
      .where(eq(budgetPeriods.organizationId, organizationId))
      .orderBy(desc(budgetPeriods.startDate));
  } catch (error) {
    console.error("Error fetching budget periods:", error);
    return [];
  }
} 