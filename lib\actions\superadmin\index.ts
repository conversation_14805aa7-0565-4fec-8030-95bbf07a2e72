import { db } from "@/db/drizzle";
import { user as userTable, account as accountTable } from "@/db/schema/schema";
import { eq } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { Resend } from "resend";
import jwt from "jsonwebtoken";
import { createAuditLog } from "@/lib/actions/audit/create-audit-log";
import { user, account, member, organization } from "@/db/schema/schema";

const SUPERADMINS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

const resend = new Resend(process.env.RESEND_API_KEY);

export async function requireSuperadmin(session: any) {
  if (!session || !SUPERADMINS.includes(session.user.email)) {
    throw new Error("Unauthorized");
  }
  return session;
}

export async function impersonateUser(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  // SECURITY: This should only be used by superadmins for admin purposes.
  // In a real implementation, you would create a new session for the target user and return a session cookie or token.
  // Here, we return a short-lived signed JWT as a placeholder.
  try {
    const token = jwt.sign(
      { userId: targetUserId, impersonated: true },
      process.env.SUPERADMIN_IMPERSONATE_SECRET || "dev-secret",
      { expiresIn: "10m" }
    );
    try { await logSuperadminAction(session, "impersonateUser", { targetUserId }); } catch {}
    return { success: true, token };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
}

export async function resetUserPassword(session: any, targetUserId: string, newPassword: string) {
  await requireSuperadmin(session);
  if (!newPassword || newPassword.length < 8) {
    return { success: false, message: "New password must be at least 8 characters." };
  }
  try {
    const ctx = await auth.$context;
    // Fetch all accounts for the user, then filter for providerId 'credential'
    const accounts = await ctx.internalAdapter.findAccount(targetUserId);
    const account = Array.isArray(accounts)
      ? accounts.find((a: any) => a.providerId === "credential")
      : accounts && accounts.providerId === "credential"
        ? accounts
        : null;
    if (!account) {
      return { success: false, message: "No password account found for this user." };
    }
    // Hash new password
    const hash = await ctx.password.hash(newPassword);
    // Update password
    await ctx.internalAdapter.updatePassword(targetUserId, hash);
    try { await logSuperadminAction(session, "resetUserPassword", { targetUserId }); } catch {}
    return { success: true };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
}

export async function activateUser(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  try {
    await db.update(userTable).set({ onboarded: true, updatedAt: new Date() }).where(eq(userTable.id, targetUserId));
    try { await logSuperadminAction(session, "activateUser", { targetUserId }); } catch {}
    return { success: true };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
}

export async function inactivateUser(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  try {
    await db.update(userTable).set({ onboarded: false, updatedAt: new Date() }).where(eq(userTable.id, targetUserId));
    try { await logSuperadminAction(session, "inactivateUser", { targetUserId }); } catch {}
    return { success: true };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
}

export async function getUserDetails(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  // TODO: Implement user details fetch logic
  return { success: false, message: "Not implemented" };
}

export async function deleteUser(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  try {
    // Soft delete: set onboarded to false and anonymize email
    await db.update(userTable)
      .set({ onboarded: false, email: `deleted+${targetUserId}@example.com`, updatedAt: new Date() })
      .where(eq(userTable.id, targetUserId));
    try { await logSuperadminAction(session, "deleteUser", { targetUserId }); } catch {}
    return { success: true };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
}

export async function sendSystemEmail(session: any, targetUserId: string, subject: string, body: string) {
  await requireSuperadmin(session);
  try {
    // Fetch user email
    const [targetUser] = await db.select().from(userTable).where(eq(userTable.id, targetUserId));
    if (!targetUser) return { success: false, message: "User not found" };
    await resend.emails.send({
      from: "NextGenBusiness <<EMAIL>>",
      to: targetUser.email,
      subject,
      text: body,
    });
    try { await logSuperadminAction(session, "sendSystemEmail", { targetUserId, subject }); } catch {}
    return { success: true };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
}

export async function getUserOrganizations(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  // TODO: Implement fetch user's organizations logic
  return { success: false, message: "Not implemented" };
}

export async function getAllUsersForSuperadmin(session: any) {
  await requireSuperadmin(session);
  // Fetch all users with basic info
  const users = await db.select({
    id: user.id,
    name: user.name,
    email: user.email,
    isActive: user.onboarded, // or use a dedicated isActive field if available
    createdAt: user.createdAt,
  }).from(user);
  return users;
}

export async function getUserDetailsForSuperadmin(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  const result = await db.select({
    id: user.id,
    name: user.name,
    email: user.email,
    emailVerified: user.emailVerified,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    onboarded: user.onboarded,
    twoFactorEnabled: user.twoFactorEnabled,
    // Removed jobTitle, phone, defaultOrganizationId
  }).from(user).where(eq(user.id, targetUserId)).limit(1);
  return result[0] || null;
}

export async function getUserOrganizationsForSuperadmin(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  const orgs = await db
    .select({
      organizationId: member.organizationId,
name: organization.name,
role: member.role,
joinedAt: member.createdAt,
    })
    .from(member)
.innerJoin(organization, eq(member.organizationId, organization.id))
.where(eq(member.userId, targetUserId));
  return orgs;
}

export async function logSuperadminAction(session: any, action: string, details: any) {
  await requireSuperadmin(session);
  try {
    await createAuditLog({
      action: "update", // or "admin_action" if you want to extend AuditAction
      resourceType: "user",
      resourceId: details?.targetUserId || undefined,
      description: `Superadmin action: ${action}`,
      metadata: details,
      riskLevel: ["deleteUser", "impersonateUser"].includes(action) ? "critical" : "high",
    });
    return { success: true };
  } catch (error) {
    console.error("Failed to log superadmin action:", error);
    return { success: false, message: error instanceof Error ? error.message : String(error) };
  }
} 