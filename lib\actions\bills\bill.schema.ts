import { z } from "zod";
import { lineItemSchema } from "@/lib/actions/invoices/invoice.schema";

export const billFormSchema = z.object({
  vendorId: z.string().min(1, "Please select a vendor."),
  billNumber: z.string().min(1, "Bill number is required."),
  currency: z.string().min(2, "Currency is required."),
  date: z.coerce.date(),
  dueDate: z.coerce.date().optional(),
  status: z.enum(["draft", "submitted", "approved", "paid", "void"]),
  lineItems: z.array(lineItemSchema).min(1, "Bill must have at least one line item."),
  tax: z.coerce.number().optional(),
  appliedCredit: z.number().optional(),
});

export type NewBill = z.infer<typeof billFormSchema>;
