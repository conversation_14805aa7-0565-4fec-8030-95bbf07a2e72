"use client";

import React, { useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { baseColumns, Account } from "./columns";
import { AccountsDataTable } from "./accounts-data-table";
import AddAccountSheet from "./AddAccountSheet";
import { deleteAccounts } from "@/lib/actions/accounting/delete-accounts";
import { updateAccount } from "@/lib/actions/accounting/update-account";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";
import { createAccount } from "@/lib/actions/accounting/create-account";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import DeleteConfirmationDialog from "@/components/general/DeleteConfirmationDialog";
import { RowSelectionState } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { FormState, NewAccount } from "@/lib/actions/accounting/account.schema";
import { Checkbox } from "@/components/ui/checkbox";
import ImportAccountsSheetServer from "./ImportAccountsSheetServer";
import { AccountAnalytics } from "./AccountAnalytics";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, BarChart3 } from "lucide-react";

export default function AccountsPageClient({ organizationId }: { organizationId: string }) {
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editingAccountId, setEditingAccountId] = React.useState<string | null>(null);
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [accountsToDelete, setAccountsToDelete] = React.useState<string[]>([]);
  const [importDialogOpen, setImportDialogOpen] = React.useState(false);
  const [analyticsOpen, setAnalyticsOpen] = React.useState(false);
  const [sorting, setSorting] = React.useState([{ id: "accountNumber", desc: false }]);

  const { data: accounts = [], isLoading: isLoadingAccounts } = useQuery({
    queryKey: ["accounts"],
    queryFn: getAccounts,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  // Ensure isHeader is always boolean
  const normalizedAccounts = accounts.map(acc => ({ ...acc, isHeader: !!acc.isHeader }));

  const { mutate: mutateCreateUpdate, isPending: isCreatingOrUpdating } = useMutation<
    FormState,
    Error,
    { id?: string; accountData: NewAccount }
  >({
    mutationFn: ({ id, accountData }) => {
      if (id) {
        // Convert all undefined values to null for database compatibility
        const safeAccountData = {
          ...accountData,
          accountNumber: accountData.accountNumber ?? null,
          description: accountData.description ?? null,
          parentId: accountData.parentId ?? null,
          classification: accountData.classification ?? null,
          financialStatementSection: accountData.financialStatementSection ?? null,
          accountGroup: accountData.accountGroup ?? null,
          cashFlowCategory: accountData.cashFlowCategory ?? null,
          subcategory: accountData.subcategory ?? null,
          gaapCategory: accountData.gaapCategory ?? null,
          ifrsCategory: accountData.ifrsCategory ?? null,
          notes: accountData.notes ?? null,
        };
        return updateAccount(id, safeAccountData);
      } else {
        const formData = new FormData();
        Object.entries(accountData).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            formData.append(key, String(value));
          }
        });
        return createAccount({ success: false, message: "" }, formData);
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["accounts"] });
        setDialogOpen(false);
        setEditingAccountId(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });

  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (ids: string[]) => deleteAccounts(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["accounts"] });
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
      setDeleteDialogOpen(false);
      setAccountsToDelete([]);
    },
    onError: (error) => {
      toast.error(error.message);
      setDeleteDialogOpen(false);
      setAccountsToDelete([]);
    },
  });

  const selectedAccountIds = useMemo(
    () =>
      Object.keys(rowSelection)
        .map((key) => accounts[parseInt(key, 10)]?.id)
        .filter(Boolean),
    [rowSelection, accounts]
  );

  const selectedAccount = selectedAccountIds.length === 1 ? accounts.find(acc => acc.id === selectedAccountIds[0]) : null;

  const handleSubmit = (accountData: NewAccount) => {
    mutateCreateUpdate({ id: editingAccountId ?? undefined, accountData });
  };

  const openDeleteDialog = (ids: string[]) => {
    setAccountsToDelete(ids);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    mutateDelete(accountsToDelete);
  };

  const openEditDialog = (account: Account) => {
    setEditingAccountId(account.id);
    setDialogOpen(true);
  };

  const openNewDialog = () => {
    setEditingAccountId(null);
    setDialogOpen(true);
  };

  const handleImportSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["accounts"] });
  };
  
  const columns = useMemo(() => [
    {
      id: "select",
      header: ({ table }: { table: any }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }: { row: any }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    ...baseColumns,
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }: { row: { original: Account } }) => (
        <div className="flex gap-2 justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openEditDialog(row.original)}
            disabled={isCreatingOrUpdating || isDeleting}
          >
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openDeleteDialog([row.original.id])}
            disabled={isCreatingOrUpdating || isDeleting}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ], [isCreatingOrUpdating, isDeleting]);

  if (isLoadingAccounts) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-28" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10 px-4 max-w-full overflow-hidden">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Chart of Accounts</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setImportDialogOpen(true)}>
            Import
          </Button>
          <Button onClick={openNewDialog}>Add Account</Button>
        </div>
      </div>

      {/* Analytics Section */}
      <Collapsible open={analyticsOpen} onOpenChange={setAnalyticsOpen} className="mb-6">
        <CollapsibleTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>Professional Analytics & Insights</span>
            </div>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-4">
          <AccountAnalytics />
        </CollapsibleContent>
      </Collapsible>

      {/* Selected Items Bar */}
      {selectedAccountIds.length > 0 && (
        <div className="flex justify-between items-center p-2 bg-muted rounded-md mb-4">
          <span>{selectedAccountIds.length} account{selectedAccountIds.length > 1 ? 's' : ''} selected</span>
          <div className="flex gap-2">
            {selectedAccount && (
              <Button
                variant="secondary"
                size="sm"
                onClick={() => openEditDialog(selectedAccount)}
              >
                Edit Account
              </Button>
            )}
            <Button
              variant="destructive"
              size="sm"
              onClick={() => openDeleteDialog(selectedAccountIds)}
              disabled={isDeleting}
            >
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      {/* Main Data Table with Professional Columns */}
      <AccountsDataTable<any, unknown>
        columns={columns}
        data={normalizedAccounts}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        sorting={sorting}
        setSorting={setSorting}
        openEditDialog={openEditDialog}
        openDeleteDialog={openDeleteDialog}
      />
      
      <AddAccountSheet
        open={dialogOpen}
        onOpenChange={(open) => {
          if (!open) setEditingAccountId(null);
          setDialogOpen(open);
        }}
        onSubmit={handleSubmit}
        accountId={editingAccountId}
        organizationId={organizationId}
      />
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        isPending={isDeleting}
        count={accountsToDelete.length}
      />
      {/* Only render ImportAccountsSheetServer if organizationId is valid */}
      {organizationId && (
        <ImportAccountsSheetServer
          open={importDialogOpen}
          onOpenChange={setImportDialogOpen}
          onImportSuccess={handleImportSuccess}
          organizationId={organizationId}
        />
      )}
    </div>
  );
} 