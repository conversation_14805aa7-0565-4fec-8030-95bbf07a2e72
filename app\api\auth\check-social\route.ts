import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { db } from "@/db/drizzle";
import { account } from "@/db/schema/schema";
import { and, eq } from "drizzle-orm";

async function hasSocialAccount(userId: string): Promise<boolean> {
  const socialAccount = await db
    .select({ providerId: account.providerId })
    .from(account)
    .where(and(
      eq(account.userId, userId),
      eq(account.providerId, "google") // Add other providers as needed
    ))
    .limit(1);
  
  return socialAccount.length > 0;
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: await headers() });
    
    if (!session) {
      return NextResponse.json({ 
        isAuthenticated: false, 
        hasSocialAccount: false 
      });
    }

    const socialAccount = await hasSocialAccount(session.user.id);
    
    return NextResponse.json({ 
      isAuthenticated: true, 
      hasSocialAccount: socialAccount,
      userId: session.user.id
    });
  } catch (error) {
    console.error("Error checking social account:", error);
    return NextResponse.json({ 
      isAuthenticated: false, 
      hasSocialAccount: false,
      error: "Failed to check social account" 
    }, { status: 500 });
  }
} 