"use server";
import { db } from "@/db/drizzle";
import { user as userTable } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

const nameRateLimit: Record<string, number> = {};

export async function updateUserName(userId: string, newName: string) {
  // Rate limit: 1 update per 10 seconds per user
  const now = Date.now();
  if (nameRateLimit[userId] && now - nameRateLimit[userId] < 10000) {
    return { error: "You can only update your name once every 10 seconds." };
  }
  // Validation: only letters, spaces, hyphens, apostrophes, 2-50 chars
  const trimmed = newName.trim();
  if (!trimmed || trimmed.length < 2 || trimmed.length > 50) {
    return { error: "Name must be 2-50 characters." };
  }
  if (!/^[A-Za-z\s\-']+$/.test(trimmed)) {
    return { error: "Name can only contain letters, spaces, hyphens, and apostrophes." };
  }
  try {
    await db.update(userTable).set({ name: trimmed }).where(eq(userTable.id, userId));
    nameRateLimit[userId] = now;
    return { success: true };
  } catch (error) {
    return { error: error instanceof Error ? error.message : String(error) };
  }
} 