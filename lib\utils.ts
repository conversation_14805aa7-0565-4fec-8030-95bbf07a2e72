import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Converts a 2-letter country code to a flag emoji.
 * @param {string} countryCode The 2-letter country code (ISO 3166-1 alpha-2).
 * @returns {string} The flag emoji.
 */
export function getFlagEmoji(countryCode: string): string {
  if (!/^[A-Z]{2}$/.test(countryCode)) {
    return '';
  }
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt(0));
  return String.fromCodePoint(...codePoints);
}

export const taxIdInfo: Record<string, { name: string; placeholder: string, types?: string[] }> = {
  'US': { name: 'Tax ID', placeholder: 'Enter EIN, SSN, or ITIN', types: ['EIN', 'SSN', 'ITIN'] },
  'GB': { name: 'UTR / VAT Reg No', placeholder: 'Enter UTR or VAT Registration Number' },
  'CA': { name: 'Business Number / GST/HST number', placeholder: 'Enter Business Number' },
  'AU': { name: 'ABN / TFN', placeholder: 'Enter ABN or TFN' },
  'DE': { name: 'Steuer-ID / USt-IdNr.', placeholder: 'Enter Steuer-Identifikationsnummer' },
  'FR': { name: 'Numéro SIREN / Numéro de TVA', placeholder: 'Enter Numéro SIREN' },
  'IN': { name: 'PAN / GSTIN', placeholder: 'Enter PAN or GSTIN' },
  'BR': { name: 'CNPJ / CPF', placeholder: 'Enter CNPJ or CPF' },
  'default': { name: 'Business Tax ID', placeholder: 'Enter your business tax ID' },
};
