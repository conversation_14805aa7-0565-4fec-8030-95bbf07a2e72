import { db } from "@/db/drizzle";
import { member } from "@/db/schema/schema";
import { organization } from "@/db/schema/schema";
import { requireSuperadmin } from "./index";
import { eq } from "drizzle-orm";

export async function getUserOrganizationsForSuperadmin(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  const orgs = await db
    .select({
      organizationId: member.organizationId,
name: organization.name,
role: member.role,
joinedAt: member.createdAt,
    })
    .from(member)
.innerJoin(organization, eq(member.organizationId, organization.id))
.where(eq(member.userId, targetUserId));
  return orgs;
} 