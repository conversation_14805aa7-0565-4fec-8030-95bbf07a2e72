"use server";

import { db } from "@/db/drizzle";
import { organization } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";

type ProductType = "service" | "digital_service" | "physical_product" | "digital_product" | "subscription" | "bundle";

/**
 * Get intelligent product setup suggestions
 */
export async function getProductSuggestions(
  productType: ProductType,
  productName?: string
) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    
    // Get organization business type
    const org = await db
      .select({ businessType: organization.businessType })
      .from(organization)
      .where(eq(organization.id, orgId))
      .limit(1);

    const businessType = org[0]?.businessType || 'other';
    
    // Return basic suggestions based on product type
    return {
      revenueAccount: [],
      inventoryAccount: [],
      defaultCostStructure: {
        estimatedMargin: 50,
        typicalCostFactors: ["Direct costs", "Labor", "Materials"]
      }
    };
  } catch (error) {
    console.error("Failed to get product suggestions:", error);
    return {
      revenueAccount: [],
      inventoryAccount: [],
      defaultCostStructure: {
        estimatedMargin: 50,
        typicalCostFactors: ["Direct costs", "Labor", "Materials"]
      }
    };
  }
} 