"use server";

import { db } from "@/db/drizzle";
import { invoices, clients } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, ne, lte } from "drizzle-orm";
import { ageBucket } from "./age-bucket";

export async function getARAgingDetail(asOfDateStr?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;
    const asOfDate = asOfDateStr ? new Date(asOfDateStr) : new Date();

    const openInvoices = await db.select({
        invoiceId: invoices.id,
        clientName: clients.name,
        invoiceDate: invoices.date,
        dueDate: invoices.dueDate,
        total: invoices.total,
        status: invoices.status,
    }).from(invoices)
      .leftJoin(clients, eq(invoices.clientId, clients.id))
      .where(and(
          eq(invoices.organizationId, organizationId),
          ne(invoices.status, 'paid'),
          ne(invoices.status, 'void'),
          lte(invoices.date, asOfDate)
      ));

    const detailed = openInvoices.map(invoice => {
        const bucket = ageBucket(invoice.dueDate!, asOfDate);
        return {
            ...invoice,
            bucket,
            amount: parseFloat(invoice.total)
        };
    });
    
    return detailed;
} 