"use client";
import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  Re<PERSON>ons<PERSON><PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
import { Card } from "@/components/ui/card";

interface FinancialChartsProps {
  balanceSheet: {
    totalAssets: number;
    totalLiabilities: number;
    totalEquity: number;
  };
  incomeStatement: {
    totalRevenue: number;
    totalExpenses: number;
  };
  comparative?: boolean;
}

const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884D8",
  "#82CA9D",
];

const FinancialCharts = ({ balanceSheet, incomeStatement, comparative = false }: FinancialChartsProps) => {
  // Prepare data for Balance Sheet composition
  const balanceSheetData = [
    {
      name: "Assets",
      value: balanceSheet.totalAssets,
    },
    {
      name: "Liabilities",
      value: balanceSheet.totalLiabilities,
    },
    {
      name: "Equity",
      value: balanceSheet.totalEquity,
    },
  ];

  // Prepare data for Income vs Expenses
  const incomeExpenseData = [
    {
      name: "Revenue",
      value: incomeStatement.totalRevenue,
    },
    {
      name: "Expenses",
      value: incomeStatement.totalExpenses,
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-6 mt-6">
      <Card className="p-4">
        <h3 className="text-lg font-medium mb-4">Balance Sheet Composition</h3>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={balanceSheetData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={100}
              label
            >
              {balanceSheetData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </Card>
      <Card className="p-4">
        <h3 className="text-lg font-medium mb-4">Revenue vs Expenses</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={incomeExpenseData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default FinancialCharts; 