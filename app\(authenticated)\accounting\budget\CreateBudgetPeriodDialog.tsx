"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { createBudgetPeriod } from "@/lib/actions/budget/create-budget-period";

interface CreateBudgetPeriodDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function CreateBudgetPeriodDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateBudgetPeriodDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    isDefault: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.startDate || !formData.endDate) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Validate date range
    const start = new Date(formData.startDate);
    const end = new Date(formData.endDate);
    if (start >= end) {
      toast.error("End date must be after start date");
      return;
    }

    try {
      setIsSubmitting(true);
      const result = await createBudgetPeriod(formData);
      
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success("Budget period created successfully");
        setFormData({
          name: "",
          description: "",
          startDate: "",
          endDate: "",
          isDefault: false,
        });
        onOpenChange(false);
        onSuccess();
      }
    } catch (error) {
      console.error("Error creating budget period:", error);
      toast.error("Failed to create budget period");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Auto-generate date suggestions
  const generateDateSuggestions = () => {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    return [
      {
        name: `${currentYear} Annual Budget`,
        startDate: `${currentYear}-01-01`,
        endDate: `${currentYear}-12-31`,
      },
      {
        name: `${nextYear} Annual Budget`,
        startDate: `${nextYear}-01-01`,
        endDate: `${nextYear}-12-31`,
      },
      {
        name: `Q1 ${nextYear}`,
        startDate: `${nextYear}-01-01`,
        endDate: `${nextYear}-03-31`,
      },
      {
        name: `Q2 ${nextYear}`,
        startDate: `${nextYear}-04-01`,
        endDate: `${nextYear}-06-30`,
      },
    ];
  };

  const applySuggestion = (suggestion: { name: string; startDate: string; endDate: string }) => {
    setFormData(prev => ({
      ...prev,
      name: suggestion.name,
      startDate: suggestion.startDate,
      endDate: suggestion.endDate,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create Budget Period</DialogTitle>
          <DialogDescription>
            Set up a new budget period for tracking and managing your organization's financial planning.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Budget Period Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                placeholder="e.g., 2024 Annual Budget, Q1 2024"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                placeholder="Optional description for this budget period..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleChange("startDate", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date *</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleChange("endDate", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isDefault"
                checked={formData.isDefault}
                onCheckedChange={(checked) => handleChange("isDefault", checked as boolean)}
              />
              <Label htmlFor="isDefault" className="text-sm">
                Set as default budget period
              </Label>
            </div>
          </div>

          {/* Quick Suggestions */}
          <div className="border-t pt-4">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Quick Suggestions:
            </Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {generateDateSuggestions().map((suggestion, index) => (
                <Button
                  key={index}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => applySuggestion(suggestion)}
                  className="text-xs"
                >
                  {suggestion.name}
                </Button>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Budget Period"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 