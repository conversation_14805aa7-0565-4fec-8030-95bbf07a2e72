"use client";
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { addTeammate, getTeammates } from "@/lib/actions/team";
import { RequireUserLimit, PlanUsageCard } from "@/components/ui/FeatureGate";
import { toast } from "sonner";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface Teammate {
    id: string;
    email: string | null;
    name: string | null;
    image: string | null;
    role: "admin" | "editor" | "viewer" | "owner" | "manager" | "member" | "auditor" | null;
    status: "active" | "pending" | "declined" | "accepted" | "expired" | "removed" | null;
}

export function TeamPageClient() {
  const [teammates, setTeammates] = useState<Teammate[]>([]);
  const [newInvites, setNewInvites] = useState<{ email: string; role: "admin" | "editor" | "viewer" }[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    async function fetchTeammates() {
      setLoading(true);
      const existingTeammates = await getTeammates();
      setTeammates(existingTeammates);
      setLoading(false);
    }
    fetchTeammates();
  }, []);

  const handleAddInvite = () => {
    setNewInvites([...newInvites, { email: "", role: "editor" }]);
  };

  const handleRemoveInvite = (idx: number) => {
    setNewInvites(newInvites.filter((_, i) => i !== idx));
  };
  
  const handleInviteChange = (idx: number, field: 'email' | 'role', value: string) => {
    const updated = newInvites.map((invite, i) =>
      i === idx ? { ...invite, [field]: value } : invite
    );
    setNewInvites(updated);
  };

  const handleSendInvites = async () => {
    setSaving(true);
    try {
      const validInvites = newInvites.filter(invite => invite.email.trim() !== "");
      if (validInvites.length === 0) {
        toast.info("Please add at least one valid email to invite.");
        return;
      }
      
      await Promise.all(validInvites.map(invite => addTeammate(invite)));
      
      const existingTeammates = await getTeammates();
      setTeammates(existingTeammates);
      setNewInvites([]);
      toast.success("Invitations sent successfully!");
    } catch (error) {
      console.error("Error sending invites:", error);
      toast.error(error instanceof Error ? error.message : "Failed to send invitations");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-1/3" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-1/2 mb-2" />
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center space-x-4">
                            <Skeleton className="h-12 w-12 rounded-full" />
                            <div className="space-y-2">
                                <Skeleton className="h-4 w-[250px]" />
                                <Skeleton className="h-4 w-[200px]" />
                            </div>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Skeleton className="h-12 w-12 rounded-full" />
                            <div className="space-y-2">
                                <Skeleton className="h-4 w-[250px]" />
                                <Skeleton className="h-4 w-[200px]" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
            <div className="space-y-4">
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-1/2 mb-2" />
                    </CardHeader>
                    <CardContent>
                        <Skeleton className="h-10 w-full" />
                    </CardContent>
                </Card>
            </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Team Management</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
                <CardTitle>Existing Team Members</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-2">
                {teammates.map(tm => (
                    <div key={tm.id} className="flex items-center justify-between p-2 rounded-md bg-muted/50">
                        <div className="flex items-center gap-4">
                            <Avatar>
                                <AvatarImage src={tm.image || undefined} alt={tm.name || tm.email || ''} />
                                <AvatarFallback>{(tm.name || tm.email || 'U').charAt(0).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <div>
                                <p className="font-semibold">{tm.name || tm.email}</p>
                                <p className="text-sm capitalize text-muted-foreground">{tm.role} ({tm.status})</p>
                            </div>
                        </div>
                    </div>
                ))}
                {teammates.length === 0 && <p className="text-muted-foreground p-2">No team members yet.</p>}
                </div>
            </CardContent>
          </Card>

          <RequireUserLimit
            fallback={
              <Card>
                <CardHeader>
                    <CardTitle>Invite New Teammates</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground mb-4">
                    You've reached your user limit. Upgrade your plan to invite more team members.
                    </p>
                    <Button 
                    onClick={() => toast.info("Please upgrade your plan to invite more users")}
                    variant="outline"
                    >
                    Upgrade Plan
                    </Button>
                </CardContent>
              </Card>
            }
          >
            <Card>
              <CardHeader>
                <CardTitle>Invite New Teammates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                    {newInvites.map((invite, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                        <Input
                        type="email"
                        value={invite.email}
                        onChange={e => handleInviteChange(idx, "email", e.target.value)}
                        placeholder="<EMAIL>"
                        className="flex-1"
                        />
                        <Select value={invite.role} onValueChange={value => handleInviteChange(idx, "role", value)}>
                        <SelectTrigger className="w-[120px]">
                            <SelectValue placeholder="Role" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="admin">Admin</SelectItem>
                            <SelectItem value="editor">Editor</SelectItem>
                            <SelectItem value="viewer">Viewer</SelectItem>
                        </SelectContent>
                        </Select>
                        <Button type="button" variant="ghost" size="sm" onClick={() => handleRemoveInvite(idx)}>Remove</Button>
                    </div>
                    ))}
                </div>
                <Button
                    type="button"
                    onClick={handleAddInvite}
                    variant="outline"
                    className="mt-4"
                >
                    + Add another
                </Button>
              </CardContent>
            </Card>
          </RequireUserLimit>

          <RequireUserLimit>
            <div className="flex justify-end gap-2">
              <Button onClick={handleSendInvites} disabled={saving || newInvites.length === 0}>
                {saving ? "Sending Invites..." : "Send Invites"}
              </Button>
            </div>
          </RequireUserLimit>
        </div>

        <div className="space-y-4">
          <PlanUsageCard />
        </div>
      </div>
    </div>
  );
}