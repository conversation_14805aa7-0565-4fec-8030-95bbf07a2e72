import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { DollarSign, Calendar } from "lucide-react";

export interface Transaction {
  id: string;
  type: "income" | "expense";
  amount: number;
  description: string;
  date: string;
  category: string;
}

interface TransactionCardProps {
  transaction: Transaction;
}

const TransactionCard = ({ transaction }: TransactionCardProps) => {
  const isIncome = transaction.type === "income";

  return (
    <Card className="w-full hover:bg-muted/50 transition-colors">
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0">
          <div className="flex items-center gap-3">
            <div
              className={`h-10 w-10 rounded-full flex items-center justify-center ${
                isIncome ? "bg-green-500/10" : "bg-red-500/10"
              }`}
            >
              <DollarSign
                className={`h-5 w-5 ${
                  isIncome ? "text-green-500" : "text-red-500"
                }`}
              />
            </div>
            <div>
              <p className="font-medium">
                {transaction.description}
              </p>
              <p className="text-sm text-muted-foreground">{transaction.category}</p>
            </div>
          </div>
          <div className="text-right">
            <p
              className={`text-lg font-semibold ${
                isIncome ? "text-green-500" : "text-red-500"
              }`}
            >
              {isIncome ? "+" : "-"}${transaction.amount.toLocaleString('en-US')}
            </p>
            <div className="flex items-center justify-end gap-1 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              {new Date(transaction.date).toLocaleDateString('en-US')}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionCard; 