import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import React from "react";

interface QuickCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function QuickCreateDialog({ open, onOpenChange }: QuickCreateDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="p-0 bg-white dark:bg-gray-900 rounded-xl shadow-2xl border"
        showCloseButton={false}
        style={{
            minHeight: 0,
            position: 'fixed',
            left: '60%',
            top: '50%',
            transform: 'translateX(-50%)',
            width: '95vw',
            maxWidth: '750px',
            margin: 0,
            zIndex: 50,
          }}
      >
        <DialogTitle className="sr-only">Quick Create</DialogTitle>
        <div className="p-8 flex flex-col w-full">
          <div className="grid grid-cols-4 gap-8 w-full">
            {/* Customers/Clients */}
            <div>
              <div className="font-bold text-base mb-3">Customers</div>
              <ul className="space-y-2">
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Invoice</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Receive payment</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Statement</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Estimate</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Credit note</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Sales receipt</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Refund receipt</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Delayed credit</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Delayed charge</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Add customer</li>
              </ul>
            </div>
            {/* Suppliers */}
            <div>
              <div className="font-bold text-base mb-3">Suppliers</div>
              <ul className="space-y-2">
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Expense</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Cheque</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Bill</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Pay bills</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Purchase order</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Supplier credit</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Credit card credit</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Add supplier</li>
              </ul>
            </div>
            {/* Team */}
            <div>
              <div className="font-bold text-base mb-3">Team</div>
              <ul className="space-y-2">
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Single time activity</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Weekly timesheet</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Review time</li>
              </ul>
            </div>
            {/* Other */}
            <div>
              <div className="font-bold text-base mb-3">Other</div>
              <ul className="space-y-2">
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Task</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Bank deposit</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Transfer</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Journal entry</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Inventory qty adjustment</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Pay down credit card</li>
                <li className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1.5 py-1 cursor-pointer text-[15px]">Add product/service</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 