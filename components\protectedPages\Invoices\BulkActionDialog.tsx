import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

export interface BulkActionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  count: number;
  isPending: boolean;
}

const BulkActionDialog = ({
  isOpen,
  onClose,
  onConfirm,
  count,
  isPending,
}: BulkActionDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Invoices</DialogTitle>
        </DialogHeader>

        <div className="py-6">
          <p className="text-sm text-gray-600">
            Are you sure you want to delete {count} selected invoice
            {count !== 1 ? "s" : ""}? This action cannot be undone.
          </p>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isPending}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isPending}
          >
            {isPending ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkActionDialog;
