import { z } from "zod";
export const projectFormSchema = z.object({
  name: z.string().min(2, "Project name must be at least 2 characters."),
  clientId: z.string().optional(),
  description: z.string().optional(),
  status: z.enum([
    "not_started", "in_progress", "completed", "on_hold", "canceled"
  ]),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

export type NewProject = z.infer<typeof projectFormSchema>;

export type FormState = {
  success: boolean;
  message: string;
}; 