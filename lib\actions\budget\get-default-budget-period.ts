"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and } from "drizzle-orm";

export async function getDefaultBudgetPeriod() {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    const [period] = await db
      .select()
      .from(budgetPeriods)
      .where(
        and(
          eq(budgetPeriods.organizationId, organizationId),
          eq(budgetPeriods.isDefault, true)
        )
      )
      .limit(1);

    return period || null;
  } catch (error) {
    console.error("Error fetching default budget period:", error);
    return null;
  }
} 