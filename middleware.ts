import { NextRequest, NextResponse } from "next/server";
import { getSessionCookie } from "better-auth/cookies";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  const publicRoutes = [
    "/",
    "/signin",
    "/register",
    "/forgot-password",
    "/reset-password",
    "/verify-email",
    "/accept-invite",
    "/verify-2fa",
  ];

  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  const sessionCookie = getSessionCookie(request);
  if (!sessionCookie) {
    // Enhanced redirect with session expiry information
    const url = new URL("/signin", request.url);
    url.searchParams.set("message", "session_expired");
    return NextResponse.redirect(url);
  }

  // Session monitoring is handled by better-auth automatically
  const response = NextResponse.next();
  
  return response;
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};