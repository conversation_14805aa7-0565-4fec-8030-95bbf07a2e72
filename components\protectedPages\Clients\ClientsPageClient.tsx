"use client";

import React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ClientsDataTable } from "./clients-data-table";
import { getClientColumns, Client } from "./columns";
import AddClient from "./AddClient";
import {
  getClients,
} from "@/lib/actions/clients/get-clients";
import { deleteClients } from "@/lib/actions/clients/delete-clients";
import { updateClient } from "@/lib/actions/clients/update-client";
import { createClient } from "@/lib/actions/clients/create-client";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { RowSelectionState } from "@tanstack/react-table";
import { NewClient } from "@/lib/actions/clients/client.schema";
import DeleteConfirmationDialog from "@/components/general/DeleteConfirmationDialog";
import { Skeleton } from "@/components/ui/skeleton";

export default function ClientsPageClient() {
  const queryClient = useQueryClient();
  const [sheetOpen, setSheetOpen] = React.useState(false);
  const [editingClientId, setEditingClientId] = React.useState<string | null>(null);
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [clientsToDelete, setClientsToDelete] = React.useState<string[]>([]);

  const { data: clients = [], isLoading: isLoadingClients } = useQuery({
    queryKey: ["clients"],
    queryFn: () => getClients(),
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { mutate: mutateClient, isPending: isMutatingClient } = useMutation({
    mutationFn: async (data: { clientData: NewClient; id?: string }) => {
      if (data.id) {
        return updateClient({ ...data.clientData, id: data.id } as Client);
      } else {
        return createClient(data.clientData);
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["clients"] });
        setSheetOpen(false);
        setEditingClientId(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (ids: string[]) => deleteClients(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["clients"] });
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
      setDeleteDialogOpen(false);
      setClientsToDelete([]);
    },
    onError: (error) => {
      toast.error(error.message);
      setDeleteDialogOpen(false);
      setClientsToDelete([]);
    },
  });

  const selectedClientIds = React.useMemo(() => 
    Object.keys(rowSelection).map((key) => clients[parseInt(key, 10)]?.id).filter(Boolean),
    [rowSelection, clients]
  );

  const handleSubmit = (clientData: NewClient) => {
    mutateClient(editingClientId ? { clientData, id: editingClientId } : { clientData });
  };

  const openDeleteDialog = (ids: string[]) => {
    setClientsToDelete(ids);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    mutateDelete(clientsToDelete);
  };

  const columns = getClientColumns({
    onEdit: (client) => {
      setEditingClientId(client.id);
      setSheetOpen(true);
    },
    onDelete: (clientId) => openDeleteDialog([clientId]),
  });

  if (isLoadingClients) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Clients</h1>
        <Button
          onClick={() => {
            setEditingClientId(null);
            setSheetOpen(true);
          }}
        >
          Add Client
        </Button>
      </div>
      {selectedClientIds.length > 0 && (
        <div className="flex justify-between items-center p-2 bg-muted rounded-md">
          <span>{selectedClientIds.length} selected</span>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => openDeleteDialog(selectedClientIds)}
            disabled={isDeleting}
          >
            Delete Selected
          </Button>
        </div>
      )}
      <ClientsDataTable
        columns={columns}
        data={clients}
        loading={isDeleting || isMutatingClient}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
      <AddClient
        open={sheetOpen}
        onOpenChange={setSheetOpen}
        onSubmit={handleSubmit}
        clientId={editingClientId}
        loading={isMutatingClient}
      />
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        isPending={isDeleting}
        count={clientsToDelete.length}
      />
    </div>
  );
} 