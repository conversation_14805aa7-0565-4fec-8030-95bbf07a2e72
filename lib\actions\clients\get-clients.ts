"use server";

import { db } from "@/db/drizzle";
import { clients } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";
import { Client } from "@/components/protectedPages/Clients/columns";

export async function getClients(): Promise<Client[]> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const clientList = await db
      .select()
      .from(clients)
      .where(eq(clients.organizationId, orgId))
      .orderBy(desc(clients.createdAt));
    return clientList;
  } catch (error) {
    console.error("Failed to fetch clients:", error);
    return [];
  }
} 