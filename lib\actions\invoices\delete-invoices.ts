"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { invoices, invoiceItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, inArray, eq } from "drizzle-orm";

export async function deleteInvoices(invoiceIds: string[]) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    // Delete invoice items first (due to foreign key constraint)
    await db
      .delete(invoiceItems)
      .where(inArray(invoiceItems.invoiceId, invoiceIds));
    
    // Then delete the invoices
    await db
      .delete(invoices)
      .where(
        and(
          inArray(invoices.id, invoiceIds),
          eq(invoices.organizationId, organizationId)
        )
      );

    revalidatePath("/invoices");
    return { success: true, message: "Selected invoices deleted." };
  } catch (error) {
    console.error(error);
    return { success: false, message: "Failed to delete selected invoices." };
  }
} 