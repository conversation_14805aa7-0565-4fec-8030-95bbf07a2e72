"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc, sql } from "drizzle-orm";

/**
 * Get products with profitability analytics
 */
export async function getProductsWithAnalytics() {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const productList = await db
      .select({
        id: products.id,
        name: products.name,
        description: products.description,
        type: products.type,
        price: products.price,
        costBasis: products.costBasis,
        totalSold: products.totalSold,
        totalRevenue: products.totalRevenue,
        totalCosts: products.totalCosts,
        marginTarget: products.marginTarget,
        lastSaleDate: products.lastSaleDate,
        createdAt: products.createdAt,
        revenueAccountId: products.revenueAccountId,
        inventoryAccountId: products.inventoryAccountId,
        categoryId: products.categoryId,
        // Calculate profitability metrics
        margin: sql<number>`
          CASE 
            WHEN ${products.totalRevenue} > 0 
            THEN (${products.totalRevenue} - ${products.totalCosts}) * 100.0 / ${products.totalRevenue}
            ELSE 0 
          END
        `.as('margin'),
        profitPerUnit: sql<number>`
          CASE 
            WHEN ${products.totalSold} > 0 
            THEN (${products.totalRevenue} - ${products.totalCosts}) / ${products.totalSold}
            ELSE 0 
          END
        `.as('profitPerUnit'),
        status: sql<string>`
          CASE 
            WHEN ${products.totalRevenue} - ${products.totalCosts} > 0 THEN 'profitable'
            WHEN ${products.totalRevenue} - ${products.totalCosts} = 0 THEN 'break_even'
            ELSE 'loss'
          END
        `.as('status'),
      })
      .from(products)
      .where(eq(products.organizationId, orgId))
      .orderBy(desc(products.totalRevenue));
    
    return productList;
  } catch (error) {
    console.error("Failed to fetch products with analytics:", error);
    return [];
  }
} 