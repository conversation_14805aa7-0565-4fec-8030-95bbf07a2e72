"use client";

import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertCircle, Upload, Download, FileText, CheckCircle, XCircle } from "lucide-react";
import { parseBudgetCSV } from "@/lib/actions/budget/utils";
import { importBudgetFromCSV } from "@/lib/actions/budget/import-budget-from-csv";
import { generateBudgetCSVTemplate } from "@/lib/actions/budget/generate-budget-csv-template";
import { toast } from "sonner";

interface ImportBudgetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  budgetPeriodId: string;
  onImportCompleted: () => void;
}

interface ParsedItem {
  accountIdentifier: string;
  budgetedAmount: number;
  notes: string;
  rowNumber: number;
}

export default function ImportBudgetDialog({
  open,
  onOpenChange,
  budgetPeriodId,
  onImportCompleted,
}: ImportBudgetDialogProps) {
  const [parsedItems, setParsedItems] = useState<ParsedItem[]>([]);
  const [parseErrors, setParseErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importStep, setImportStep] = useState<"upload" | "preview" | "complete">("upload");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error("Please upload a CSV file");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      handleParseCSV(content);
    };
    reader.readAsText(file);
  };

  const handleParseCSV = async (content: string) => {
    setIsLoading(true);
    try {
      const result = parseBudgetCSV(content);
      // result is an array of ParsedItem | null
      const items = result.filter((item): item is ParsedItem => item !== null);
      setParsedItems(items);
      setParseErrors([]); // No error reporting in current parseBudgetCSV
      setImportStep("preview");
      toast.success(`Successfully parsed ${items.length} items`);
    } catch (error) {
      console.error("Error parsing CSV:", error);
      toast.error("Failed to parse CSV file");
      setImportStep("upload");
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = async () => {
    setIsImporting(true);
    try {
      const result = await importBudgetFromCSV(budgetPeriodId, parsedItems);
      
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(result.message);
        setImportStep("complete");
        onImportCompleted();
        
        setTimeout(() => {
          resetDialog();
        }, 3000);
      }
    } catch (error) {
      console.error("Error importing budget:", error);
      toast.error("Failed to import budget data");
    } finally {
      setIsImporting(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const template = await generateBudgetCSVTemplate();
      if (template) {
        const blob = new Blob([template.data ?? ""], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'budget-template.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        toast.success("Template downloaded successfully");
      } else {
        toast.error("Failed to generate template");
      }
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Failed to download template");
    }
  };

  const resetDialog = () => {
    setParsedItems([]);
    setParseErrors([]);
    setImportStep("upload");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Import Budget Data</DialogTitle>
          <DialogDescription>
            Upload a CSV file with budget information or download our template to get started.
          </DialogDescription>
        </DialogHeader>

        <div className="min-h-[400px]">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3">Processing CSV...</span>
            </div>
          ) : (
            <>
              {importStep === "upload" && (
                <div className="space-y-6">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Import Budget from CSV</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      Upload a CSV file with your budget data or download our template to get started.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="cursor-pointer hover:border-blue-300 transition-colors">
                      <CardHeader>
                        <div className="flex items-center space-x-2">
                          <Upload className="h-5 w-5 text-blue-600" />
                          <CardTitle className="text-base">Upload CSV File</CardTitle>
                        </div>
                        <CardDescription>
                          Upload your budget data in CSV format
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept=".csv"
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          Choose File
                        </Button>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:border-green-300 transition-colors">
                      <CardHeader>
                        <div className="flex items-center space-x-2">
                          <Download className="h-5 w-5 text-green-600" />
                          <CardTitle className="text-base">Download Template</CardTitle>
                        </div>
                        <CardDescription>
                          Get a sample CSV template with your accounts
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={handleDownloadTemplate}
                        >
                          Download Template
                        </Button>
                      </CardContent>
                    </Card>
                  </div>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Your CSV should have columns for account (number or name) and budgeted amount. 
                      Optional columns include notes or descriptions.
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {importStep === "preview" && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Preview Import Data</h3>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-sm">
                        {parsedItems.length} items
                      </Badge>
                      {parseErrors.length > 0 && (
                        <Badge variant="destructive" className="text-sm">
                          {parseErrors.length} errors
                        </Badge>
                      )}
                    </div>
                  </div>

                  {parseErrors.length > 0 && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="space-y-1">
                          <p className="font-semibold">Found {parseErrors.length} errors:</p>
                          <ul className="list-disc list-inside text-sm">
                            {parseErrors.slice(0, 5).map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                            {parseErrors.length > 5 && (
                              <li>... and {parseErrors.length - 5} more errors</li>
                            )}
                          </ul>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Data Preview</CardTitle>
                      <CardDescription>
                        Review the parsed data before importing
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-[300px]">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Account</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Notes</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {parsedItems.map((item, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">
                                  {item.accountIdentifier}
                                </TableCell>
                                <TableCell>{formatCurrency(item.budgetedAmount)}</TableCell>
                                <TableCell className="text-sm text-gray-600 dark:text-gray-400">
                                  {item.notes || "—"}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    </CardContent>
                  </Card>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      The system will try to match accounts by number first, then by name. 
                      Unmatched accounts will be skipped during import.
                    </AlertDescription>
                  </Alert>

                  <div className="flex justify-between space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setImportStep("upload")}
                    >
                      Back
                    </Button>
                    <div className="flex space-x-3">
                      <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={isImporting}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleImport}
                        disabled={isImporting || parsedItems.length === 0}
                      >
                        {isImporting ? "Importing..." : `Import ${parsedItems.length} Items`}
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {importStep === "complete" && (
                <div className="text-center space-y-4">
                  <CheckCircle className="h-16 w-16 mx-auto text-green-600" />
                  <h3 className="text-lg font-semibold">Import Completed</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Your budget data has been successfully imported.
                  </p>
                  <Button onClick={() => onOpenChange(false)}>
                    Close
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 