// api/webhook/polar/route.ts
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { db } from "@/db/drizzle";
import { schema } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

// TODO: Add webhook verification once Polar provides a secret
// const POLAR_WEBHOOK_SECRET = process.env.POLAR_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // TODO: Verify webhook signature
    // const headersList = await headers();
    // if (POLAR_WEBHOOK_SECRET) {
    //   const signature = headersList.get("x-polar-signature");
    //   // Add signature verification logic here
    // }

    const event = body;
    console.log("Received Polar webhook:", { type: event.type, id: event.id });

    // Handle different event types
    switch (event.type) {
      case "subscription.created":
        await handleSubscriptionUpdate(event.data, "created");
        break;
      
      case "subscription.updated":
        await handleSubscriptionUpdate(event.data, "updated");
        break;
      
      case "subscription.canceled":
        await handleSubscriptionUpdate(event.data, "canceled");
        break;

      case "invoice.payment_failed":
        await handlePaymentFailed(event.data);
        break;
      
      default:
        console.log("Unhandled event type:", event.type);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return NextResponse.json(
      { error: "Webhook handler failed" },
      { status: 500 }
    );
  }
}

/**
 * Handles subscription created, updated, and canceled events.
 * The logic is consolidated as they all update an existing organization's subscription state.
 */
async function handleSubscriptionUpdate(subscription: any, eventType: "created" | "updated" | "canceled") {
  const organizationId = subscription.customer?.external_id;
  
  if (!organizationId) {
    console.error(`❌ [handleSubscriptionUpdate] No organization ID (customer.external_id) found in subscription webhook for event: ${eventType}`);
    return;
  }

  console.log(`🔷 [handleSubscriptionUpdate] Processing ${eventType} event for organization: ${organizationId}`);

  try {
    const org = await db.query.organizations.findFirst({
      where: eq(schema.organizations.id, organizationId),
    });

    if (!org) {
      console.error(`❌ [handleSubscriptionUpdate] Organization with ID ${organizationId} not found.`);
      // In a real-world scenario, you might want to send an alert here.
      return;
    }

    const planType = extractPlanTypeFromProduct(subscription.product);
    const planStatus = eventType === "canceled" ? "canceled" : subscription.status;

    await db.update(schema.organizations)
      .set({
        subscriptionId: subscription.id,
        planType: planType,
        planStatus: planStatus,
        subscriptionCurrentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null,
        updatedAt: new Date(),
      })
      .where(eq(schema.organizations.id, organizationId));
      
    console.log(`✅ [handleSubscriptionUpdate] Successfully updated organization ${organizationId} for subscription event: ${eventType}`);

  } catch (error) {
    console.error(`❌ [handleSubscriptionUpdate] Error processing subscription event for organization ${organizationId}:`, error);
  }
}

async function handlePaymentFailed(invoice: any) {
  const organizationId = invoice.customer?.external_id;

  if (!organizationId) {
    console.error("❌ [handlePaymentFailed] No organization ID (customer.external_id) found in payment failed webhook.");
    return;
  }

  console.log(`🔷 [handlePaymentFailed] Processing payment_failed event for organization: ${organizationId}`);

  try {
    const org = await db.query.organizations.findFirst({
      where: eq(schema.organizations.id, organizationId),
    });

    if (!org) {
      console.error(`❌ [handlePaymentFailed] Organization with ID ${organizationId} not found.`);
      return;
    }

    // Update the organization's plan status to reflect the payment failure.
    await db.update(schema.organizations)
      .set({
        planStatus: "suspended",
        deactivationReason: "payment_failed",
        updatedAt: new Date(),
      })
      .where(eq(schema.organizations.id, organizationId));
      
    console.log(`✅ [handlePaymentFailed] Successfully suspended organization ${organizationId} due to failed payment.`);

    // Optional: Send an email to the user notifying them of the failed payment.

  } catch (error) {
    console.error(`❌ [handlePaymentFailed] Error processing payment_failed event for organization ${organizationId}:`, error);
  }
}

// Helper function to extract a simplified plan type from the Polar product name
function extractPlanTypeFromProduct(product: any): string {
  if (!product || !product.name) return 'unknown';
  const name = product.name.toLowerCase();
  if (name.includes('starter')) return 'starter';
  if (name.includes('professional')) return 'professional';
  if (name.includes('business')) return 'business';
  if (name.includes('unlimited')) return 'unlimited';
  return 'unknown';
}