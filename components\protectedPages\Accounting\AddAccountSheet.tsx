"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDes<PERSON> } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useQuery } from "@tanstack/react-query";
import { getAccountById } from "@/lib/actions/accounting/get-account-by-id";
import { <PERSON><PERSON>, FileText, ToggleLeft, Info, CheckCircle, Loader2 } from "lucide-react";

interface AddAccountSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (account: import("@/lib/actions/accounting/account.schema").NewAccount) => void;
  accountId?: string | null;
  organizationId: string;
}

const accountTypes = [
  { value: "asset", label: "Asset", color: "bg-blue-100 text-blue-800", description: "Resources owned by the business" },
  { value: "liability", label: "Liability", color: "bg-red-100 text-red-800", description: "Debts and obligations" },
  { value: "equity", label: "Equity", color: "bg-green-100 text-green-800", description: "Owner's interest in the business" },
  { value: "revenue", label: "Revenue", color: "bg-purple-100 text-purple-800", description: "Income from business operations" },
  { value: "expense", label: "Expense", color: "bg-orange-100 text-orange-800", description: "Costs of doing business" },
];

const allowedTypes = ["asset", "liability", "equity", "revenue", "expense"] as const;
type AccountType = typeof allowedTypes[number];

export default function AddAccountSheet({
  open,
  onOpenChange,
  onSubmit,
  accountId,
  organizationId,
}: AddAccountSheetProps) {
  const isEdit = !!accountId;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  
  const { data: account, isLoading: isLoadingAccount } = useQuery({
    queryKey: ["account", accountId],
    queryFn: () => accountId ? getAccountById(accountId) : null,
    enabled: !!accountId && open,
  });

  const [formData, setFormData] = React.useState<{
    accountNumber: string;
    name: string;
    type: string;
    description: string;
    normalBalance: "debit" | "credit";
    isActive: boolean;
    isHeader: boolean;
    parentId?: string | null;
  }>({
    accountNumber: "",
    name: "",
    type: "asset",
    description: "",
    normalBalance: "debit",
    isActive: true,
    isHeader: false,
    parentId: null,
  });

  React.useEffect(() => {
    if (open) {
      if (isEdit && account) {
        setFormData({
          accountNumber: account.accountNumber || "",
          name: account.name,
          type: account.type,
          description: account.description || "",
          normalBalance: account.normalBalance,
          isActive: !!account.isActive,
          isHeader: !!account.isHeader,
          parentId: account.parentId ?? null,
        });
      } else if (!isEdit) {
        setFormData({
          accountNumber: "",
          name: "",
          type: "asset",
          description: "",
          normalBalance: "debit",
          isActive: true,
          isHeader: false,
          parentId: null,
        });
      }
    }
  }, [open, isEdit, account]);

  const handleTypeChange = (type: string) => {
    setFormData({
      ...formData,
      type,
      normalBalance: type === "asset" || type === "expense" ? "debit" : "credit",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await onSubmit({
        accountNumber: formData.accountNumber || "",
        name: formData.name,
        type: formData.type as AccountType,
        description: formData.description || undefined,
        normalBalance: formData.normalBalance,
        isActive: formData.isActive,
        isHeader: formData.isHeader,
        parentId: formData.parentId || undefined,
        level: 0,
        displayOrder: 0,
        classification: null,
        financialStatementSection: null,
        accountGroup: null,
        cashFlowCategory: null,
        subcategory: null,
        gaapCategory: null,
        ifrsCategory: null,
        notes: null,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSheetClose = (open: boolean) => {
    if (!isSubmitting) {
      onOpenChange(open);
    }
  };

  const selectedAccountType = accountTypes.find(type => type.value === formData.type);

  if (isEdit && isLoadingAccount) {
    return (
      <Sheet open={open} onOpenChange={handleSheetClose}>
        <SheetContent 
          side="right" 
          className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
          aria-describedby="loading-account-description"
        >
          <SheetHeader className="px-6 py-4 sm:px-8 sm:py-5 border-b">
            <SheetTitle className="text-xl sm:text-2xl">Loading Account...</SheetTitle>
            <SheetDescription id="loading-account-description">
              Please wait while we load the account details
            </SheetDescription>
          </SheetHeader>
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={open} onOpenChange={handleSheetClose}>
      <SheetContent 
        side="right" 
        className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
        aria-describedby="account-form-description"
      >
        <SheetHeader className="px-6 py-4 sm:px-8 sm:py-5 border-b">
          <SheetTitle className="text-xl sm:text-2xl flex items-center gap-2">
            <User className="h-5 w-5 sm:h-6 sm:w-6" />
            {isEdit ? "Edit Account" : "Add New Account"}
          </SheetTitle>
          <SheetDescription id="account-form-description">
            {isEdit ? "Modify the account details below" : "Fill out the form to create a new account"}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto px-6 pb-6 sm:px-8 sm:pb-8">
          <form onSubmit={handleSubmit} className="space-y-6 mt-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                  <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                  Account Type & Classification
                </CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Select the account type and configure its basic properties.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type" className="text-sm font-medium">
                      Account Type
                    </Label>
                    <Select value={formData.type} onValueChange={handleTypeChange}>
                      <SelectTrigger id="type">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {accountTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className={`text-xs ${type.color}`}>
                                {type.label}
                              </Badge>
                              <span className="text-xs text-muted-foreground hidden sm:inline">
                                {type.description}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedAccountType && (
                      <p className="text-xs text-muted-foreground">
                        {selectedAccountType.description}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="normalBalance" className="text-sm font-medium">
                      Normal Balance
                    </Label>
                    <Select
                      value={formData.normalBalance}
                      onValueChange={(value: "debit" | "credit") =>
                        setFormData({ ...formData, normalBalance: value })
                      }
                    >
                      <SelectTrigger id="normalBalance">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="debit">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">Debit</Badge>
                            <span className="text-xs text-muted-foreground hidden sm:inline">
                              Assets & Expenses
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="credit">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">Credit</Badge>
                            <span className="text-xs text-muted-foreground hidden sm:inline">
                              Liabilities, Equity & Revenue
                            </span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        {formData.normalBalance === "debit" 
                          ? "Increases with debits, decreases with credits"
                          : "Increases with credits, decreases with debits"
                        }
                      </AlertDescription>
                    </Alert>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base sm:text-lg">Account Details</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Configure the account name, number, and description.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="accountNumber" className="text-sm font-medium">
                      Account Number
                    </Label>
                    <Input
                      id="accountNumber"
                      value={formData.accountNumber}
                      onChange={(e) =>
                        setFormData({ ...formData, accountNumber: e.target.value })
                      }
                      placeholder="e.g., 1000, 2100, 4000"
                      className="font-mono"
                    />
                    <p className="text-xs text-muted-foreground">
                      Leave empty to auto-generate based on account type
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Account Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      required
                      placeholder="e.g., Cash and Cash Equivalents"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    rows={3}
                    placeholder="Optional description of what this account tracks..."
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                  <ToggleLeft className="h-4 w-4 sm:h-5 sm:w-5" />
                  Account Settings
                </CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Configure account behavior and visibility.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isActive"
                        checked={formData.isActive}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, isActive: checked })
                        }
                      />
                      <Label htmlFor="isActive" className="text-sm font-medium">
                        Active Account
                      </Label>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Active accounts can be used in transactions
                    </p>
                  </div>

                  <Separator orientation="vertical" className="hidden sm:block h-12" />

                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isHeader"
                        checked={formData.isHeader}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, isHeader: checked })
                        }
                      />
                      <Label htmlFor="isHeader" className="text-sm font-medium">
                        Header Account
                      </Label>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Header accounts organize other accounts
                    </p>
                  </div>
                </div>

                {formData.isHeader && (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      Header accounts are used for organization and typically don't have transactions posted directly to them.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t">
              <Button 
                type="button"
                variant="outline" 
                onClick={() => handleSheetClose(false)}
                disabled={isSubmitting}
                className="w-full sm:w-auto order-2 sm:order-1"
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={isSubmitting || !formData.name.trim()}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {isEdit ? "Saving..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {isEdit ? "Save Changes" : "Create Account"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </SheetContent>
    </Sheet>
  );
}
