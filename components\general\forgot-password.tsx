'use client'

import React, { useState } from 'react'
import { Logo } from '@/components/general/logo'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import Link from 'next/link'
import { authClient } from '@/lib/auth-client'
import { z } from 'zod'
import { AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react'


// Define the validation schema
const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [formData, setFormData] = useState<ForgotPasswordFormData>({
    email: '',
  })
  const [fieldErrors, setFieldErrors] = useState<Partial<Record<keyof ForgotPasswordFormData, string>>>({})

  const validateField = (field: keyof ForgotPasswordFormData, value: string) => {
    try {
      forgotPasswordSchema.shape[field].parse(value)
      setFieldErrors(prev => ({ ...prev, [field]: undefined }))
    } catch (err) {
      if (err instanceof z.ZodError) {
        setFieldErrors(prev => ({ ...prev, [field]: err.errors[0].message }))
      }
    }
  }

  const handleInputChange = (field: keyof ForgotPasswordFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear any existing error when user starts typing
    if (error) setError(null)
    if (success) setSuccess(null)
    validateField(field, value)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Validate the entire form
      const validatedData = forgotPasswordSchema.parse(formData)
      
      const { error } = await authClient.forgetPassword({
        email: "",
        redirectTo: "/reset-password",
      })
      
      setSuccess('Password reset link sent! Please check your email for instructions.')
    } catch (err) {
      if (err instanceof z.ZodError) {
        // Handle validation errors
        const errors: Partial<Record<keyof ForgotPasswordFormData, string>> = {}
        err.errors.forEach(error => {
          const field = error.path[0] as keyof ForgotPasswordFormData
          errors[field] = error.message
        })
        setFieldErrors(errors)
        setError('Please fix the errors above and try again.')
      } else {
        // Handle authentication errors
        const errorMessage = getAuthErrorMessage(err)
        setError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to get user-friendly error messages
  const getAuthErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error
    
    // Handle different error types
    if (error?.message) {
      const message = error.message.toLowerCase()
      
      if (message.includes('user not found') || message.includes('email not found')) {
        return 'No account found with this email address. Please check your email or create a new account.'
      }
      if (message.includes('email not verified')) {
        return 'Please verify your email address first. Check your inbox for a verification link.'
      }
      if (message.includes('rate limit') || message.includes('too many requests')) {
        return 'Too many reset attempts. Please wait a few minutes before trying again.'
      }
      if (message.includes('network') || message.includes('connection')) {
        return 'Network error. Please check your internet connection and try again.'
      }
      if (message.includes('timeout')) {
        return 'Request timed out. Please try again.'
      }
    }
    
    return 'An unexpected error occurred. Please try again.'
  }

  return (
    <section className="flex min-h-screen bg-zinc-50 px-4 py-16 md:py-32 dark:bg-transparent">
      <div className="bg-card m-auto h-fit w-full max-w-sm rounded-[calc(var(--radius)+.125rem)] border p-0.5 shadow-md dark:[--color-muted:var(--color-zinc-900)]">
        <div className="p-8 pb-6">
          <div>
            <Link href="/" aria-label="go home">
              <Logo />
            </Link>
            <h1 className="mb-1 mt-4 text-xl font-semibold">Forgot Password</h1>
            <p className="text-sm text-muted-foreground">
              Enter your email address and we'll send you a link to reset your password.
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mt-6 flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-400">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mt-6 flex items-center gap-2 rounded-md border border-green-200 bg-green-50 p-3 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
              <CheckCircle className="h-4 w-4 flex-shrink-0" />
              <span>{success}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="mt-6 space-y-6">
            <div className="space-y-2">
              <Label htmlFor="email" className="block text-sm">
                Email Address
              </Label>
              <Input 
                type="email" 
                id="email" 
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={isLoading}
                className={fieldErrors.email ? 'border-red-500 focus:border-red-500' : ''}
                placeholder="Enter your email address"
              />
              {fieldErrors.email && (
                <p className="flex items-center gap-1 text-sm text-red-500">
                  <AlertCircle className="h-3 w-3" />
                  {fieldErrors.email}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Sending Reset Link...' : 'Send Reset Link'}
            </Button>
          </form>

          {/* Additional Help Section */}
          {success && (
            <div className="mt-6 rounded-md border border-blue-200 bg-blue-50 p-4 text-sm text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-400">
              <h3 className="font-medium mb-2">What happens next?</h3>
              <ul className="space-y-1 text-xs">
                <li>• Check your email inbox (and spam folder)</li>
                <li>• Click the reset link in the email</li>
                <li>• Create a new password</li>
                <li>• Sign in with your new password</li>
              </ul>
            </div>
          )}
        </div>

        <div className="bg-muted rounded-(--radius) border p-3">
          <div className="flex items-center justify-between">
            <Button asChild variant="link" size="sm" className="px-0">
              <Link href="/signin" className="flex items-center gap-1">
                <ArrowLeft className="h-3 w-3" />
                Back to Sign In
              </Link>
            </Button>
            <p className="text-accent-foreground text-center text-sm">
              Don't have an account ?
              <Button asChild variant="link" className="px-2">
                <Link href="/register">Create account</Link>
              </Button>
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
