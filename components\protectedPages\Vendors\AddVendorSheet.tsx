"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { NewVendor, vendorFormSchema } from "@/lib/actions/vendors/vendor.schema";
import { useQuery } from "@tanstack/react-query";
import { getVendorById } from "@/lib/actions/vendors/get-vendor-by-id";
import { PhoneNumberInput } from "@/components/general/PhoneNumberInput";
import { CountrySelector } from "@/components/general/CountrySelector";

interface AddVendorSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (vendorData: NewVendor) => void;
  isPending: boolean;
  vendorId?: string | null;
}

export default function AddVendorSheet({
  open,
  onOpenChange,
  onSubmit,
  isPending,
  vendorId,
}: AddVendorSheetProps) {
  const isEdit = !!vendorId;
  const { data: vendor, isLoading: isLoadingVendor } = useQuery({
    queryKey: ["vendor", vendorId],
    queryFn: () => vendorId ? getVendorById(vendorId) : null,
    enabled: !!vendorId && open,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<NewVendor>({
    resolver: zodResolver(vendorFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      category: "",
      address: {
        street: "",
        city: "",
        zip: "",
        country: "",
      },
    },
  });

  useEffect(() => {
    if (vendor) {
      reset({
        name: vendor.name,
        email: vendor.email ?? "",
        phone: vendor.phone ?? "",
        category: vendor.category ?? "",
        address: {
          street: (vendor.address as any)?.street ?? "",
          city: (vendor.address as any)?.city ?? "",
          zip: (vendor.address as any)?.zip ?? "",
          country: (vendor.address as any)?.country ?? "",
        },
      });
    } else {
      reset({
        name: "",
        email: "",
        phone: "",
        category: "",
        address: {
          street: "",
          city: "",
          zip: "",
          country: "",
        },
      });
    }
  }, [vendor, reset]);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="!w-full sm:!w-[400px] md:!w-[500px] lg:!w-[600px] xl:!w-[700px] !max-w-[50vw] !p-0 !gap-0 flex flex-col h-screen overflow-hidden">
        {/* Fixed Header */}
        <SheetHeader className="border-b bg-background flex-shrink-0 !p-0 !gap-0">
          <div className="px-4 sm:px-6 py-4">
            <SheetTitle className="text-lg sm:text-xl font-semibold">{isEdit ? "Edit Vendor" : "Add New Vendor"}</SheetTitle>
            <SheetDescription className="text-sm text-muted-foreground mt-1">
              {isEdit
                ? "Update the details for this vendor."
                : "Add a new vendor to your records. Fill out the form below."}
            </SheetDescription>
          </div>
        </SheetHeader>

        {isLoadingVendor ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-muted-foreground">Loading vendor details...</div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col flex-1 min-h-0">
            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-y-auto min-h-0">
              <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 pb-8">
                {/* Basic Information Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3 pb-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium">Basic Information</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-foreground">Vendor Name *</Label>
                      <Input 
                        {...register("name")} 
                        className="h-10 mt-1"
                        placeholder="Enter vendor name"
                      />
                      {errors.name && (
                        <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>
                      )}
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-foreground">Category</Label>
                      <Input 
                        {...register("category")} 
                        className="h-10 mt-1"
                        placeholder="e.g., Supplier, Contractor"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-foreground">Email Address</Label>
                      <Input 
                        type="email" 
                        {...register("email")} 
                        className="h-10 mt-1"
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
                      )}
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-foreground">Phone Number</Label>
                      <PhoneNumberInput 
                        value={register("phone").value ?? ""} 
                        onChange={(value) => {
                          setValue("phone", value, { shouldValidate: true });
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Address Information Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3 pb-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium">Address Information</h3>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-foreground">Street Address</Label>
                      <Input 
                        {...register("address.street")} 
                        className="h-10 mt-1"
                        placeholder="Enter street address"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div>
                        <Label className="text-sm font-medium text-foreground">City</Label>
                        <Input 
                          {...register("address.city")} 
                          className="h-10 mt-1"
                          placeholder="Enter city"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-foreground">ZIP / Postal Code</Label>
                        <Input 
                          {...register("address.zip")} 
                          className="h-10 mt-1"
                          placeholder="Enter ZIP code"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-foreground">Country</Label>
                        <CountrySelector 
                          value={register("address.country").value ?? ""} 
                          onChange={(value) => {
                            setValue("address.country", value, { shouldValidate: true });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Fixed Footer */}
            <div className="border-t bg-background flex-shrink-0 p-4 sm:p-6">
              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  className="flex-1"
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button type="submit" className="flex-1" disabled={isPending}>
                  {isPending ? "Saving..." : (isEdit ? "Update Vendor" : "Add Vendor")}
                </Button>
              </div>
            </div>
          </form>
        )}
      </SheetContent>
    </Sheet>
  );
}
