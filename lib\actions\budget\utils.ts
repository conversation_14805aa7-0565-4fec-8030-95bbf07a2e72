export function parseBudgetCSV(csvContent: string) {
  // This is a placeholder for a robust CSV parsing logic
  // In a real app, use a library like Papaparse
  const lines = csvContent.split("\n").slice(1); // Skip header
  return lines
    .map((line) => {
      const [accountIdentifier, budgetedAmount, notes] = line.split(",");
      if (accountIdentifier && budgetedAmount) {
        return {
          accountIdentifier: accountIdentifier.trim(),
          budgetedAmount: parseFloat(budgetedAmount.trim()),
          notes: notes ? notes.trim() : "",
        };
      }
      return null;
    })
    .filter(Boolean);
} 