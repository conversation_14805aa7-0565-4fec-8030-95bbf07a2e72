"use client";

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const faqData = [
    {
      question: "How do I get started?",
      answer: "Simply sign up for an account and follow the onboarding process. Our intuitive platform will guide you through each step."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, PayPal, and bank transfers. You can manage your payment methods in your account settings."
    },
    {
      question: "Is there a free trial available?",
      answer:"Yes, we offer a 7-day free trial but with a credit card required. You can explore all features before you get billed."
    },
    {
        question: "Do you offer phone support?",
        answer: "We do not offer phone support at this time. However, you can contact us via email or live chat for any questions or concerns you may have."
      },
    {
        question: "Can I upgrade my plan?",
        answer:
        <>
            <p className="text-muted-foreground my-4">Yes, you can upgrade your plan at any time by logging into your account and selecting the plan you want to upgrade to.</p>
                <ul className="list-outside list-disc space-y-2 pl-4">
                    <li className="text-muted-foreground">You will be charged the difference in price between your current plan and the plan you are upgrading to.</li>
                    <li className="text-muted-foreground">Your new plan will take effect immediately and you will be billed at the new rate on your next billing cycle.</li>
                </ul>
        </>
    },
    {
      question: "How do I cancel my subscription?",
      answer: "You can cancel your subscription anytime from your account settings. If you cancel, you'll still have access until the end of your billing period."
    },
    {
      question: "What is the refund policy?",
    answer: (
      <>
        <div className="pb-6">
            <p className="text-muted-foreground mt-4">We offer a 30-day money back guarantee. If you are not satisfied with our product, you can request a refund within 30 days of your purchase.</p>
                <ol className="list-outside list-decimal space-y-2 pl-4">
                    <li className="text-muted-foreground mt-4">To request a refund, please contact our support team with your order number and reason for the refund.</li>
                    <li className="text-muted-foreground mt-4">Refunds will be processed within 3-5 business days.</li>
                    <li className="text-muted-foreground mt-4">Please note that refunds are only available for new customers and are limited to one per customer.</li>
                </ol>
        </div>
      </>
    )
    }
  ];


export default function FAQ() {
    return (
        <section className="scroll-py-16 py-16 md:scroll-py-32 md:py-32" id="faq">
            <div className="mx-auto max-w-5xl px-6">
                <div className="grid gap-y-12 px-2 lg:[grid-template-columns:1fr_auto]">
                    <div className="text-center lg:text-left">
                        <h2 className="mb-4 text-3xl font-semibold md:text-4xl">
                            Frequently <br className="hidden lg:block" /> Asked <br className="hidden lg:block" />
                            Questions
                        </h2>
                    </div>
                    <div className="divide-y divide-dashed sm:mx-auto sm:max-w-lg lg:mx-0">
                        <Accordion type="single" collapsible className="w-full">
                            {faqData.map((faq, index) => (
                                <AccordionItem key={index} value={`item-${index}`}>
                                    <AccordionTrigger className="text-left text-lg font-medium">
                                        {faq.question}
                                    </AccordionTrigger>
                                    <AccordionContent className="text-muted-foreground">
                                        {faq.answer}
                                    </AccordionContent>
                                </AccordionItem>
                            ))}
                        </Accordion>
                    </div>
                </div>
            </div>       
        </section>
    )
}
