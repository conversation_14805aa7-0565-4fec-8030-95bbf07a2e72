import React from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Teammate {
  email: string;
  role: "admin" | "editor" | "viewer";
}

interface StepInviteTeamProps {
  teammates: Teammate[];
  onChange: (teammates: Teammate[]) => void;
  onNext: () => void;
  onBack: () => void;
}

export function StepInviteTeam({ teammates, onChange, onNext, onBack }: StepInviteTeamProps) {
  const handleAdd = () => {
    onChange([...teammates, { email: "", role: "editor" }]);
  };
  const handleRemove = (idx: number) => {
    onChange(teammates.filter((_, i) => i !== idx));
  };
  const handleChange = (idx: number, field: keyof Teammate, value: string) => {
    const updated = teammates.map((tm, i) =>
      i === idx ? { ...tm, [field]: value } : tm
    );
    onChange(updated);
  };
  return (
    <form
      onSubmit={e => {
        e.preventDefault();
        onNext();
      }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-lg font-medium mb-4">Invite Teammates (optional)</h2>
        <div className="space-y-4">
          {teammates.map((tm, idx) => (
            <div key={idx} className="flex items-center gap-2">
              <Input
                type="email"
                value={tm.email}
                onChange={e => handleChange(idx, "email", e.target.value)}
                placeholder="Teammate's email"
                className="flex-1"
              />
              <Select value={tm.role} onValueChange={value => handleChange(idx, "role", value)}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="editor">Editor</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
              <Button type="button" variant="ghost" size="sm" onClick={() => handleRemove(idx)}>Remove</Button>
            </div>
          ))}
        </div>
        <Button
          type="button"
          onClick={handleAdd}
          variant="outline"
          className="mt-4"
        >
          + Add Teammate
        </Button>
      </div>
      <div className="flex gap-2 pt-6">
        <Button type="button" variant="outline" onClick={onBack}>Back</Button>
        <Button type="submit">Next</Button>
      </div>
    </form>
  );
} 