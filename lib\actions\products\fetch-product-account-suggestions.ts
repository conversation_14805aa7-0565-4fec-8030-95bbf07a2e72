"use server";

import { getProductSetupSuggestions, getOrganizationBusinessType, AccountSuggestionEngine, ProductType } from "@/lib/actions/products/get-account-suggestions";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";

const allowedProductTypes: ProductType[] = [
  "service",
  "digital_service",
  "physical_product",
  "digital_product",
  "subscription",
  "bundle"
];

export async function fetchProductAccountSuggestions(organizationId: string, productType: string) {
  if (!allowedProductTypes.includes(productType as ProductType)) {
    throw new Error(`Invalid productType: ${productType}`);
  }
  const typedProductType = productType as ProductType;
  // Get business type
  const businessType = await getOrganizationBusinessType(organizationId);
  // Get product setup suggestions
  const suggestions = await getProductSetupSuggestions(organizationId, typedProductType, businessType);
  // Get all existing accounts
  const existingAccounts = await getAccounts();
  // Get missing account suggestions
  const engine = new AccountSuggestionEngine(organizationId);
  const missingAccounts = await engine.getMissingAccountSuggestions(businessType);

  return {
    suggestions,
    existingAccounts,
    missingAccounts,
  };
} 