import React from "react";

// Slot utility for asChild pattern
function Slot({ children, ...props }: { children: React.ReactNode } & React.HTMLAttributes<HTMLElement>) {
  return React.isValidElement(children)
    ? React.cloneElement(children, props)
    : <>{children}</>;
}

// SidebarProvider should be a server component that just renders children
export function SidebarProvider({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

export function Sidebar({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`bg-sidebar text-sidebar-foreground flex h-full flex-col ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarHeader({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`flex flex-col gap-2 p-2 ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarContent({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`flex min-h-0 flex-1 flex-col gap-2 overflow-auto ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarFooter({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`flex flex-col gap-2 p-2 ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarInset({ children, className = "", ...props }: React.ComponentProps<"main">) {
  return (
    <main className={`bg-background relative flex w-full flex-1 flex-col ${className}`} {...props}>
      {children}
    </main>
  );
}

export function SidebarMenu({ children, className = "", ...props }: React.ComponentProps<"ul">) {
  return (
    <ul className={`flex flex-col gap-1 ${className}`} {...props}>
      {children}
    </ul>
  );
}

export function SidebarMenuItem({ children, className = "", ...props }: React.ComponentProps<"li">) {
  return (
    <li className={`flex items-center ${className}`} {...props}>
      {children}
    </li>
  );
}

export function SidebarMenuButton({ children, className = "", asChild, isActive, ...props }: React.ComponentProps<"button"> & { asChild?: boolean; isActive?: boolean }) {
  const activeClass = isActive ? "bg-muted font-semibold" : "";
  if (asChild) {
    return <Slot className={`w-full text-left p-2 rounded-md ${activeClass} ${className}`} {...props}>{children}</Slot>;
  }
  return (
    <button className={`w-full text-left p-2 rounded-md ${activeClass} ${className}`} {...props}>
      {children}
    </button>
  );
}

export function SidebarSeparator({ className = "", ...props }: React.ComponentProps<"hr">) {
  return <hr className={`border-t border-sidebar-border my-2 ${className}`} {...props} />;
}

export function SidebarGroup({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`flex flex-col gap-1 ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarGroupLabel({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`font-semibold text-xs uppercase tracking-wider px-2 py-1 ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarGroupAction({ children, className = "", ...props }: React.ComponentProps<"button">) {
  return (
    <button className={`text-primary px-2 py-1 rounded ${className}`} {...props}>
      {children}
    </button>
  );
}

export function SidebarGroupContent({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`flex flex-col gap-1 ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarMenuAction({ children, className = "", ...props }: React.ComponentProps<"button">) {
  return (
    <button className={`text-primary px-2 py-1 rounded ${className}`} {...props}>
      {children}
    </button>
  );
}

export function SidebarMenuBadge({ children, className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <div className={`inline-block bg-primary text-primary-foreground rounded px-2 py-0.5 text-xs ${className}`} {...props}>
      {children}
    </div>
  );
}

export function SidebarMenuSkeleton({ className = "", ...props }: React.ComponentProps<"div">) {
  return <div className={`bg-muted h-6 w-full rounded ${className}`} {...props} />;
}

export function SidebarMenuSub({ children, className = "", ...props }: React.ComponentProps<"ul">) {
  return (
    <ul className={`pl-4 flex flex-col gap-1 ${className}`} {...props}>
      {children}
    </ul>
  );
}

export function SidebarMenuSubItem({ children, className = "", ...props }: React.ComponentProps<"li">) {
  return (
    <li className={`flex items-center ${className}`} {...props}>
      {children}
    </li>
  );
}

export function SidebarMenuSubButton({ children, className = "", ...props }: React.ComponentProps<"a">) {
  return (
    <a className={`block w-full text-left p-2 rounded-md ${className}`} {...props}>
      {children}
    </a>
  );
}
