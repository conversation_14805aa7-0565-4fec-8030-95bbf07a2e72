"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { clients } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, inArray, eq } from "drizzle-orm";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

/**
 * Deletes one or more clients from the database.
 * @param ids - An array of client IDs to delete.
 */
export async function deleteClients(ids: string[]): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    if (ids.length === 0) {
      return { success: false, message: "No client IDs provided." };
    }

    await db
      .delete(clients)
      .where(
        and(inArray(clients.id, ids), eq(clients.organizationId, orgId))
      );

    revalidatePath("/clients");
    return { success: true, message: "Clients deleted successfully." };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to delete clients.",
    };
  }
} 