"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function setDefaultBudgetPeriod(budgetPeriodId: string) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    // Start a transaction
    await db.transaction(async (tx) => {
      // Unset any existing default
      await tx
        .update(budgetPeriods)
        .set({ isDefault: false })
        .where(
          and(
            eq(budgetPeriods.organizationId, organizationId),
            eq(budgetPeriods.isDefault, true)
          )
        );

      // Set the new default
      const [updatedPeriod] = await tx
        .update(budgetPeriods)
        .set({ isDefault: true })
        .where(
          and(
            eq(budgetPeriods.id, budgetPeriodId),
            eq(budgetPeriods.organizationId, organizationId)
          )
        )
        .returning();

      if (!updatedPeriod) {
        throw new Error("Budget period not found or you do not have permission.");
      }

      return updatedPeriod;
    });

    revalidatePath("/accounting/budget");
    return { success: true };
  } catch (error) {
    console.error("Error setting default budget period:", error);
    return { error: "Failed to set default budget period" };
  }
} 