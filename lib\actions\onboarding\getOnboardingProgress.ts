"use server";

import { db } from "@/db/drizzle";
import { eq } from "drizzle-orm";
import { onboarding } from "@/db/schema/schema";
import { OnboardingProgress } from "@/lib/onboarding-constants";
import { mapDbRowToOnboardingProgress } from "@/lib/actions/onboarding/mapDbRowToOnboardingProgress";

export async function getOnboardingProgress(userId: string): Promise<OnboardingProgress | null> {
  try {
    const result = await db
      .select()
      .from(onboarding)
      .where(eq(onboarding.userId, userId))
      .limit(1);

    if (!result || result.length === 0) {
      return null;
    }

    return mapDbRowToOnboardingProgress(result[0]);
  } catch (error) {
    console.error('Error fetching onboarding progress:', error);
    return null;
  }
} 