[{"accountNumber": "1000", "name": "Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "statement_of_financial_position", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1000, "subcategory": "Main Category", "gaapCategory": "Assets", "ifrsCategory": "Assets", "description": "Main asset category header", "isActive": true}, {"accountNumber": "1100", "name": "Current Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "current_assets", "cashFlowCategory": "n_a", "displayOrder": 1100, "subcategory": "Current Assets", "gaapCategory": "Current Assets", "ifrsCategory": "Current Assets", "description": "Assets expected to be converted to cash within one year", "isActive": true}, {"accountNumber": "1110", "name": "Cash and Cash Equivalents", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "n_a", "displayOrder": 1110, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Cash on hand and in bank accounts", "isActive": true}, {"accountNumber": "1112", "name": "Cash", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1112, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Primary business cash account", "isActive": true}, {"accountNumber": "1114", "name": "Petty Cash", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1114, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Petty cash for small expenses", "isActive": true}, {"accountNumber": "1120", "name": "Accounts Receivable", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1120, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Amounts due from donors, grants, or other receivables", "isActive": true}, {"accountNumber": "1122", "name": "Grants Receivable", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1120", "level": 3, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1122, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Amounts due from grantors", "isActive": true}, {"accountNumber": "1124", "name": "Pledges Receivable", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1120", "level": 3, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1124, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Pledges from donors not yet received", "isActive": true}, {"accountNumber": "1130", "name": "Prepaid Expenses", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "prepaid_expenses", "cashFlowCategory": "operating", "displayOrder": 1130, "subcategory": "Prepaid Expenses", "gaapCategory": "Prepaid Expenses and Other Current Assets", "ifrsCategory": "Other Current Assets", "description": "Expenses paid in advance", "isActive": true}, {"accountNumber": "1200", "name": "Inventory", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1200, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Inventory for program or fundraising use", "isActive": true}, {"accountNumber": "1500", "name": "Property and Equipment", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1500, "subcategory": "Property and Equipment", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Long-term tangible assets", "isActive": true}, {"accountNumber": "1510", "name": "Buildings & Improvements", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1510, "subcategory": "Property and Equipment", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Buildings, renovations, and improvements", "isActive": true}, {"accountNumber": "1512", "name": "Furniture, Fixtures & Equipment", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1512, "subcategory": "Property and Equipment", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Office and program equipment", "isActive": true}, {"accountNumber": "1550", "name": "Accumulated Depreciation - Buildings", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accumulated_depreciation", "cashFlowCategory": "investing", "displayOrder": 1550, "subcategory": "Property and Equipment", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Contra-asset for building depreciation", "isActive": true}, {"accountNumber": "1552", "name": "Accumulated Depreciation - Equipment", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accumulated_depreciation", "cashFlowCategory": "investing", "displayOrder": 1552, "subcategory": "Property and Equipment", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Contra-asset for equipment depreciation", "isActive": true}, {"accountNumber": "2000", "name": "Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "statement_of_financial_position", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2000, "subcategory": "Main Category", "gaapCategory": "Liabilities", "ifrsCategory": "Liabilities", "description": "Main liability category header", "isActive": true}, {"accountNumber": "2100", "name": "Current Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "current_liabilities", "cashFlowCategory": "n_a", "displayOrder": 2100, "subcategory": "Current Liabilities", "gaapCategory": "Current Liabilities", "ifrsCategory": "Current Liabilities", "description": "Liabilities due within one year", "isActive": true}, {"accountNumber": "2110", "name": "Accounts Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accounts_payable", "cashFlowCategory": "operating", "displayOrder": 2110, "subcategory": "Accounts Payable", "gaapCategory": "Accounts Payable", "ifrsCategory": "Trade and Other Payables", "description": "Amounts owed to vendors and suppliers", "isActive": true}, {"accountNumber": "2120", "name": "Accrued Expenses", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "accrued_liabilities", "cashFlowCategory": "operating", "displayOrder": 2120, "subcategory": "Accrued Expenses", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Accrued Liabilities", "description": "Expenses incurred but not yet paid", "isActive": true}, {"accountNumber": "2130", "name": "Deferred Revenue", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "deferred_revenue", "cashFlowCategory": "operating", "displayOrder": 2130, "subcategory": "Deferred Revenue", "gaapCategory": "Unearned Revenue", "ifrsCategory": "Contract Liabilities", "description": "Funds received but not yet earned", "isActive": true}, {"accountNumber": "2150", "name": "Payroll Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2150, "subcategory": "Payroll", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Accrued Liabilities", "description": "Accrued but unpaid payroll and related taxes", "isActive": true}, {"accountNumber": "2155", "name": "Payroll Taxes Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2150", "level": 3, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2155, "subcategory": "Payroll", "gaapCategory": "Payroll Taxes Payable", "ifrsCategory": "Payroll Taxes Payable", "description": "Payroll taxes withheld but not yet remitted", "isActive": true}, {"accountNumber": "2160", "name": "Employee Benefits Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2150", "level": 3, "classification": "current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2160, "subcategory": "Payroll", "gaapCategory": "Benefits Payable", "ifrsCategory": "Benefits Payable", "description": "Employee benefits withheld but not yet remitted", "isActive": true}, {"accountNumber": "2500", "name": "Long-Term Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "long_term_liabilities", "cashFlowCategory": "n_a", "displayOrder": 2500, "subcategory": "Long-Term Liabilities", "gaapCategory": "<PERSON>-<PERSON><PERSON>", "ifrsCategory": "Non-current Liabilities", "description": "Liabilities due after one year", "isActive": true}, {"accountNumber": "2510", "name": "Notes Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2500", "level": 2, "classification": "non_current", "financialStatementSection": "statement_of_financial_position", "accountGroup": "long_term_debt", "cashFlowCategory": "financing", "displayOrder": 2510, "subcategory": "Loans", "gaapCategory": "Notes Payable", "ifrsCategory": "Borrowings", "description": "Long-term loans owed", "isActive": true}, {"accountNumber": "3000", "name": "Net Assets", "type": "equity", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "statement_of_financial_position", "accountGroup": "net_assets", "cashFlowCategory": "n_a", "displayOrder": 3000, "subcategory": "Net Assets", "gaapCategory": "Net Assets", "ifrsCategory": "Equity", "description": "Net assets or fund balances", "isActive": true}, {"accountNumber": "3100", "name": "Unrestricted Net Assets", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "n_a", "financialStatementSection": "statement_of_financial_position", "accountGroup": "unrestricted_net_assets", "cashFlowCategory": "n_a", "displayOrder": 3100, "subcategory": "Unrestricted Net Assets", "gaapCategory": "Net Assets", "ifrsCategory": "Equity", "description": "Net assets without donor restrictions", "isActive": true}, {"accountNumber": "3200", "name": "Temporarily Restricted Net Assets", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "n_a", "financialStatementSection": "statement_of_financial_position", "accountGroup": "temporarily_restricted_net_assets", "cashFlowCategory": "n_a", "displayOrder": 3200, "subcategory": "Temporarily Restricted Net Assets", "gaapCategory": "Net Assets", "ifrsCategory": "Equity", "description": "Net assets with donor restrictions", "isActive": true}, {"accountNumber": "3300", "name": "Permanently Restricted Net Assets", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "n_a", "financialStatementSection": "statement_of_financial_position", "accountGroup": "permanently_restricted_net_assets", "cashFlowCategory": "n_a", "displayOrder": 3300, "subcategory": "Permanently Restricted Net Assets", "gaapCategory": "Net Assets", "ifrsCategory": "Equity", "description": "Net assets with permanent donor restrictions", "isActive": true}, {"accountNumber": "4000", "name": "Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "statement_of_activities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 4000, "subcategory": "Main Category", "gaapCategory": "Revenue", "ifrsCategory": "Revenue", "description": "Main revenue category header", "isActive": true}, {"accountNumber": "4100", "name": "Earned Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "earned_revenue", "financialStatementSection": "statement_of_activities", "accountGroup": "earned_revenue", "cashFlowCategory": "operating", "displayOrder": 4100, "subcategory": "Earned Revenue", "gaapCategory": "Revenue", "ifrsCategory": "Revenue", "description": "Revenue from services, contracts, or sales", "isActive": true}, {"accountNumber": "4200", "name": "Contributed Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "contributed_revenue", "financialStatementSection": "statement_of_activities", "accountGroup": "contributed_revenue", "cashFlowCategory": "operating", "displayOrder": 4200, "subcategory": "Contributed Revenue", "gaapCategory": "Revenue", "ifrsCategory": "Revenue", "description": "Donations, grants, and fundraising income", "isActive": true}, {"accountNumber": "4300", "name": "Other Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "other_revenue", "financialStatementSection": "statement_of_activities", "accountGroup": "other_revenue", "cashFlowCategory": "investing", "displayOrder": 4300, "subcategory": "Other Revenue", "gaapCategory": "Other Income", "ifrsCategory": "Other Income", "description": "Miscellaneous income", "isActive": true}, {"accountNumber": "5000", "name": "Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "statement_of_activities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 5000, "subcategory": "Main Category", "gaapCategory": "Expenses", "ifrsCategory": "Expenses", "description": "Main expense category header", "isActive": true}, {"accountNumber": "6000", "name": "Program Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 1, "classification": "program_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "program_expenses", "cashFlowCategory": "operating", "displayOrder": 6000, "subcategory": "Program Expenses", "gaapCategory": "Program Services", "ifrsCategory": "Expenses", "description": "Expenses directly related to program services", "isActive": true}, {"accountNumber": "6100", "name": "Salaries and Wages - Program", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 2, "classification": "program_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "personnel_expenses", "cashFlowCategory": "operating", "displayOrder": 6100, "subcategory": "Payroll", "gaapCategory": "Salaries and Wages Expense", "ifrsCategory": "Employee Benefits Expense", "description": "Salaries for program staff", "isActive": true}, {"accountNumber": "6200", "name": "Employee Benefits - Program", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 2, "classification": "program_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "payroll_expenses", "cashFlowCategory": "operating", "displayOrder": 6200, "subcategory": "Payroll", "gaapCategory": "Employee Benefits", "ifrsCategory": "Employee Benefits", "description": "Benefits for program staff", "isActive": true}, {"accountNumber": "6300", "name": "Program Supplies", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 2, "classification": "program_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "supplies_expenses", "cashFlowCategory": "operating", "displayOrder": 6300, "subcategory": "Supplies", "gaapCategory": "Supplies Expense", "ifrsCategory": "Operating Expenses", "description": "Supplies used in program services", "isActive": true}, {"accountNumber": "7000", "name": "Management and General Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 1, "classification": "management_general_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "management_general_expenses", "cashFlowCategory": "operating", "displayOrder": 7000, "subcategory": "Management and General Expenses", "gaapCategory": "Supporting Services", "ifrsCategory": "Expenses", "description": "Expenses related to general management and administration", "isActive": true}, {"accountNumber": "7100", "name": "Salaries and Wages - Admin", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "7000", "level": 2, "classification": "management_general_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "personnel_expenses", "cashFlowCategory": "operating", "displayOrder": 7100, "subcategory": "Payroll", "gaapCategory": "Salaries and Wages Expense", "ifrsCategory": "Employee Benefits Expense", "description": "Salaries for administrative staff", "isActive": true}, {"accountNumber": "7200", "name": "Employee Benefits - Admin", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "7000", "level": 2, "classification": "management_general_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "payroll_expenses", "cashFlowCategory": "operating", "displayOrder": 7200, "subcategory": "Payroll", "gaapCategory": "Employee Benefits", "ifrsCategory": "Employee Benefits", "description": "Benefits for administrative staff", "isActive": true}, {"accountNumber": "7300", "name": "Office Supplies", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "7000", "level": 2, "classification": "management_general_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "office_supplies", "cashFlowCategory": "operating", "displayOrder": 7300, "subcategory": "Office Supplies", "gaapCategory": "Office Supplies Expense", "ifrsCategory": "Operating Expenses", "description": "Costs of office supplies", "isActive": true}, {"accountNumber": "7400", "name": "Professional Fees", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "7000", "level": 2, "classification": "management_general_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "professional_fees", "cashFlowCategory": "operating", "displayOrder": 7400, "subcategory": "Professional Fees", "gaapCategory": "Professional Fees", "ifrsCategory": "Professional Fees", "description": "Legal, accounting, and consulting fees", "isActive": true}, {"accountNumber": "7500", "name": "Rent Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "7000", "level": 2, "classification": "management_general_expenses", "financialStatementSection": "statement_of_activities", "accountGroup": "rent_expenses", "cashFlowCategory": "operating", "displayOrder": 7500, "subcategory": "Rent", "gaapCategory": "Rent Expense", "ifrsCategory": "Rental Expense", "description": "Rent for office space", "isActive": true}]