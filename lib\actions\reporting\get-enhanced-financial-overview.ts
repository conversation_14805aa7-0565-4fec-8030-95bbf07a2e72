"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { db } from "@/db/drizzle";
import { accounts, journalEntries, transactions } from "@/db/schema/schema";
import { eq, and, sql } from "drizzle-orm";

/**
 * Generate enhanced financial overview using professional categorizations
 */
export async function getEnhancedFinancialOverview() {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  if (!orgId) throw new Error("Organization not found");

  // Get account balances with enhanced categorization
  const accountBalances = await db
    .select({
      accountGroup: accounts.accountGroup,
      financialStatementSection: accounts.financialStatementSection,
      type: accounts.type,
      balance: sql<number>`
        COALESCE(
          SUM(
            CASE 
              WHEN ${accounts.normalBalance} = 'debit' 
              THEN COALESCE(${journalEntries.debitAmount}, 0) - COALESCE(${journalEntries.creditAmount}, 0)
              ELSE COALESCE(${journalEntries.creditAmount}, 0) - COALESCE(${journalEntries.debitAmount}, 0)
            END
          ), 0
        )
      `.as('balance')
    })
    .from(accounts)
    .leftJoin(journalEntries, eq(accounts.id, journalEntries.accountId))
    .leftJoin(transactions, and(
      eq(journalEntries.transactionId, transactions.id),
      eq(transactions.status, 'posted')
    ))
    .where(and(
      eq(accounts.organizationId, orgId),
      eq(accounts.isActive, true),
      eq(accounts.isHeader, false)
    ))
    .groupBy(
      accounts.accountGroup,
      accounts.financialStatementSection,
      accounts.type
    );

  // Calculate key financial metrics using professional categorizations
  const totalRevenue = accountBalances
    .filter(acc => acc.type === 'revenue')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const operatingRevenue = accountBalances
    .filter(acc => acc.financialStatementSection === 'operating_revenue')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const totalExpenses = accountBalances
    .filter(acc => acc.type === 'expense')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const operatingExpenses = accountBalances
    .filter(acc => acc.financialStatementSection === 'operating_expenses')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const netIncome = totalRevenue - totalExpenses;
  
  const accountsReceivable = accountBalances
    .filter(acc => acc.accountGroup === 'accounts_receivable')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const accountsPayable = accountBalances
    .filter(acc => acc.accountGroup === 'accounts_payable')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const currentAssets = accountBalances
    .filter(acc => acc.financialStatementSection === 'current_assets')
    .reduce((sum, acc) => sum + acc.balance, 0);
    
  const currentLiabilities = accountBalances
    .filter(acc => acc.financialStatementSection === 'current_liabilities')
    .reduce((sum, acc) => sum + acc.balance, 0);

  return {
    totalRevenue: { 
      value: totalRevenue, 
      change: "+0.0%" // Will be calculated with historical data
    },
    netIncome: { 
      value: netIncome, 
      change: "+0.0%" 
    },
    totalExpenses: { 
      value: totalExpenses, 
      change: "+0.0%" 
    },
    accountsReceivable: { 
      value: accountsReceivable
    },
    accountsPayable: { 
      value: accountsPayable
    },
    // Enhanced professional metrics
    workingCapital: {
      value: currentAssets - currentLiabilities
    },
    currentRatio: {
      value: currentLiabilities > 0 ? currentAssets / currentLiabilities : 0
    },
    operatingMargin: {
      value: operatingRevenue > 0 ? ((operatingRevenue - operatingExpenses) / operatingRevenue * 100) : 0
    }
  };
} 