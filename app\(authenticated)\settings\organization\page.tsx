import { getServerUserContext } from "@/lib/server-auth";
import { OrganizationSettingsForm } from "./OrganizationSettingsForm";
import { db } from "@/db/drizzle";
import { organizationAddresses } from "@/db/schema/schema";
import { eq, and } from "drizzle-orm";

export default async function OrganizationProfilePage() {
  const { organization } = await getServerUserContext();

  const [rawAddress] = await db
    .select()
    .from(organizationAddresses)
    .where(
      and(
        eq(organizationAddresses.organizationId, organization.id),
        eq(organizationAddresses.type, "primary")
      )
    );

  // Map nulls to undefined for type compatibility
  const address = rawAddress
    ? {
        streetAddress: rawAddress.streetAddress ?? undefined,
        address2: rawAddress.address2 ?? undefined,
        city: rawAddress.city ?? undefined,
        zipCode: rawAddress.zipCode ?? undefined,
        country: rawAddress.country ?? undefined,
        timeZone: rawAddress.timeZone ?? undefined,
      }
    : undefined;

  return <OrganizationSettingsForm organization={organization} address={address} />;
} 