"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import * as React from "react";
import { invoices } from "@/db/schema/schema";

export type Invoice = typeof invoices.$inferSelect & { clientName?: string | null };

export const columns: ColumnDef<Invoice>[] = [
  { accessorKey: "invoiceNumber", header: "Number" },
  { accessorKey: "clientName", header: "Client" },
  {
    accessorKey: "date",
    header: "Issued",
    cell: ({ row }) => new Date(row.getValue("date")).toLocaleDateString(),
  },
  {
    accessorKey: "dueDate",
    header: "Due",
    cell: ({ row }) => row.getValue("dueDate") ? new Date(row.getValue("dueDate")).toLocaleDateString() : "-",
  },
  {
    accessorKey: "total",
    header: "Total",
    cell: ({ row }) => new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(Number(row.getValue("total"))),
  },
  {
    accessorKey: "amountPaid",
    header: "Paid",
    cell: ({ row }) => new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(Number(row.getValue("amountPaid"))),
  },
  {
    accessorKey: "amountRemaining",
    header: "Remaining",
    cell: ({ row }) => new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(Number(row.getValue("amountRemaining"))),
  },
  {
    accessorKey: "overpaid",
    header: "Overpaid",
    cell: ({ row }) => {
      const value = Number(row.getValue("overpaid"));
      if (value > 0) {
        return (
          <div className="flex flex-col gap-1">
            <span className="text-green-600 font-semibold">{new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: "USD",
            }).format(value)}</span>
            <Button size="sm" variant="outline" onClick={() => {/* TODO: Implement apply/refund credit */}}>
              Apply/Refund Credit
            </Button>
          </div>
        );
      }
      return "-";
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      // TODO: Add color variants for status
      return <Badge>{status}</Badge>;
    },
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => {
      const invoice = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => {/* TODO: Edit action */}}>
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => {/* TODO: Delete action */}}>
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];