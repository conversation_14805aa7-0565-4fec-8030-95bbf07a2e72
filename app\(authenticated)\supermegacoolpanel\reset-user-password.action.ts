"use server";
import { auth } from "@/lib/auth";
import { resetUserPassword } from "@/lib/actions/superadmin";
import { headers } from "next/headers";

export async function resetUserPasswordAction(formData: FormData) {
  const userId = formData.get("userId") as string;
  const newPassword = formData.get("newPassword") as string;
  const session = await auth.api.getSession({ headers: await headers() });
  return resetUserPassword(session, userId, newPassword);
} 