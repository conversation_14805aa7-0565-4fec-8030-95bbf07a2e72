import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { db } from "@/db/drizzle";
import { organizations, organizationMembers } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: await headers() });
    
    if (!session?.user) {
      return NextResponse.json({ 
        error: "Not authenticated"
      }, { status: 401 });
    }

    const user = session.user;

    // Check if user already has an organization
    const existingOrgs = await db
      .select()
      .from(organizations)
      .where(eq(organizations.ownerId, user.id));

    if (existingOrgs.length > 0) {
      return NextResponse.json({
        message: "User already has an organization",
        organization: existingOrgs[0]
      });
    }

    // Create organization with subscription (simulating successful checkout)
    const result = await db.transaction(async (tx) => {
      // Create organization
      const [newOrg] = await tx.insert(organizations)
        .values({
          name: `${user.name || 'User'}'s Organization`,
          slug: `${user.name || 'User'}'s Organization`,
          ownerId: user.id,
          email: user.email,
          subscriptionId: `manual_subscription_${Date.now()}`, // Temporary subscription ID
          planType: "professional", // Default to professional plan
          planStatus: "active",
          monthlyTransactionLimit: 500,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      // Create membership
      await tx.insert(organizationMembers)
        .values({
          userId: user.id,
          organizationId: newOrg.id,
          role: "owner",
          status: "active",
        });

      return newOrg;
    });

    console.log(`✅ [fix-subscription] Manually created organization ${result.id} for user ${user.id}`);

    return NextResponse.json({
      success: true,
      message: "Organization created successfully",
      organization: result,
      user: {
        id: user.id,
        email: user.email,
        name: user.name
      }
    });

  } catch (error) {
    console.error("❌ [fix-subscription] Error:", error);
    return NextResponse.json({ 
      error: "Failed to create organization",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 