"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { invoices, invoiceItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { z } from "zod";
import { and, eq } from "drizzle-orm";
import {
  invoiceFormSchema,
  lineItemSchema,
} from "@/lib/actions/invoices/invoice.schema";
import { randomUUID } from 'crypto';

type InvoiceFormData = z.infer<typeof invoiceFormSchema>;
type LineItem = z.infer<typeof lineItemSchema>;

export async function createInvoice(data: InvoiceFormData) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  const validatedFields = invoiceFormSchema.safeParse(data);

  if (!validatedFields.success) {
    throw new Error("Invalid fields provided.");
  }

  const { lineItems, totalAmount, issueDate, ...invoiceData } = validatedFields.data;
  const appliedCredit = (data as any).appliedCredit || 0;

  try {
    // Create the invoice first
    const [newInvoice] = await db
      .insert(invoices)
      .values({
        id: randomUUID(),
        ...invoiceData,
        total: totalAmount.toString(),
        subtotal: totalAmount.toString(),
        tax: invoiceData.tax?.toString() || "0.00",
        date: issueDate,
        organizationId,
        invoiceNumber: `INV-${Date.now()}`,
      })
      .returning({ id: invoices.id });

    // Apply overpaid credit if present
    if (appliedCredit > 0 && invoiceData.clientId) {
      // Find all overpaid invoices for this client, oldest first
      const overpaidInvoices = await db.query.invoices.findMany({
        where: and(
          eq(invoices.clientId, invoiceData.clientId),
          eq(invoices.organizationId, organizationId),
        ),
        orderBy: [invoices.date],
      });
      let remainingCredit = appliedCredit;
      for (const inv of overpaidInvoices) {
        const overpaid = Number(inv.overpaid);
        if (overpaid > 0 && remainingCredit > 0) {
          const toApply = Math.min(overpaid, remainingCredit);
          await db.update(invoices).set({
            overpaid: (overpaid - toApply).toFixed(2),
          }).where(eq(invoices.id, inv.id));
          remainingCredit -= toApply;
        }
        if (remainingCredit <= 0) break;
      }
    }

    // Then create the line items if any exist
    if (lineItems && lineItems.length > 0) {
      await db.insert(invoiceItems).values(
        lineItems.map((item: LineItem) => ({
          id: randomUUID(),
          productId: item.productId || null,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice.toString(),
          total: item.totalPrice.toString(),
          invoiceId: newInvoice.id,
        }))
      );
    }

    revalidatePath("/invoices");
    return { success: true, message: "Invoice created successfully." };
  } catch (error) {
    console.error(error);
    return { success: false, message: "Failed to create invoice." };
  }
} 