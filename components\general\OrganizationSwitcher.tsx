"use client";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"
import { authClient } from "@/lib/auth-client";
import { Organization } from "@/db/schema/schema";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Accepts either minimal org shape or full Organization shape
interface MinimalOrg {
  organizationId: string;
  organizationName: string;
  role?: string;
}

interface OrganizationSwitcherProps {
  organizations: MinimalOrg[] | Organization[];
  currentOrganizationId?: string; // Optional, for controlled value
  onSwitched?: (organizationId: string) => void; // Optional callback
}

export function OrganizationSwitcher({
  organizations,
  currentOrganizationId,
  onSwitched,
}: OrganizationSwitcherProps) {
  const { data: activeOrganization } = authClient.useActiveOrganization();
  const router = useRouter();

  // Normalize organizations to { id, name }
  const normalizedOrgs = (organizations as any[]).map((org) => {
    if ("organizationId" in org && "organizationName" in org) {
      return { id: org.organizationId, name: org.organizationName };
    }
    // Assume full Organization shape
    return { id: org.id, name: org.name };
  });

  const value =
    typeof currentOrganizationId === "string"
      ? currentOrganizationId
      : activeOrganization?.id;

  const handleOrganizationChange = async (organizationId: string) => {
    if (organizationId === "__create__") {
      router.push("/create-organization");
      return;
    }
    try {
      const { error } = await authClient.organization.setActive({
        organizationId,
      });
      if (error) {
        toast.error("Failed to switch organization");
      } else {
        toast.success(
          `Switched to ${
            normalizedOrgs.find((org) => org.id === organizationId)?.name || "organization"
          }`
        );
        if (onSwitched) onSwitched(organizationId);
        router.refresh(); // Or router.push("/dashboard") if you want to redirect
      }
    } catch (error) {
      toast.error("Failed to switch organization");
    }
  };

  return (
    <Select  onValueChange={handleOrganizationChange} value={value}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select Organization" />
      </SelectTrigger>
      <SelectContent>
        {normalizedOrgs.map((org) => (
          <SelectItem key={org.id} value={org.id}>
            {org.name}
          </SelectItem>
        ))}
        <SelectItem key="__create__" value="__create__">
          + Create New Organization
        </SelectItem>
      </SelectContent>
    </Select>
  );
}

