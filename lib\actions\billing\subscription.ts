"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { Polar } from "@polar-sh/sdk";
import { db } from "@/db/drizzle";
import {
  accounts,
  transactions,
  clients,
  products,
  member,
  user,
} from "@/db/schema/schema";
import { eq } from "drizzle-orm";

const polar = new Polar({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
});

export const cancelSubscriptionAction = async () => {
  try {
    const context = await getServerUserContext();

    if (context.membership.role !== "owner") {
      throw new Error("Forbidden: Only organization owners can cancel a subscription.");
    }

    const subscriptionId = context.organization.subscriptionId;

    if (!subscriptionId) {
      throw new Error("No active subscription found to cancel.");
    }

    const revokedSubscription = await polar.subscriptions.revoke({
      id: subscriptionId,
    });

    console.log(`[BILLING] Subscription ${subscriptionId} for org ${context.organization.id} cancellation initiated.`);

    return { success: true, data: revokedSubscription };
  } catch (error) {
    console.error("Subscription cancellation error:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";
    return { success: false, error: errorMessage };
  }
};

export const exportDataAction = async () => {
    try {
        const context = await getServerUserContext();
        const organizationId = context.organization.id;
        const organizationName = context.organization.name;

        // TODO: The following data export is incomplete as it references database tables
        // (bills, invoices, vendors, projects, budgets, etc.) that do not yet exist in the schema.
        // This action can be completed once the relevant schema migrations have been created.
        const exportData = {
            metadata: {
                exportDate: new Date().toISOString(),
                organizationName,
                organizationId,
                version: "1.0"
            },
            organization: context.organization,
            accounts: await db.select().from(accounts).where(eq(accounts.organizationId, organizationId)),
            transactions: await db.select().from(transactions).where(eq(transactions.organizationId, organizationId)),
            clients: await db.select().from(clients).where(eq(clients.organizationId, organizationId)),
            products: await db.select().from(products).where(eq(products.organizationId, organizationId)),
            members: await db
                .select({
                    userId: member.userId,
                    role: member.role,
                    userEmail: user.email,
                    userName: user.name,
                })
                .from(member)
                .innerJoin(user, eq(member.userId, user.id))
                .where(eq(member.organizationId, organizationId)),
        };

        const jsonData = JSON.stringify(exportData, null, 2);
        
        return { success: true, data: jsonData };

    } catch (error) {
        console.error("Data export error:", error);
        const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";
        return { success: false, error: errorMessage };
    }
} 