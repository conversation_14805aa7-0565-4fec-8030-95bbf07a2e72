[{"accountNumber": "1000", "name": "Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1000, "subcategory": "Main Category", "gaapCategory": "Assets", "ifrsCategory": "Assets", "description": "Main asset category header", "isActive": true}, {"accountNumber": "1100", "name": "Current Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1100, "subcategory": "Current Assets", "gaapCategory": "Current Assets", "ifrsCategory": "Current Assets", "description": "Assets expected to be converted to cash within one year", "isActive": true}, {"accountNumber": "1110", "name": "Cash and Cash Equivalents", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1110, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Cash on hand and highly liquid investments", "isActive": true}, {"accountNumber": "1111", "name": "Petty Cash", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1111, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Small cash fund for minor expenses", "isActive": true}, {"accountNumber": "1112", "name": "Checking Account", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1112, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Primary business checking account", "isActive": true}, {"accountNumber": "1113", "name": "Savings Account", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1113, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Business savings account", "isActive": true}, {"accountNumber": "1120", "name": "Accounts Receivable", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1120, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Money owed by customers", "isActive": true}, {"accountNumber": "1121", "name": "Trade Receivables", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1120", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1121, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Amount owed by customers for goods/services", "isActive": true}, {"accountNumber": "1125", "name": "Allowance for Doubtful Accounts", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1120", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1125, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Estimated uncollectible receivables", "isActive": true}, {"accountNumber": "1130", "name": "Inventory", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1130, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Goods held for sale", "isActive": true}, {"accountNumber": "1131", "name": "Raw Materials", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1130", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1131, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Materials used in production", "isActive": true}, {"accountNumber": "1132", "name": "Work in Process", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1130", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1132, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Partially completed goods", "isActive": true}, {"accountNumber": "1133", "name": "Finished Goods", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1130", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1133, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Completed goods ready for sale", "isActive": true}, {"accountNumber": "1140", "name": "Prepaid Expenses", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "prepaid_expenses", "cashFlowCategory": "operating", "displayOrder": 1140, "subcategory": "Prepaid Expenses", "gaapCategory": "Prepaid Expenses", "ifrsCategory": "Other Current Assets", "description": "Expenses paid in advance", "isActive": true}, {"accountNumber": "1141", "name": "Prepaid Insurance", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1140", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "prepaid_expenses", "cashFlowCategory": "operating", "displayOrder": 1141, "subcategory": "Prepaid Expenses", "gaapCategory": "Prepaid Expenses", "ifrsCategory": "Other Current Assets", "description": "Insurance premiums paid in advance", "isActive": true}, {"accountNumber": "1142", "name": "Prepaid Rent", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1140", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "prepaid_expenses", "cashFlowCategory": "operating", "displayOrder": 1142, "subcategory": "Prepaid Expenses", "gaapCategory": "Prepaid Expenses", "ifrsCategory": "Other Current Assets", "description": "Rent paid in advance", "isActive": true}, {"accountNumber": "1200", "name": "Non-Current Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1200, "subcategory": "Non-Current Assets", "gaapCategory": "Non-Current Assets", "ifrsCategory": "Non-Current Assets", "description": "Assets with useful life greater than one year", "isActive": true}, {"accountNumber": "1210", "name": "Property, Plant & Equipment", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1200", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1210, "subcategory": "Property, Plant & Equipment", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Long-term tangible assets", "isActive": true}, {"accountNumber": "1211", "name": "Land", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1210", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1211, "subcategory": "Property, Plant & Equipment", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Land owned by the company", "isActive": true}, {"accountNumber": "1212", "name": "Buildings", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1210", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1212, "subcategory": "Property, Plant & Equipment", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Buildings owned by the company", "isActive": true}, {"accountNumber": "1213", "name": "Equipment", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1210", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1213, "subcategory": "Property, Plant & Equipment", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Machinery and equipment", "isActive": true}, {"accountNumber": "1214", "name": "Vehicles", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1210", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1214, "subcategory": "Property, Plant & Equipment", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Company vehicles", "isActive": true}, {"accountNumber": "1215", "name": "Furniture & Fixtures", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1210", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1215, "subcategory": "Property, Plant & Equipment", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Office furniture and fixtures", "isActive": true}, {"accountNumber": "1220", "name": "Accumulated Depreciation", "type": "asset", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "1200", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "accumulated_depreciation", "cashFlowCategory": "n_a", "displayOrder": 1220, "subcategory": "Accumulated Depreciation", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Accumulated depreciation on assets", "isActive": true}, {"accountNumber": "1221", "name": "Accumulated Depreciation - Buildings", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1220", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "accumulated_depreciation", "cashFlowCategory": "n_a", "displayOrder": 1221, "subcategory": "Accumulated Depreciation", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Accumulated depreciation on buildings", "isActive": true}, {"accountNumber": "1222", "name": "Accumulated Depreciation - Equipment", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1220", "level": 3, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "accumulated_depreciation", "cashFlowCategory": "n_a", "displayOrder": 1222, "subcategory": "Accumulated Depreciation", "gaapCategory": "Property, Plant & Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Accumulated depreciation on equipment", "isActive": true}, {"accountNumber": "2000", "name": "Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2000, "subcategory": "Main Category", "gaapCategory": "Liabilities", "ifrsCategory": "Liabilities", "description": "Main liability category header", "isActive": true}, {"accountNumber": "2100", "name": "Current Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2100, "subcategory": "Current Liabilities", "gaapCategory": "Current Liabilities", "ifrsCategory": "Current Liabilities", "description": "Obligations due within one year", "isActive": true}, {"accountNumber": "2110", "name": "Accounts Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accounts_payable", "cashFlowCategory": "operating", "displayOrder": 2110, "subcategory": "Accounts Payable", "gaapCategory": "Accounts Payable", "ifrsCategory": "Trade and Other Payables", "description": "Money owed to suppliers", "isActive": true}, {"accountNumber": "2120", "name": "Accrued Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accrued_liabilities", "cashFlowCategory": "operating", "displayOrder": 2120, "subcategory": "Accrued Liabilities", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Other Current Liabilities", "description": "Expenses incurred but not yet paid", "isActive": true}, {"accountNumber": "2121", "name": "Accrued Wages", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2120", "level": 3, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accrued_liabilities", "cashFlowCategory": "operating", "displayOrder": 2121, "subcategory": "Accrued Liabilities", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Other Current Liabilities", "description": "Wages earned but not yet paid", "isActive": true}, {"accountNumber": "2130", "name": "Taxes Payable", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "other_liabilities", "cashFlowCategory": "operating", "displayOrder": 2130, "subcategory": "Tax Liabilities", "gaapCategory": "Tax Liabilities", "ifrsCategory": "Current Tax Liabilities", "description": "Taxes owed to government", "isActive": true}, {"accountNumber": "2131", "name": "Sales Tax Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2130", "level": 3, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "other_liabilities", "cashFlowCategory": "operating", "displayOrder": 2131, "subcategory": "Tax Liabilities", "gaapCategory": "Tax Liabilities", "ifrsCategory": "Current Tax Liabilities", "description": "Sales tax collected but not remitted", "isActive": true}, {"accountNumber": "2140", "name": "Short-term Debt", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "short_term_debt", "cashFlowCategory": "financing", "displayOrder": 2140, "subcategory": "Short-term Debt", "gaapCategory": "Short-term Debt", "ifrsCategory": "Financial Liabilities", "description": "Debt due within one year", "isActive": true}, {"accountNumber": "2200", "name": "Non-Current Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2200, "subcategory": "Non-Current Liabilities", "gaapCategory": "Non-Current Liabilities", "ifrsCategory": "Non-Current Liabilities", "description": "Obligations due after one year", "isActive": true}, {"accountNumber": "2210", "name": "Long-term Debt", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2200", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_liabilities", "accountGroup": "long_term_debt", "cashFlowCategory": "financing", "displayOrder": 2210, "subcategory": "Long-term Debt", "gaapCategory": "Long-term Debt", "ifrsCategory": "Financial Liabilities", "description": "Debt due after one year", "isActive": true}, {"accountNumber": "3000", "name": "Equity", "type": "equity", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "equity", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 3000, "subcategory": "Main Category", "gaapCategory": "Equity", "ifrsCategory": "Equity", "description": "Owner's equity in the business", "isActive": true}, {"accountNumber": "3100", "name": "Capital Stock", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "n_a", "financialStatementSection": "equity", "accountGroup": "capital_stock", "cashFlowCategory": "financing", "displayOrder": 3100, "subcategory": "Capital Stock", "gaapCategory": "Capital Stock", "ifrsCategory": "Share Capital", "description": "Investment by owners", "isActive": true}, {"accountNumber": "3200", "name": "Retained Earnings", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "n_a", "financialStatementSection": "equity", "accountGroup": "retained_earnings", "cashFlowCategory": "n_a", "displayOrder": 3200, "subcategory": "Retained Earnings", "gaapCategory": "Retained Earnings", "ifrsCategory": "Retained Earnings", "description": "Accumulated profits retained in business", "isActive": true}, {"accountNumber": "4000", "name": "Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "operating_revenue", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 4000, "subcategory": "Main Category", "gaapCategory": "Revenue", "ifrsCategory": "Revenue", "description": "Income from business operations", "isActive": true}, {"accountNumber": "4100", "name": "Operating Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "4000", "level": 1, "classification": "operating", "financialStatementSection": "operating_revenue", "accountGroup": "other", "cashFlowCategory": "operating", "displayOrder": 4100, "subcategory": "Operating Revenue", "gaapCategory": "Operating Revenue", "ifrsCategory": "Revenue from Contracts with Customers", "description": "Revenue from primary business activities", "isActive": true}, {"accountNumber": "4110", "name": "Sales Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4100", "level": 2, "classification": "operating", "financialStatementSection": "operating_revenue", "accountGroup": "product_sales", "cashFlowCategory": "operating", "displayOrder": 4110, "subcategory": "Product Sales", "gaapCategory": "Sales Revenue", "ifrsCategory": "Revenue from Contracts with Customers", "description": "Revenue from product sales", "isActive": true}, {"accountNumber": "4120", "name": "Service Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4100", "level": 2, "classification": "operating", "financialStatementSection": "operating_revenue", "accountGroup": "service_revenue", "cashFlowCategory": "operating", "displayOrder": 4120, "subcategory": "Service Revenue", "gaapCategory": "Service Revenue", "ifrsCategory": "Revenue from Contracts with Customers", "description": "Revenue from services provided", "isActive": true}, {"accountNumber": "4200", "name": "Other Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "4000", "level": 1, "classification": "non_operating", "financialStatementSection": "other_revenue", "accountGroup": "other_revenue", "cashFlowCategory": "operating", "displayOrder": 4200, "subcategory": "Other Revenue", "gaapCategory": "Other Revenue", "ifrsCategory": "Other Income", "description": "Revenue from non-primary activities", "isActive": true}, {"accountNumber": "4210", "name": "Interest Income", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4200", "level": 2, "classification": "non_operating", "financialStatementSection": "other_revenue", "accountGroup": "other_revenue", "cashFlowCategory": "operating", "displayOrder": 4210, "subcategory": "Interest Income", "gaapCategory": "Interest Income", "ifrsCategory": "Finance Income", "description": "Income from interest on investments", "isActive": true}, {"accountNumber": "5000", "name": "Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "cost_of_goods_sold", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 5000, "subcategory": "Main Category", "gaapCategory": "Expenses", "ifrsCategory": "Expenses", "description": "Costs of business operations", "isActive": true}, {"accountNumber": "5100", "name": "Cost of Goods Sold", "type": "expense", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "5000", "level": 1, "classification": "operating", "financialStatementSection": "cost_of_goods_sold", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5100, "subcategory": "Cost of Goods Sold", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Direct costs of producing goods sold", "isActive": true}, {"accountNumber": "5110", "name": "Materials", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5100", "level": 2, "classification": "operating", "financialStatementSection": "cost_of_goods_sold", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5110, "subcategory": "Materials", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Cost of raw materials used", "isActive": true}, {"accountNumber": "5120", "name": "Direct Labor", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5100", "level": 2, "classification": "operating", "financialStatementSection": "cost_of_goods_sold", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5120, "subcategory": "Direct Labor", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Direct labor costs for production", "isActive": true}, {"accountNumber": "6000", "name": "Operating Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "5000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "other", "cashFlowCategory": "operating", "displayOrder": 6000, "subcategory": "Operating Expenses", "gaapCategory": "Operating Expenses", "ifrsCategory": "Operating Expenses", "description": "Expenses from normal business operations", "isActive": true}, {"accountNumber": "6100", "name": "Selling Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "6000", "level": 2, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "selling_expenses", "cashFlowCategory": "operating", "displayOrder": 6100, "subcategory": "Selling Expenses", "gaapCategory": "Selling Expenses", "ifrsCategory": "Distribution Costs", "description": "Expenses related to selling activities", "isActive": true}, {"accountNumber": "6110", "name": "Advertising Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6100", "level": 3, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "selling_expenses", "cashFlowCategory": "operating", "displayOrder": 6110, "subcategory": "Advertising", "gaapCategory": "Selling Expenses", "ifrsCategory": "Distribution Costs", "description": "Cost of advertising and marketing", "isActive": true}, {"accountNumber": "6200", "name": "Administrative Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "6000", "level": 2, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "administrative_expenses", "cashFlowCategory": "operating", "displayOrder": 6200, "subcategory": "Administrative Expenses", "gaapCategory": "Administrative Expenses", "ifrsCategory": "Administrative Expenses", "description": "General administrative costs", "isActive": true}, {"accountNumber": "6210", "name": "Salaries Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6200", "level": 3, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "administrative_expenses", "cashFlowCategory": "operating", "displayOrder": 6210, "subcategory": "Salaries", "gaapCategory": "Administrative Expenses", "ifrsCategory": "Employee Benefits", "description": "Employee salaries and wages", "isActive": true}, {"accountNumber": "6220", "name": "Rent Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6200", "level": 3, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "administrative_expenses", "cashFlowCategory": "operating", "displayOrder": 6220, "subcategory": "Rent", "gaapCategory": "Administrative Expenses", "ifrsCategory": "Administrative Expenses", "description": "Office and facility rent", "isActive": true}, {"accountNumber": "6230", "name": "Utilities Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6200", "level": 3, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "administrative_expenses", "cashFlowCategory": "operating", "displayOrder": 6230, "subcategory": "Utilities", "gaapCategory": "Administrative Expenses", "ifrsCategory": "Administrative Expenses", "description": "Electricity, water, internet, phone", "isActive": true}, {"accountNumber": "6240", "name": "Insurance Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6200", "level": 3, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "administrative_expenses", "cashFlowCategory": "operating", "displayOrder": 6240, "subcategory": "Insurance", "gaapCategory": "Administrative Expenses", "ifrsCategory": "Administrative Expenses", "description": "Business insurance premiums", "isActive": true}, {"accountNumber": "6250", "name": "Depreciation Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6200", "level": 3, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "depreciation_amortization", "cashFlowCategory": "n_a", "displayOrder": 6250, "subcategory": "Depreciation", "gaapCategory": "Depreciation Expense", "ifrsCategory": "Depreciation and Amortisation", "description": "Depreciation of fixed assets", "isActive": true}, {"accountNumber": "7000", "name": "Other Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "5000", "level": 1, "classification": "non_operating", "financialStatementSection": "other_expenses", "accountGroup": "other_expenses", "cashFlowCategory": "operating", "displayOrder": 7000, "subcategory": "Other Expenses", "gaapCategory": "Other Expenses", "ifrsCategory": "Finance Costs", "description": "Non-operating expenses", "isActive": true}, {"accountNumber": "7100", "name": "Interest Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "7000", "level": 2, "classification": "non_operating", "financialStatementSection": "other_expenses", "accountGroup": "interest_expense", "cashFlowCategory": "operating", "displayOrder": 7100, "subcategory": "Interest Expense", "gaapCategory": "Interest Expense", "ifrsCategory": "Finance Costs", "description": "Interest paid on debt", "isActive": true}]