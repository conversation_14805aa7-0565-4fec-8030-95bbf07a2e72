"use client";

import React, { useEffect, useTransition } from "react";
import { useForm, useF<PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { billFormSchema, NewBill } from "@/lib/actions/bills/bill.schema";
import { getBillDetails } from "@/lib/actions/bills/get-bill-details";
import { getBillById } from "@/lib/actions/bills/get-bill-by-id";
import { toast } from "sonner";
import { Plus, Trash } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getVendors } from "@/lib/actions/vendors/get-vendors";
import { getProducts } from "@/lib/actions/products/get-products";
import { Calendar as CalendarIcon, PlusCircle, Trash2 } from "lucide-react";

const STATUS_OPTIONS = ["draft", "submitted", "approved", "paid", "void"] as const;

interface Vendor { id: string; name: string; }
interface Product { id: string; name: string; }

interface AddBillDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (billData: NewBill) => void;
  isPending: boolean;
  billId?: string | null;
  vendors: { id: string; name: string }[];
}

export default function AddBillDialog({ open, onOpenChange, onSubmit, isPending, billId, vendors }: AddBillDialogProps) {
  const [isFetchingDetails, startFetching] = useTransition();

  // Fetch bill details if editing
  const isEdit = !!billId;
  const { data: bill, isLoading: isLoadingBill } = useQuery({
    queryKey: ["bill", billId],
    queryFn: () => billId ? getBillById(billId) : null,
    enabled: !!billId && open,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  // Fetch vendors
  const { data: vendors = [], isLoading: isLoadingVendors } = useQuery({
    queryKey: ["vendors"],
    queryFn: getVendors,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  // Fetch products
  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products"],
    queryFn: getProducts,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const {
    register,
    handleSubmit,
    control,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<NewBill>({
    resolver: zodResolver(billFormSchema),
    defaultValues: {
      status: "draft",
      lineItems: [{ description: "", quantity: 1, unitPrice: 0, total: 0 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "lineItems",
  });

  useEffect(() => {
    if (open && bill) {
      startFetching(async () => {
        const result = await getBillDetails(bill.id);
        if (result.success && result.data) {
          const billData = result.data;
          reset({
            ...billData,
            status: billData.status ?? 'draft',
            date: new Date(billData.date),
            dueDate: billData.dueDate ? new Date(billData.dueDate) : new Date(),
            lineItems: billData.lineItems.map(item => ({
              productId: item.productId,
              description: item.description,
              quantity: item.quantity,
              unitPrice: Number(item.unitPrice),
              total: Number(item.total),
            }))
          });
        } else {
          toast.error(result.message || "Failed to fetch bill details.");
          onOpenChange(false);
        }
      });
    } else if (open && !bill) {
      reset({
        vendorId: "",
        billNumber: "",
        date: new Date(),
        dueDate: new Date(),
        status: "draft",
        lineItems: [{ description: "", quantity: 1, unitPrice: 0, total: 0 }],
      });
    }
  }, [bill, open, reset, onOpenChange]);

  const lineItems = watch("lineItems");
  const total = lineItems.reduce((sum: number, item: { quantity: number; unitPrice: number; }) => sum + item.quantity * item.unitPrice, 0);
  
  const formatDateForInput = (date: Date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{bill ? "Edit Bill" : "Add Bill"}</DialogTitle>
          <DialogDescription>
            {bill ? "Update the bill information below." : "Create a new bill with vendor details and line items."}
          </DialogDescription>
        </DialogHeader>
        {isFetchingDetails ? (
          <div className="p-8 text-center">Loading bill details...</div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Vendor</Label>
                <Select onValueChange={(value) => setValue("vendorId", value)} value={watch("vendorId")}>
                  <SelectTrigger><SelectValue placeholder="Select vendor" /></SelectTrigger>
                  <SelectContent>
                    {vendors.map((v: Vendor) => <SelectItem key={v.id} value={v.id}>{v.name}</SelectItem>)}
                  </SelectContent>
                </Select>
                {errors.vendorId && <p className="text-red-500 text-xs">{errors.vendorId.message}</p>}
              </div>
              <div>
                <Label>Bill Number</Label>
                <Input {...register("billNumber")} />
                {errors.billNumber && <p className="text-red-500 text-xs">{errors.billNumber.message}</p>}
              </div>
              <div>
                <Label>Date</Label>
                <Input type="date" {...register("date", { valueAsDate: true })} defaultValue={formatDateForInput(watch("date"))} />
                {errors.date && <p className="text-red-500 text-xs">{errors.date.message}</p>}
              </div>
              <div>
                <Label>Due Date</Label>
                <Input type="date" {...register("dueDate", { valueAsDate: true })} defaultValue={formatDateForInput(watch("dueDate"))} />
                {errors.dueDate && <p className="text-red-500 text-xs">{errors.dueDate.message}</p>}
              </div>
              <div>
                <Label>Status</Label>
                <Select onValueChange={(value) => setValue("status", value as typeof STATUS_OPTIONS[number])} value={watch("status")}>
                  <SelectTrigger><SelectValue placeholder="Select status" /></SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map(s => <SelectItem key={s} value={s}>{s}</SelectItem>)}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Label>Line Items</Label>
            <div className="space-y-2">
              {fields.map((field, index) => (
                <div key={field.id} className="flex gap-2 items-center">
                  <Input {...register(`lineItems.${index}.description`)} placeholder="Description" className="flex-grow" />
                  <Input type="number" {...register(`lineItems.${index}.quantity`, { valueAsNumber: true })} placeholder="Qty" className="w-20" />
                  <Input type="number" {...register(`lineItems.${index}.unitPrice`, { valueAsNumber: true })} placeholder="Price" className="w-24" />
                  <span className="w-24 text-right">${((lineItems[index]?.quantity || 0) * (lineItems[index]?.unitPrice || 0)).toFixed(2)}</span>
                  <Button type="button" variant="ghost" onClick={() => remove(index)} disabled={fields.length === 1}><Trash className="h-4 w-4" /></Button>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={() => append({ description: "", quantity: 1, unitPrice: 0, total: 0 })}>
                <Plus className="h-4 w-4 mr-2" /> Add Item
              </Button>
            </div>
            
            <DialogFooter className="pt-4">
                <div className="flex-grow font-semibold text-lg">Total: ${total.toFixed(2)}</div>
                <Button type="button" variant="ghost" onClick={() => onOpenChange(false)} disabled={isPending}>Cancel</Button>
                <Button type="submit" disabled={isPending || isFetchingDetails}>
                    {isPending ? "Saving..." : "Save Bill"}
                </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
