"use server";

import { db } from "@/db/drizzle";
import { budgetLineItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { revalidatePath } from "next/cache";

export async function createBudgetLineItem(data: {
  budgetPeriodId: string;
  accountId: string;
  budgetedAmount: number;
  notes?: string;
}) {
  await getServerUserContext();

  try {
    const [lineItem] = await db.insert(budgetLineItems).values({
      budgetPeriodId: data.budgetPeriodId,
      accountId: data.accountId,
      budgetedAmount: String(data.budgetedAmount),
      notes: data.notes,
    }).returning();

    revalidatePath("/accounting/budget");
    return { success: true, data: lineItem };
  } catch (error) {
    console.error("Error creating budget line item:", error);
    return { error: "Failed to create budget line item" };
  }
} 