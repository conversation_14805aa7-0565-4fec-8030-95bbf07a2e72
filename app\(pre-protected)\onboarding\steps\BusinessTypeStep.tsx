"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Building2, Monitor, Shuffle, CheckCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { useDebouncedCallback } from 'use-debounce';
import { updateOnboardingStep } from "@/lib/actions/onboarding/updateOnboardingStep";
import { ONBOARDING_STEPS } from "@/lib/onboarding-constants";

interface BusinessTypeStepProps {
  isLoading: boolean;
  onNext: (data: { businessType: BusinessType }) => void;
  onPrevious: () => void;
  userId: string;
}

type BusinessType = "physical" | "digital" | "hybrid";

const businessTypes = [
  {
    id: "physical" as BusinessType,
    icon: Building2,
    title: "Physical Business",
    description: "Sell physical products, have inventory, shipping, warehouses",
    examples: ["Retail stores", "Manufacturing", "Distribution", "Restaurants", "Auto repair"],
    color: "bg-blue-500",
    features: ["Inventory management", "Shipping tracking", "Physical locations", "COGS tracking"]
  },
  {
    id: "digital" as BusinessType,
    icon: Monitor,
    title: "Digital Business",
    description: "Sell digital products/services, no physical inventory",
    examples: ["SaaS companies", "Consulting", "Digital marketing", "Online courses", "Freelancing"],
    color: "bg-green-500",
    features: ["Subscription billing", "Digital assets", "Remote operations", "Service tracking"]
  },
  {
    id: "hybrid" as BusinessType,
    icon: Shuffle,
    title: "Hybrid Business",
    description: "Mix of physical and digital products/services",
    examples: ["E-commerce + SaaS", "Physical + Software", "Retail + Services", "Product + Training"],
    color: "bg-purple-500",
    features: ["Multi-channel sales", "Complex inventory", "Service + product mix", "Flexible accounting"]
  }
];

export function BusinessTypeStep({ 
  isLoading, 
  onNext, 
  onPrevious,
  userId
}: BusinessTypeStepProps) {
  const { updateData, data: onboardingData } = useOnboardingStore();
  const [businessType, setBusinessType] = useState<BusinessType | undefined>(undefined);

  // Pre-select business type from onboarding store on mount
  useEffect(() => {
    if (!businessType && onboardingData?.businessType) {
      setBusinessType(onboardingData.businessType as BusinessType);
    }
  }, [onboardingData]);

  // Debounced auto-save for business type
  const debouncedAutoSave = useDebouncedCallback((type: BusinessType | undefined) => {
    if (type) {
      updateData({ businessDetails: { ...(onboardingData.businessDetails || {}), businessType: type } });
    }
  }, 2000);

  // Watch businessType and trigger debounced auto-save
  useEffect(() => {
    debouncedAutoSave(businessType);
  }, [businessType, debouncedAutoSave]);

  const handleNext = async () => {
    if (businessType) {
      updateData({ businessDetails: { ...(onboardingData.businessDetails || {}), businessType } });
      // Persist to backend
      await updateOnboardingStep(userId, ONBOARDING_STEPS.BUSINESS_TYPE, { businessType });
      onNext({ businessType });
    }
  };

  return (
    <div className="max-w-12xl mx-auto space-y-4 sm:space-y-6 md:space-y-8 px-4 sm:px-6">
      {/* Header */}
      <div className="text-center space-y-2 sm:space-y-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-white">
          What type of business are you running?
        </h2>
        <p className="text-sm sm:text-base text-gray-300 max-w-2xl mx-auto">
          This helps us customize your chart of accounts and recommend the best features for your specific needs.
        </p>
      </div>

      {/* Business Type Options */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {businessTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = businessType === type.id;
          
          return (
            <Card 
              key={type.id}
              className={`relative cursor-pointer transition-all duration-300 hover:shadow-lg ${
                isSelected 
                  ? 'border-2 border-blue-500 bg-blue-900/30 shadow-lg transform scale-[1.02] sm:scale-105' 
                  : 'border border-gray-600 bg-gray-800/50 hover:border-gray-500'
              }`}
              onClick={() => {
                const selectedType = type.id;
                setBusinessType(selectedType);
                // Update the onboarding store with selected business type
                updateData({ businessDetails: { ...(onboardingData.businessDetails || {}), businessType: selectedType } });
              }}
            >
              {isSelected && (
                <div className="absolute -top-2 -right-2 z-10">
                  <div className="w-5 h-5 sm:w-6 sm:h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  </div>
                </div>
              )}
              
              <CardHeader className="text-center space-y-3 sm:space-y-4 pb-3 sm:pb-4">
                <div className={`w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 ${type.color} rounded-xl sm:rounded-2xl flex items-center justify-center mx-auto`}>
                  <Icon className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg sm:text-xl text-white">{type.title}</CardTitle>
                  <CardDescription className="mt-1 sm:mt-2 text-sm text-gray-300">{type.description}</CardDescription>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3 sm:space-y-4 pt-0">
                {/* Examples */}
                <div>
                  <h4 className="font-medium text-sm text-gray-200 mb-2">Examples:</h4>
                  <div className="flex flex-wrap gap-1">
                    {type.examples.map((example, index) => (
                      <Badge key={index} variant="secondary" className="text-xs bg-gray-700 text-gray-200 px-2 py-1">
                        {example}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {/* Key Features */}
                <div>
                  <h4 className="font-medium text-sm text-gray-200 mb-2">Key Features:</h4>
                  <div className="space-y-1">
                    {type.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-xs sm:text-sm text-gray-300">
                        <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-gray-400 rounded-full mr-2 flex-shrink-0"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Help Text */}
      <Card className="bg-yellow-900/30 border-yellow-500/30">
        <CardContent className="p-3 sm:p-4">
          <div className="flex items-start space-x-3">
            <div className="w-4 h-4 sm:w-5 sm:h-5 bg-yellow-500 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
              <span className="text-yellow-900 text-xs font-bold">?</span>
            </div>
            <div>
              <h4 className="font-medium text-sm sm:text-base text-yellow-300">Not sure which type fits your business?</h4>
              <p className="text-xs sm:text-sm text-yellow-200 mt-1">
                Choose the option that represents the majority of your revenue. You can always adjust your 
                chart of accounts later, and we'll help you set up the right tracking for all revenue streams.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
          className="flex items-center justify-center space-x-2 order-2 sm:order-1"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Previous</span>
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={!businessType || isLoading}
          className="flex items-center justify-center space-x-2 order-1 sm:order-2"
        >
          <span>{isLoading ? "Saving..." : "Continue"}</span>
          <ArrowLeft className="w-4 h-4 rotate-180" />
        </Button>
      </div>
    </div>
  );
} 