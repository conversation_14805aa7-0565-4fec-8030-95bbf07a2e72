"use server";

import { db } from "@/db/drizzle";
import { accounts, journalEntries, transactions } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, gte, lte, sum } from "drizzle-orm";

export async function getActualAmountsForPeriod(startDate: Date, endDate: Date) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    try {
        const result = await db
            .select({
                accountId: journalEntries.accountId,
                normalBalance: accounts.normalBalance,
                totalDebits: sum(journalEntries.debitAmount),
                totalCredits: sum(journalEntries.creditAmount),
            })
            .from(journalEntries)
            .leftJoin(transactions, eq(journalEntries.transactionId, transactions.id))
            .leftJoin(accounts, eq(journalEntries.accountId, accounts.id))
            .where(
                and(
                    eq(transactions.organizationId, organizationId),
                    gte(transactions.date, startDate),
                    lte(transactions.date, endDate),
                    eq(transactions.status, "posted")
                )
            )
            .groupBy(journalEntries.accountId, accounts.normalBalance);

        const actuals: { [key: string]: number } = {};
        for (const row of result) {
            if (row.accountId) {
                const debits = parseFloat(row.totalDebits || '0');
                const credits = parseFloat(row.totalCredits || '0');
                if (row.normalBalance === 'debit') {
                    actuals[row.accountId] = debits - credits;
                } else {
                    actuals[row.accountId] = credits - debits;
                }
            }
        }

        return actuals;
    } catch (error) {
        console.error("Error fetching actual amounts:", error);
        return {};
    }
} 