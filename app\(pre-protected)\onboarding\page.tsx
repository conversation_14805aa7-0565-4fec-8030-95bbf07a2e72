import { redirect } from "next/navigation";
import { initializeOnboarding } from "@/lib/actions/onboarding/initializeOnboarding";
import { OnboardingClient } from "./OnboardingClient";
import { db } from "@/db/drizzle";
import { organization, member, onboarding } from "@/db/schema/schema";
import { eq } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { resetOnboarding } from "@/lib/actions/onboarding/resetOnboarding";
import { Button } from "@/components/ui/button";

// Helper function to create user context
function createUserContext(session: any) {
  return {
    id: session.user.id || '',
    name: session.user.name || '',
    email: session.user.email || '',
    image: session.user.image || '',
  };
}

// Helper function to create organization context
function createOrganizationContext(org: any = null) {
  if (!org) {
    return {
      id: '',
      name: 'My Organization',
      businessType: null,
      planType: null,
      planStatus: null,
      subscriptionId: null,
      subscriptionCurrentPeriodEnd: null,
      trialEndsAt: null,
      monthlyTransactionCount: null,
      isTrialing: false,
      isActive: false,
      logoUrl: null,
      email: null,
      industry: null,
      website: null,
      phone: null,
      taxId: null,
      currency: null,
    };
  }

  return {
    id: org.id,
    name: org.name,
    businessType: org.businessType,
    planType: org.planType,
    planStatus: org.planStatus,
    subscriptionId: org.subscriptionId,
    subscriptionCurrentPeriodEnd: org.subscriptionCurrentPeriodEnd,
    trialEndsAt: org.trialEndsAt,
    monthlyTransactionCount: org.monthlyTransactionCount,
    isTrialing: org.trialEndsAt ? new Date() < org.trialEndsAt : false,
    isActive: org.isActive ?? false,
    logoUrl: org.logo,
    email: org.email,
    industry: org.industry,
    website: org.website,
    phone: org.phone,
    taxId: org.taxId,
    currency: org.currency,
  };
}

// Helper function to render onboarding client
function renderOnboardingClient(session: any, org: any, progress: any) {
  return (
    <OnboardingClient 
      context={{
        user: createUserContext(session),
        organization: createOrganizationContext(org),
        isOnboardingCompleted: false,
      }} 
      progress={progress} 
    />
  );
}

export default async function OnboardingPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers())
    });

    if (!session?.user?.id) {
      redirect('/signin');
    }

    // Handle reset parameter
    const params = await searchParams;
    const shouldReset = params?.reset === "true";
    if (shouldReset) {
      await resetOnboarding(session.user.id);
    }

    // Get user's organization memberships
    const orgMemberships = await db.select()
      .from(member)
      .where(eq(member.userId, session.user.id))
      .innerJoin(organization, eq(member.organizationId, organization.id));

    // Case 1: No organization memberships - show onboarding for new org
    if (!orgMemberships || orgMemberships.length === 0) {
      const progress = await initializeOnboarding();
      return renderOnboardingClient(session, null, progress);
    }

    // Case 2: User is a member but not owner - show pending message
    const pendingOrg = orgMemberships.find(m => m.member.role !== 'owner');
    if (pendingOrg) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800">
          <div className="max-w-md w-full space-y-8 text-center text-white">
            <h1 className="text-2xl font-bold mb-4">Onboarding In Progress</h1>
            <p className="mb-6 text-gray-300">
              Your organization is currently being set up by the owner. 
              You'll be able to access the dashboard once onboarding is complete.
            </p>
            <div className="space-y-3">
              <p className="text-sm text-gray-400">
                Organization: <span className="font-medium">{pendingOrg.organization.name}</span>
              </p>
              <p className="text-sm text-gray-400">
                Your role: <span className="font-medium capitalize">{pendingOrg.member.role}</span>
              </p>
            </div>
            <form action="/dashboard" className="pt-4">
              <Button type="submit" variant="outline" className="w-full">
                Check Status
              </Button>
            </form>
          </div>
        </div>
      );
    }

    // Case 3: Check if onboarding is already completed
    const onboardingRow = await db
      .select({ isCompleted: onboarding.isCompleted })
      .from(onboarding)
      .where(eq(onboarding.userId, session.user.id))
      .limit(1);
    
    const isOnboardingCompleted = onboardingRow[0]?.isCompleted ?? false;
    if (isOnboardingCompleted && orgMemberships.find(m => m.organization.isActive)) {
      redirect('/dashboard');
    }

    // Case 4: User is owner of org that needs onboarding
    const ownerOrg = orgMemberships.find(m => m.member.role === 'owner');
    if (ownerOrg) {
      const progress = await initializeOnboarding();
      return renderOnboardingClient(session, ownerOrg.organization, progress);
    }

    // Fallback: show onboarding for safety
    const progress = await initializeOnboarding();
    return renderOnboardingClient(session, null, progress);

  } catch (error) {
    console.error('Error in onboarding page:', error);
    // Redirect to signin on error
    redirect('/signin');
  }
}