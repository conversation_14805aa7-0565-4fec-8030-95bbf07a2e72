[{"accountNumber": "1000", "name": "Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1000, "subcategory": "Main Category", "gaapCategory": "Assets", "ifrsCategory": "Assets", "description": "Main asset category header", "isActive": true}, {"accountNumber": "1100", "name": "Current Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 1100, "subcategory": "Current Assets", "gaapCategory": "Current Assets", "ifrsCategory": "Current Assets", "description": "Assets expected to be converted to cash within one year", "isActive": true}, {"accountNumber": "1110", "name": "Cash and Cash Equivalents", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "n_a", "displayOrder": 1110, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Cash on hand and in bank accounts", "isActive": true}, {"accountNumber": "1112", "name": "Cash", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "cash_and_equivalents", "cashFlowCategory": "operating", "displayOrder": 1112, "subcategory": "Cash and Cash Equivalents", "gaapCategory": "Cash and Cash Equivalents", "ifrsCategory": "Cash and Cash Equivalents", "description": "Primary business cash account", "isActive": true}, {"accountNumber": "1114", "name": "Escrow Accounts", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1110", "level": 3, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "escrow", "cashFlowCategory": "operating", "displayOrder": 1114, "subcategory": "Escrow", "gaapCategory": "Other Current Assets", "ifrsCategory": "Other Current Assets", "description": "Funds held in escrow for property transactions", "isActive": true}, {"accountNumber": "1120", "name": "Accounts Receivable", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "accounts_receivable", "cashFlowCategory": "operating", "displayOrder": 1120, "subcategory": "Accounts Receivable", "gaapCategory": "Accounts Receivable", "ifrsCategory": "Trade and Other Receivables", "description": "Money owed by tenants or clients", "isActive": true}, {"accountNumber": "1130", "name": "Security Deposits Held", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "deposits", "cashFlowCategory": "operating", "displayOrder": 1130, "subcategory": "Deposits", "gaapCategory": "Other Current Assets", "ifrsCategory": "Other Current Assets", "description": "Security deposits held from tenants", "isActive": true}, {"accountNumber": "1200", "name": "Prepaid Expenses", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1100", "level": 2, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "prepaid_expenses", "cashFlowCategory": "operating", "displayOrder": 1200, "subcategory": "Prepaid Expenses", "gaapCategory": "Prepaid Expenses and Other Current Assets", "ifrsCategory": "Other Current Assets", "description": "Expenses paid in advance", "isActive": true}, {"accountNumber": "1300", "name": "Property Held for Sale", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1000", "level": 1, "classification": "current", "financialStatementSection": "current_assets", "accountGroup": "inventory", "cashFlowCategory": "operating", "displayOrder": 1300, "subcategory": "Inventory", "gaapCategory": "Inventory", "ifrsCategory": "Inventories", "description": "Properties available for sale", "isActive": true}, {"accountNumber": "1500", "name": "Fixed Assets", "type": "asset", "normalBalance": "debit", "isHeader": true, "parentAccountNumber": "1000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1500, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Long-term tangible assets", "isActive": true}, {"accountNumber": "1510", "name": "Buildings & Improvements", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1510, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Buildings, renovations, and improvements", "isActive": true}, {"accountNumber": "1512", "name": "Land", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1512, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Land owned for investment or operations", "isActive": true}, {"accountNumber": "1514", "name": "Furniture, Fixtures & Equipment", "type": "asset", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1514, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Office and property equipment", "isActive": true}, {"accountNumber": "1550", "name": "Accumulated Depreciation - Buildings", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1550, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Contra-asset for building depreciation", "isActive": true}, {"accountNumber": "1552", "name": "Accumulated Depreciation - Equipment", "type": "asset", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "1500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_assets", "accountGroup": "property_plant_equipment", "cashFlowCategory": "investing", "displayOrder": 1552, "subcategory": "Fixed Assets", "gaapCategory": "Property, Plant, and Equipment", "ifrsCategory": "Property, Plant and Equipment", "description": "Contra-asset for equipment depreciation", "isActive": true}, {"accountNumber": "2000", "name": "Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2000, "subcategory": "Main Category", "gaapCategory": "Liabilities", "ifrsCategory": "Liabilities", "description": "Main liability category header", "isActive": true}, {"accountNumber": "2100", "name": "Current Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2100, "subcategory": "Current Liabilities", "gaapCategory": "Current Liabilities", "ifrsCategory": "Current Liabilities", "description": "Liabilities due within one year", "isActive": true}, {"accountNumber": "2105", "name": "Payroll Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2105, "subcategory": "Payroll", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Accrued Liabilities", "description": "Accrued but unpaid payroll", "isActive": true}, {"accountNumber": "2110", "name": "Accounts Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accounts_payable", "cashFlowCategory": "operating", "displayOrder": 2110, "subcategory": "Accounts Payable", "gaapCategory": "Accounts Payable", "ifrsCategory": "Trade and Other Payables", "description": "Money owed to suppliers or contractors", "isActive": true}, {"accountNumber": "2115", "name": "Payroll Taxes Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2115, "subcategory": "Payroll", "gaapCategory": "Payroll Taxes Payable", "ifrsCategory": "Payroll Taxes Payable", "description": "Payroll taxes withheld but not yet remitted", "isActive": true}, {"accountNumber": "2120", "name": "Security Deposits Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "deposits", "cashFlowCategory": "operating", "displayOrder": 2120, "subcategory": "Deposits", "gaapCategory": "Other Current Liabilities", "ifrsCategory": "Other Current Liabilities", "description": "Security deposits owed to tenants", "isActive": true}, {"accountNumber": "2125", "name": "Employee Benefits Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "payroll_liabilities", "cashFlowCategory": "operating", "displayOrder": 2125, "subcategory": "Payroll", "gaapCategory": "Benefits Payable", "ifrsCategory": "Benefits Payable", "description": "Employee benefits withheld but not yet remitted", "isActive": true}, {"accountNumber": "2130", "name": "Accrued Expenses", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "accrued_liabilities", "cashFlowCategory": "operating", "displayOrder": 2130, "subcategory": "Accrued Expenses", "gaapCategory": "Accrued Liabilities", "ifrsCategory": "Accrued Liabilities", "description": "Expenses incurred but not yet paid", "isActive": true}, {"accountNumber": "2140", "name": "Unearned Revenue", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2100", "level": 2, "classification": "current", "financialStatementSection": "current_liabilities", "accountGroup": "unearned_revenue", "cashFlowCategory": "operating", "displayOrder": 2140, "subcategory": "Unearned Revenue", "gaapCategory": "Unearned Revenue", "ifrsCategory": "Contract Liabilities", "description": "Payments received for rent or services not yet earned", "isActive": true}, {"accountNumber": "2500", "name": "Long-Term Liabilities", "type": "liability", "normalBalance": "credit", "isHeader": true, "parentAccountNumber": "2000", "level": 1, "classification": "non_current", "financialStatementSection": "non_current_liabilities", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 2500, "subcategory": "Long-Term Liabilities", "gaapCategory": "<PERSON>-<PERSON><PERSON>", "ifrsCategory": "Non-current Liabilities", "description": "Liabilities due after one year", "isActive": true}, {"accountNumber": "2510", "name": "Mortgage Payable", "type": "liability", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "2500", "level": 2, "classification": "non_current", "financialStatementSection": "non_current_liabilities", "accountGroup": "long_term_debt", "cashFlowCategory": "financing", "displayOrder": 2510, "subcategory": "Loans", "gaapCategory": "Notes Payable", "ifrsCategory": "Borrowings", "description": "Long-term mortgages owed", "isActive": true}, {"accountNumber": "3000", "name": "Equity", "type": "equity", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "n_a", "financialStatementSection": "equity", "accountGroup": "other", "cashFlowCategory": "n_a", "displayOrder": 3000, "subcategory": "Main Category", "gaapCategory": "Equity", "ifrsCategory": "Equity", "description": "Main equity category header", "isActive": true}, {"accountNumber": "3100", "name": "Owner's Equity", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "equity", "financialStatementSection": "equity", "accountGroup": "equity", "cashFlowCategory": "financing", "displayOrder": 3100, "subcategory": "Ownership", "gaapCategory": "Contributed Capital", "ifrsCategory": "Issued Capital and Reserves", "description": "Owner's contributions and distributions", "isActive": true}, {"accountNumber": "3200", "name": "Retained Earnings", "type": "equity", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "3000", "level": 1, "classification": "equity", "financialStatementSection": "equity", "accountGroup": "retained_earnings", "cashFlowCategory": "n_a", "displayOrder": 3200, "subcategory": "Earnings", "gaapCategory": "Retained Earnings", "ifrsCategory": "Retained Earnings", "description": "Cumulative net income/loss", "isActive": true}, {"accountNumber": "4000", "name": "Revenue", "type": "revenue", "normalBalance": "credit", "isHeader": true, "level": 0, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "other", "cashFlowCategory": "operating", "displayOrder": 4000, "subcategory": "Main Category", "gaapCategory": "Revenue", "ifrsCategory": "Revenue", "description": "Main revenue category header", "isActive": true}, {"accountNumber": "4100", "name": "Rental Income", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "rental_income", "cashFlowCategory": "operating", "displayOrder": 4100, "subcategory": "Rental Income", "gaapCategory": "Rental Income", "ifrsCategory": "Revenue", "description": "Income from property rentals", "isActive": true}, {"accountNumber": "4200", "name": "Property Sales Income", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "property_sales", "cashFlowCategory": "operating", "displayOrder": 4200, "subcategory": "Property Sales", "gaapCategory": "Sales Revenue", "ifrsCategory": "Revenue", "description": "Income from property sales", "isActive": true}, {"accountNumber": "4300", "name": "Management Fees", "type": "revenue", "normalBalance": "credit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "revenue", "financialStatementSection": "operating_revenue", "accountGroup": "service_revenue", "cashFlowCategory": "operating", "displayOrder": 4300, "subcategory": "Management Fees", "gaapCategory": "Service Revenue", "ifrsCategory": "Revenue", "description": "Fees for property management services", "isActive": true}, {"accountNumber": "4500", "name": "Rent Concessions & Sales Returns", "type": "revenue", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "4000", "level": 1, "classification": "contra_revenue", "financialStatementSection": "operating_revenue", "accountGroup": "rental_income", "cashFlowCategory": "operating", "displayOrder": 4500, "subcategory": "Adjustments", "gaapCategory": "Sales Returns and Allowances", "ifrsCategory": "Revenue", "description": "Contra-revenue for rent concessions and property sales returns", "isActive": true}, {"accountNumber": "5000", "name": "Cost of Goods Sold", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5000, "subcategory": "Main Category", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Direct costs of property sales and management", "isActive": true}, {"accountNumber": "5100", "name": "Property Sale Costs", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5000", "level": 1, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5100, "subcategory": "Direct Costs", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Direct costs of selling properties (commissions, closing costs)", "isActive": true}, {"accountNumber": "5200", "name": "Property Management Payroll", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5000", "level": 1, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5200, "subcategory": "Direct Costs", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Payroll for property management staff", "isActive": true}, {"accountNumber": "5300", "name": "Repairs and Maintenance", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "5000", "level": 1, "classification": "cost_of_sales", "financialStatementSection": "cost_of_sales", "accountGroup": "cost_of_sales", "cashFlowCategory": "operating", "displayOrder": 5300, "subcategory": "Direct Costs", "gaapCategory": "Cost of Goods Sold", "ifrsCategory": "Cost of Sales", "description": "Repairs and maintenance for properties", "isActive": true}, {"accountNumber": "6000", "name": "Operating Expenses", "type": "expense", "normalBalance": "debit", "isHeader": true, "level": 0, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "other", "cashFlowCategory": "operating", "displayOrder": 6000, "subcategory": "Main Category", "gaapCategory": "Operating Expenses", "ifrsCategory": "Operating Expenses", "description": "Expenses incurred in day-to-day business operations", "isActive": true}, {"accountNumber": "6100", "name": "Salaries and Wages", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "personnel_expenses", "cashFlowCategory": "operating", "displayOrder": 6100, "subcategory": "Payroll", "gaapCategory": "Salaries and Wages Expense", "ifrsCategory": "Employee Benefits Expense", "description": "Employee salaries (non-property management)", "isActive": true}, {"accountNumber": "6200", "name": "Marketing and Advertising", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "marketing_and_advertising", "cashFlowCategory": "operating", "displayOrder": 6200, "subcategory": "Marketing", "gaapCategory": "Advertising Expense", "ifrsCategory": "Advertising and Promotion Expense", "description": "Costs for marketing and ads", "isActive": true}, {"accountNumber": "6300", "name": "Utilities", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "utilities", "cashFlowCategory": "operating", "displayOrder": 6300, "subcategory": "Utilities", "gaapCategory": "Utilities Expense", "ifrsCategory": "Rental Expense", "description": "Utilities for properties and offices", "isActive": true}, {"accountNumber": "6400", "name": "Property Taxes", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "taxes", "cashFlowCategory": "operating", "displayOrder": 6400, "subcategory": "Taxes", "gaapCategory": "Property Taxes", "ifrsCategory": "Other Operating Expenses", "description": "Property taxes paid", "isActive": true}, {"accountNumber": "6500", "name": "Insurance Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "insurance_expenses", "cashFlowCategory": "operating", "displayOrder": 6500, "subcategory": "Insurance", "gaapCategory": "Insurance Expense", "ifrsCategory": "Insurance Expense", "description": "Insurance premiums and related costs", "isActive": true}, {"accountNumber": "6600", "name": "Legal and Professional Fees", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "operating_expenses", "accountGroup": "professional_fees", "cashFlowCategory": "operating", "displayOrder": 6600, "subcategory": "Professional Fees", "gaapCategory": "Professional Fees", "ifrsCategory": "Professional Fees", "description": "Legal, consulting, and other professional services", "isActive": true}, {"accountNumber": "6700", "name": "Depreciation Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "non_current_assets", "accountGroup": "depreciation_amortization", "cashFlowCategory": "operating", "displayOrder": 6700, "subcategory": "Depreciation", "gaapCategory": "Depreciation Expense", "ifrsCategory": "Depreciation and Amortisation Expense", "description": "Depreciation expense for buildings and equipment", "isActive": true}, {"accountNumber": "6800", "name": "Amortization Expense", "type": "expense", "normalBalance": "debit", "isHeader": false, "parentAccountNumber": "6000", "level": 1, "classification": "operating", "financialStatementSection": "non_current_assets", "accountGroup": "depreciation_amortization", "cashFlowCategory": "operating", "displayOrder": 6800, "subcategory": "Amortization", "gaapCategory": "Amortization Expense", "ifrsCategory": "Depreciation and Amortisation Expense", "description": "Amortization of intangible assets (e.g., software, licenses)", "isActive": true}]