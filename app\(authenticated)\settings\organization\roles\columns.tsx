"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export type TeamMember = {
    id: string;
    email: string | null;
    name: string | null;
    image: string | null;
    role: "owner" | "admin" | "manager" | "editor" | "viewer" | "member" | "auditor";
    status: "active" | "pending" | "inactive" | "expired" | "declined" | "removed";
};

const roles = ["owner", "admin", "manager", "editor", "viewer", "member"];

export const getColumns = ({ onRoleChange, currentUserRole }: { onRoleChange: (userId: string, role: string) => void; currentUserRole?: string }): ColumnDef<TeamMember>[] => [
    {
        accessorKey: "name",
        header: "Member",
        cell: ({ row }) => {
            const member = row.original;
            return (
                <div className="flex items-center gap-3">
                    <Avatar>
                        <AvatarImage src={member.image || ''} alt={member.name || ''} />
                        <AvatarFallback>{member.name?.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-muted-foreground">{member.email}</div>
                    </div>
                </div>
            )
        }
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => <Badge variant={row.original.status === 'active' ? 'default' : 'secondary'}>{row.original.status}</Badge>,
    },
    {
        accessorKey: "role",
        header: "Role",
        cell: ({ row }) => {
            const member = row.original;

            const canEdit = currentUserRole === 'owner' || (currentUserRole === 'admin' && member.role !== 'owner');
            
            if (!canEdit) {
                return <Badge variant="outline">{member.role}</Badge>;
            }

            return (
                <Select
                    defaultValue={member.role}
                    onValueChange={(value) => onRoleChange(member.id, value)}
                >
                    <SelectTrigger className="w-36">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        {roles.map(role => (
                            <SelectItem key={role} value={role} disabled={role === 'owner' && currentUserRole !== 'owner'}>
                                {role.charAt(0).toUpperCase() + role.slice(1)}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            )
        }
    },
]; 