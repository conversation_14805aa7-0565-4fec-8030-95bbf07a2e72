"use server";
import { db } from "@/db/drizzle";
import { organizations, organizationMembers } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq } from "drizzle-orm";
import { randomUUID } from "crypto";

const SUPERADMINS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

export async function createOrganizationForUser(targetUserId: string, orgName: string, orgEmail: string) {
  const session = await auth.api.getSession({ headers: await headers() });
  if (!session || !SUPERADMINS.includes(session.user.email)) {
    throw new Error("Unauthorized");
  }

  // Check if user already has an organization (adjust logic as needed, e.g., by membership or email)
  const existingOrgs = await db
    .select()
    .from(organizations);

  if (existingOrgs.length > 0) {
    return {
      message: "User already has an organization",
      organization: existingOrgs[0],
    };
  }

  // Generate a slug from the org name
  const orgSlug = orgName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  const orgId = randomUUID();
  // Create organization with subscription (simulating successful checkout)
  const result = await db.transaction(async (tx) => {
    // Create organization
    const [newOrg] = await tx.insert(organizations)
      .values({
        id: orgId,
        name: orgName,
        slug: orgSlug,
        email: orgEmail,
        subscriptionId: `manual_subscription_${Date.now()}`,
        planType: "professional",
        planStatus: "active",
        monthlyTransactionLimit: 500,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    // Create membership
    await tx.insert(organizationMembers)
      .values({
        id: randomUUID(),
        userId: targetUserId,
        organizationId: orgId,
        role: "owner",
      });

    return newOrg;
  });

  return {
    success: true,
    message: "Organization created successfully",
    organization: result,
  };
} 