"use client";
    
import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { continents, Country, Continent } from "@/lib/country-utils";
import { getFlagEmoji } from "@/lib/utils";

interface CountrySelectorProps {
  value: string; // The selected country code (e.g., "US")
  onChange: (value: string) => void; // Callback with the new country code
  className?: string;
  autoFocus?: boolean; // Whether to auto-focus the search input when opened
}

export function CountrySelector({ value, onChange, className, autoFocus = true }: CountrySelectorProps) {
  const [open, setOpen] = React.useState(false);

  const selectedCountry = React.useMemo(() => {
    for (const continent of continents) {
      const found = continent.countries.find(
        (country: Country) => country.code === value
      );
      if (found) return found;
    }
    return null;
  }, [value]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between h-10", className)}
        >
          <span className="truncate">
            {selectedCountry
              ? `${getFlagEmoji(selectedCountry.code)} ${selectedCountry.name} (${selectedCountry.code})`
              : "Select country"}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-[350px] p-0 z-50"
        onCloseAutoFocus={(e) => e.preventDefault()}
        onWheel={(e) => e.stopPropagation()}
        side="bottom"
        align="start"
        sideOffset={4}
        avoidCollisions={true}
        collisionPadding={20}
      >
        <Command className="h-auto">
          <CommandInput 
            placeholder="Search country..." 
            autoFocus={autoFocus}
            onKeyDown={(e) => {
              // Prevent dialog from closing on Escape
              if (e.key === 'Escape') {
                e.stopPropagation();
                setOpen(false);
              }
            }}
          />
          <CommandList 
            className="max-h-[200px] overflow-y-scroll overscroll-contain"
            style={{ 
              scrollbarWidth: 'thin',
              scrollBehavior: 'smooth'
            }}
            onWheel={(e) => {
              e.stopPropagation();
            }}
            onScroll={(e) => {
              e.stopPropagation();
            }}
          >
            <CommandEmpty>No country found.</CommandEmpty>
            {continents.map((continent: Continent) => (
              <CommandGroup key={continent.code} heading={continent.name}>
                {continent.countries.map((country: Country) => (
                  <CommandItem
                    key={country.code}
                    value={`${country.name} (${country.code})`}
                    onSelect={() => {
                      onChange(country.code);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === country.code ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <span className="mr-2">{getFlagEmoji(country.code)}</span>
                    <span className="flex-1">{country.name}</span>
                    <span className="text-muted-foreground ml-2">
                      {country.code}
                    </span>
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}