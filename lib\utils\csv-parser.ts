import <PERSON> from 'papa<PERSON><PERSON>';
import { z } from 'zod';

// CSV row schema for validation
const csvAccountSchema = z.object({
  'Account Number': z.string().optional(),
  'Account Name': z.string().min(1, 'Account name is required'),
  'Account Type': z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']),
  'Normal Balance': z.enum(['debit', 'credit']),
  'Is Header': z.string().optional().transform(val => val?.toLowerCase() === 'true'),
  'Parent Account Number': z.string().optional(),
  'Description': z.string().optional(),
  'Is Active': z.string().optional().transform(val => val?.toLowerCase() !== 'false'),
});

export type CSVAccountRow = z.infer<typeof csvAccountSchema>;

export interface ParsedAccount {
  accountNumber?: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  normalBalance: 'debit' | 'credit';
  isHeader: boolean;
  parentAccountNumber?: string;
  description?: string;
  isActive: boolean;
  // Enhanced professional accounting fields
  level?: number;
  classification?: string;
  financialStatementSection?: string;
  accountGroup?: string;
  cashFlowCategory?: string;
  displayOrder?: number;
  subcategory?: string;
  gaapCategory?: string;
  ifrsCategory?: string;
  // Enhanced validation fields
  validationStatus: 'valid' | 'warning' | 'error';
  errors: string[];
  warnings: string[];
  rowNumber: number;
  selected: boolean; // For selective import
}

export interface CSVParseResult {
  success: boolean;
  data: ParsedAccount[];
  errors: string[];
  warnings: string[];
  // Enhanced statistics
  totalAccounts: number;
  validAccounts: number;
  accountsWithWarnings: number;
  accountsWithErrors: number;
}

export function parseAccountsCSV(file: File): Promise<CSVParseResult> {
  return new Promise((resolve) => {
    const errors: string[] = [];
    const warnings: string[] = [];
    const validAccounts: ParsedAccount[] = [];

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          errors.push(...results.errors.map(err => `CSV Parse Error: ${err.message}`));
        }

        // Validate required headers
        const requiredHeaders = ['Account Name', 'Account Type', 'Normal Balance'];
        const headers = results.meta.fields || [];
        const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
        
        if (missingHeaders.length > 0) {
          errors.push(`Missing required columns: ${missingHeaders.join(', ')}`);
          resolve({ 
            success: false, 
            data: [], 
            errors, 
            warnings,
            totalAccounts: 0,
            validAccounts: 0,
            accountsWithWarnings: 0,
            accountsWithErrors: 0
          });
          return;
        }

        // Process each row
        results.data.forEach((row: any, index: number) => {
          const rowNumber = index + 2; // +2 because index is 0-based and we skip header
          const accountErrors: string[] = [];
          const accountWarnings: string[] = [];
          let validationStatus: 'valid' | 'warning' | 'error' = 'valid';
          
          try {
            const validated = csvAccountSchema.parse(row);
            
            // Build account object
            const account: ParsedAccount = {
              accountNumber: validated['Account Number']?.trim() || undefined,
              name: validated['Account Name'].trim(),
              type: validated['Account Type'],
              normalBalance: validated['Normal Balance'],
              isHeader: validated['Is Header'] || false,
              parentAccountNumber: validated['Parent Account Number']?.trim() || undefined,
              description: validated['Description']?.trim() || undefined,
              isActive: validated['Is Active'] !== false,
              validationStatus: 'valid',
              errors: [],
              warnings: [],
              rowNumber,
              selected: true, // Default to selected for import
            };

            // Validate normal balance matches account type conventions
            const expectedBalance = getExpectedNormalBalance(account.type);
            if (account.normalBalance !== expectedBalance) {
              const warning = `${account.type} accounts typically have ${expectedBalance} normal balance, but ${account.normalBalance} was specified`;
              accountWarnings.push(warning);
              warnings.push(`Row ${rowNumber}: ${warning} for "${account.name}"`);
            }

            // Validate account number format if provided
            if (account.accountNumber) {
              const numberRange = getAccountNumberRange(account.type);
              const accountNum = parseInt(account.accountNumber);
              if (isNaN(accountNum) || accountNum < numberRange.min || accountNum > numberRange.max) {
                const warning = `Account number ${account.accountNumber} is outside recommended range ${numberRange.min}-${numberRange.max} for ${account.type} accounts`;
                accountWarnings.push(warning);
                warnings.push(`Row ${rowNumber}: ${warning}`);
              }
            }

            // Set validation status
            if (accountWarnings.length > 0) {
              validationStatus = 'warning';
            }

            // Update account with validation results
            account.validationStatus = validationStatus;
            account.errors = accountErrors;
            account.warnings = accountWarnings;

            validAccounts.push(account);
          } catch (validationError) {
            // Create error account entry
            let accountName = 'Unknown';
            try {
              accountName = row['Account Name'] || 'Unknown';
            } catch {}

            if (validationError instanceof z.ZodError) {
              const errorMessages = validationError.errors.map(err => 
                `${err.path.join('.')}: ${err.message}`
              );
              accountErrors.push(...errorMessages);
              errors.push(`Row ${rowNumber}: ${errorMessages.join(', ')}`);
            } else {
              accountErrors.push('Invalid data format');
              errors.push(`Row ${rowNumber}: Invalid data format`);
            }

            // Create error account for display
            const errorAccount: ParsedAccount = {
              name: accountName,
              type: 'asset', // Default type for display
              normalBalance: 'debit', // Default balance for display
              isHeader: false,
              isActive: true,
              validationStatus: 'error',
              errors: accountErrors,
              warnings: [],
              rowNumber,
              selected: false, // Don't select invalid accounts by default
            };

            validAccounts.push(errorAccount);
          }
        });

        // Calculate statistics
        const totalAccounts = validAccounts.length;
        const validAccountsCount = validAccounts.filter(acc => acc.validationStatus === 'valid').length;
        const accountsWithWarnings = validAccounts.filter(acc => acc.validationStatus === 'warning').length;
        const accountsWithErrors = validAccounts.filter(acc => acc.validationStatus === 'error').length;

        resolve({
          success: errors.length === 0,
          data: validAccounts,
          errors,
          warnings,
          totalAccounts,
          validAccounts: validAccountsCount,
          accountsWithWarnings,
          accountsWithErrors
        });
      },
      error: (error) => {
        resolve({
          success: false,
          data: [],
          errors: [`Failed to parse CSV file: ${error.message}`],
          warnings: [],
          totalAccounts: 0,
          validAccounts: 0,
          accountsWithWarnings: 0,
          accountsWithErrors: 0
        });
      }
    });
  });
}

function getExpectedNormalBalance(accountType: string): 'debit' | 'credit' {
  switch (accountType) {
    case 'asset':
    case 'expense':
      return 'debit';
    case 'liability':
    case 'equity':
    case 'revenue':
      return 'credit';
    default:
      return 'debit';
  }
}

function getAccountNumberRange(accountType: string): { min: number; max: number } {
  switch (accountType) {
    case 'asset':
      return { min: 1000, max: 1999 };
    case 'liability':
      return { min: 2000, max: 2999 };
    case 'equity':
      return { min: 3000, max: 3999 };
    case 'revenue':
      return { min: 4000, max: 4999 };
    case 'expense':
      return { min: 5000, max: 5999 };
    default:
      return { min: 1000, max: 9999 };
  }
}

export function generateAccountNumber(accountType: string, existingNumbers: string[]): string {
  const range = getAccountNumberRange(accountType);
  const existingNums = existingNumbers
    .map(num => parseInt(num))
    .filter(num => !isNaN(num) && num >= range.min && num <= range.max)
    .sort((a, b) => a - b);

  // Find the first available number in the range
  for (let num = range.min; num <= range.max; num += 10) {
    if (!existingNums.includes(num)) {
      return num.toString();
    }
  }

  // If all base numbers are taken, find any available number
  for (let num = range.min; num <= range.max; num++) {
    if (!existingNums.includes(num)) {
      return num.toString();
    }
  }

  // Fallback - this shouldn't happen in normal usage
  return (range.max + 1).toString();
}

// Helper functions for selective import
export function getSelectedValidAccounts(accounts: ParsedAccount[]): ParsedAccount[] {
  return accounts.filter(acc => acc.selected && acc.validationStatus !== 'error');
}

export function toggleAccountSelection(accounts: ParsedAccount[], index: number): ParsedAccount[] {
  return accounts.map((acc, i) => 
    i === index ? { ...acc, selected: !acc.selected } : acc
  );
}

export function selectAllValidAccounts(accounts: ParsedAccount[]): ParsedAccount[] {
  return accounts.map(acc => ({
    ...acc,
    selected: acc.validationStatus !== 'error'
  }));
}

export function deselectAllAccounts(accounts: ParsedAccount[]): ParsedAccount[] {
  return accounts.map(acc => ({
    ...acc,
    selected: false
  }));
}

// Export corrected CSV
export function exportAccountsToCSV(accounts: ParsedAccount[], filename: string = 'corrected-accounts.csv') {
  const headers = [
    'Account Number',
    'Account Name', 
    'Account Type',
    'Normal Balance',
    'Is Header',
    'Parent Account Number',
    'Description',
    'Is Active'
  ];

  const csvData = accounts
    .filter(acc => acc.validationStatus !== 'error') // Only export valid/warning accounts
    .map(acc => [
      acc.accountNumber || '',
      acc.name,
      acc.type,
      acc.normalBalance,
      acc.isHeader ? 'true' : 'false',
      acc.parentAccountNumber || '',
      acc.description || '',
      acc.isActive ? 'true' : 'false'
    ]);

  const csvContent = [headers, ...csvData]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

export function downloadCsv(csvString: string, filename: string) {
  const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
} 