import { resetUserPassword } from "@/lib/actions/superadmin";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const { userId, newPassword } = await req.json();
    if (!userId || !newPassword) return new NextResponse(JSON.stringify({ success: false, message: "Missing userId or newPassword" }), { status: 400 });
    const result = await resetUserPassword(userId, newPassword);
    return NextResponse.json(result);
  } catch (error) {
    return new NextResponse(JSON.stringify({ success: false, message: error instanceof Error ? error.message : String(error) }), { status: 500 });
  }
} 