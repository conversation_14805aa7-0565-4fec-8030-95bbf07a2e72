import React from "react";
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subMonths,
} from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type DateRange = {
  from: Date;
  to: Date;
};

type DateRangeSelectorProps = {
  onDateRangeChange: (range: DateRange) => void;
};

const DateRangeSelector = ({ onDateRangeChange }: DateRangeSelectorProps) => {
  const [range, setRange] = React.useState(() => {
    const now = new Date();
    return {
      from: startOfMonth(now),
      to: endOfMonth(now),
    };
  });

  // Set initial range
  React.useEffect(() => {
    onDateRangeChange(range);
  }, []);

  const handleRangeSelect = (value: string) => {
    const now = new Date();
    let newRange: DateRange;

    switch (value) {
      case "current-month":
        newRange = {
          from: startOfMonth(now),
          to: endOfMonth(now),
        };
        break;
      case "last-month":
        const lastMonth = subMonths(now, 1);
        newRange = {
          from: startOfMonth(lastMonth),
          to: endOfMonth(lastMonth),
        };
        break;
      case "current-year":
        newRange = {
          from: startOfYear(now),
          to: endOfYear(now),
        };
        break;
      case "last-year":
        const lastYear = new Date(now.getFullYear() - 1, 0, 1);
        newRange = {
          from: startOfYear(lastYear),
          to: endOfYear(lastYear),
        };
        break;
      default:
        return;
    }

    setRange(newRange);
    onDateRangeChange(newRange);
  };

  return (
    <div className="flex items-center gap-2">
      <Select onValueChange={handleRangeSelect} defaultValue="current-month">
        <SelectTrigger className="w-[140px] bg-black/50 border-white/10 text-white">
          <SelectValue placeholder="Select range" />
        </SelectTrigger>
        <SelectContent className="bg-black/90 border-white/10">
          <SelectItem
            value="current-month"
            className="text-white hover:bg-white/10"
          >
            Current Month
          </SelectItem>
          <SelectItem
            value="last-month"
            className="text-white hover:bg-white/10"
          >
            Last Month
          </SelectItem>
          <SelectItem
            value="current-year"
            className="text-white hover:bg-white/10"
          >
            Current Year
          </SelectItem>
          <SelectItem
            value="last-year"
            className="text-white hover:bg-white/10"
          >
            Last Year
          </SelectItem>
        </SelectContent>
      </Select>

      <Button
        variant="outline"
        className="min-w-[240px] justify-start text-left font-normal bg-black/50 border-white/10 text-white hover:bg-black/60"
      >
        <CalendarIcon className="mr-2 h-4 w-4" />
        {range.from.getMonth() === 0 && range.to.getMonth() === 11
          ? `Jan - Dec ${range.from.getFullYear()}`
          : format(range.from, "MMMM yyyy")}
      </Button>
    </div>
  );
};

export default DateRangeSelector;
