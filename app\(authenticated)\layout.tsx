import { Sidebar } from "@/components/layout/Sidebar";
import { ProtectedHeader } from "@/components/layout/ProtectedHeader";

export default function AuthenticatedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex flex-col flex-1 overflow-hidden" id="main-content">
        <ProtectedHeader />
        <main className="flex-1 p-6 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
