"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, FileText, CheckCircle, Eye, Settings, Star } from "lucide-react";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { useDebouncedCallback } from 'use-debounce';

interface OrganizationData {
  industry?: string;
  name?: string;
  businessType?: string;
  chartOfAccounts?: {
    selectedTemplateId?: string;
  };
}

interface ChartOfAccountsStepProps {
  onNext: (data: { selectedTemplateId: string | null }) => void;
  onPrevious: () => void;
  isLoading: boolean;
  businessType?: 'physical' | 'digital' | 'hybrid' | '';
  industry?: string;
}

interface AccountTemplate {
  id: string;
  templateName: string;
  description: string;
  isDefault: boolean;
  accounts?: any[]; // This will be loaded on demand
  industry?: string;
}

const ultimateTemplate: AccountTemplate = {
    id: 'ultimate',
    templateName: 'Ultimate Chart of Accounts',
    description: 'A comprehensive template for advanced users and service-based businesses.',
    isDefault: false,
};

export function ChartOfAccountsStep({ 
  onNext, 
  onPrevious, 
  isLoading,
  businessType,
  industry
}: ChartOfAccountsStepProps) {
  const { updateData, data: onboardingData } = useOnboardingStore();
  
  const effectiveBusinessType = businessType;
  const effectiveIndustry = industry;

  const [templates, setTemplates] = useState<AccountTemplate[]>([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<AccountTemplate | null>(null);

  // Debounced auto-save for template selection
  const debouncedAutoSave = useDebouncedCallback((templateId: string | null) => {
    if (templateId) {
      const { businessType = '', selectedAt, chartTemplate: prevChartTemplate } = onboardingData.businessDetails || {};
      updateData({ businessDetails: { businessType, selectedAt, chartTemplate: templateId } });
    }
  }, 2000);

  useEffect(() => {
    console.debug('[COA Step] effectiveBusinessType:', effectiveBusinessType);
    console.debug('[COA Step] effectiveIndustry:', effectiveIndustry);
    fetchTemplates();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [effectiveBusinessType, effectiveIndustry]);

  useEffect(() => {
    debouncedAutoSave(selectedTemplateId);
  }, [selectedTemplateId, debouncedAutoSave]);

  const fetchTemplates = async () => {
    const typeToUse = effectiveBusinessType || 'other';
    try {
      setLoadingTemplates(true);
      const { getChartOfAccountsTemplates } = await import("@/lib/actions/onboarding");
      const data = await getChartOfAccountsTemplates(typeToUse, effectiveIndustry);
      console.debug('[COA Step] Raw fetched templates:', data);
      // Separate ultimate template and other templates
      const otherTemplates = data.filter(t => t.id !== 'ultimate');
      // Combine templates with ultimate template at the end
      const templatesWithUltimate = [
        ...otherTemplates,
        ultimateTemplate
      ];
      // If no templates, fallback to generic
      const templatesToShow = templatesWithUltimate.length > 0 ? templatesWithUltimate : data.filter(t => t.id === 'generic');
      console.debug('[COA Step] templatesWithUltimate:', templatesWithUltimate);
      console.debug('[COA Step] templatesToShow:', templatesToShow);
      setTemplates(templatesToShow);
      if (templatesToShow.length > 0) {
        let preselect: AccountTemplate | undefined;
        if (effectiveIndustry) {
            preselect = templatesToShow.find((t: AccountTemplate) => 
              t.industry && t.industry.toLowerCase() === effectiveIndustry.toLowerCase()
            );
            console.debug('[COA Step] Preselect by industry:', preselect);
        }
        if (!preselect) {
            preselect = templatesToShow.find((t: AccountTemplate) => t.isDefault);
            console.debug('[COA Step] Preselect by default:', preselect);
        }
        if (!selectedTemplateId) {
            setSelectedTemplateId(preselect?.id ?? null);
        }
      }
    } catch (error) {
      console.error('[COA Step] Error fetching templates:', error);
    } finally {
      setLoadingTemplates(false);
    }
  };

  const handleNext = () => {
    if (selectedTemplateId) {
      const { businessType = '', selectedAt, chartTemplate: prevChartTemplate } = onboardingData.businessDetails || {};
      updateData({ businessDetails: { businessType, selectedAt, chartTemplate: selectedTemplateId } });
      onNext({ selectedTemplateId });
    }
  };

  const handlePreview = async (template: AccountTemplate) => {
    if (!template.accounts) {
        setLoadingTemplates(true);
        try {
            const { getTemplateAccounts } = await import("@/lib/data/coaTemplates");
            const accounts = await getTemplateAccounts(template.id);
            const updatedTemplate = { ...template, accounts: accounts || [] };
            if(template.id !== 'ultimate') {
                setTemplates(prev => prev.map(t => t.id === template.id ? updatedTemplate : t));
            }
            setPreviewTemplate(updatedTemplate);
        } catch (error) {
            console.error("Error fetching template accounts for preview:", error);
        } finally {
            setLoadingTemplates(false);
        }
    } else {
        setPreviewTemplate(template);
    }
    setShowPreview(true);
  };

  const getBusinessTypeTitle = () => {
    switch (effectiveBusinessType) {
      case 'physical': return 'Physical Business';
      case 'digital': return 'Digital Business';
      case 'hybrid': return 'Hybrid Business';
      default: return 'Business';
    }
  };

  if (showPreview && previewTemplate) {
    return (
      <div className="max-w-6xl mx-auto space-y-4 sm:space-y-6 px-4 sm:px-6">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
          <Button
            variant="outline"
            onClick={() => setShowPreview(false)}
            className="flex items-center space-x-2 border-border text-muted-foreground hover:bg-muted text-sm"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Templates</span>
          </Button>
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-white">{previewTemplate.templateName}</h2>
            <p className="text-sm text-gray-300">{previewTemplate.description}</p>
          </div>
        </div>

        <Card className="bg-card border-border">
          <CardHeader className="pb-3 sm:pb-4">
            <CardTitle className="text-lg sm:text-xl text-white">Account Preview</CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              {/* These accounts will be created for your organization */}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-80 sm:h-96">
              <div className="space-y-2 sm:space-y-3">
                {previewTemplate.accounts ? previewTemplate.accounts.map((account: any) => (
                  <div key={account.accountNumber} className="flex items-center justify-between p-2.5 sm:p-3 border border-border bg-muted/50 rounded-lg">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 sm:space-x-3">
                        <span className="font-mono text-xs sm:text-sm text-muted-foreground flex-shrink-0">
                          {account.accountNumber}
                        </span>
                        <span className="font-medium text-sm sm:text-base text-white truncate">{account.name}</span>
                        <Badge variant="outline" className="text-xs border-border text-muted-foreground flex-shrink-0">
                          {account.type}
                        </Badge>
                      </div>
                      {account.description && (
                        <p className="text-xs sm:text-sm text-muted-foreground mt-1 truncate">{account.description}</p>
                      )}
                    </div>
                  </div>
                )) : <div className="text-white text-center p-4">Loading preview...</div>}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
          <Button
            variant="outline"
            onClick={() => setShowPreview(false)}
            className="border-border text-muted-foreground hover:bg-muted order-2 sm:order-1"
          >
            Choose Different Template
          </Button>
          <Button
            onClick={() => {
              setSelectedTemplateId(previewTemplate.id);
              setShowPreview(false);
            }}
            className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 order-1 sm:order-2"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Use This Template</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-2 px-2 py-4">
      {/* Header */}
      <div className="text-center mb-2">
        <h2 className="text-xl font-bold text-white mb-1">Set up your Chart of Accounts</h2>
        <p className="text-xs text-muted-foreground max-w-xl mx-auto">
          We've prepared specialized account templates for your {getBusinessTypeTitle().toLowerCase()}. Choose a template, or select our comprehensive professional option.
        </p>
      </div>

      {/* Loading State */}
      {loadingTemplates && (
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="bg-gray-800/50 border-gray-600">
              <CardContent className="p-2">
                <div className="flex items-center space-x-2">
                  <Skeleton className="w-8 h-8 rounded-lg bg-gray-700" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-2 w-24 bg-gray-700" />
                    <Skeleton className="h-2 w-40 bg-gray-700" />
                  </div>
                  <Skeleton className="w-12 h-5 bg-gray-700" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Templates */}
      {!loadingTemplates && templates.length === 0 && (
        <div className="text-center text-red-400 py-8">
          No chart of accounts templates found. Please check your business type or try again.
        </div>
      )}
      {!loadingTemplates && templates.length > 0 && (
        <div className="space-y-2">
          {templates.map((template) => (
            <Card
              key={template.templateName}
              className={`transition-all duration-200 cursor-pointer border-2 ${
                selectedTemplateId === template.id 
                  ? (template.id === 'ultimate' 
                      ? "border-amber-500 bg-amber-900/20" 
                      : "border-blue-500 bg-blue-900/20")
                  : "border-gray-700 bg-gray-800/50"
              }`}
              onClick={() => setSelectedTemplateId(template.id)}
            >
              <CardContent className="flex items-center space-x-2 p-2">
                <div className={`flex-shrink-0 w-7 h-7 rounded-lg flex items-center justify-center ${
                  template.id === 'ultimate' 
                    ? "bg-amber-700/50" 
                    : "bg-gray-700"
                }`}>
                  {template.id === 'ultimate' 
                    ? <Star className="w-4 h-4 text-amber-300" /> 
                    : <FileText className="w-4 h-4 text-white" />
                  }
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-1">
                    <span className="font-semibold text-sm text-white truncate">{template.templateName}</span>
                    {template.id === 'ultimate' && (
                      <Badge variant="secondary" className="bg-amber-500 text-white text-xs px-1 py-0.5">Advanced</Badge>
                    )}
                    {template.isDefault && template.id !== 'ultimate' && (
                      <Badge variant="secondary" className="bg-blue-500 text-white text-xs px-1 py-0.5">Default</Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground truncate">{template.description}</p>
                </div>
                <div className="flex flex-col items-end gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-border text-muted-foreground hover:bg-muted px-2 py-1 h-6"
                    onClick={e => { e.stopPropagation(); handlePreview(template); }}
                  >
                    <Eye className="w-3 h-3 mr-1" /> Preview
                  </Button>
                  {selectedTemplateId === template.id && (
                    <CheckCircle className={`w-4 h-4 ${
                      template.id === 'ultimate' 
                        ? "text-amber-500" 
                        : "text-blue-500"
                    }`} />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Info Box */}
      <div className="mt-2">
        <div className="rounded bg-blue-900/80 p-2 text-blue-100 text-xs">
          <div className="font-semibold mb-1 flex items-center gap-1">
            <Settings className="w-3 h-3 inline-block mr-1" /> Don't worry about getting it perfect
          </div>
          <div>
            You can always customize your chart of accounts later from the accounting page.
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      <div className="flex justify-between mt-4">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
          className="border-border text-muted-foreground hover:bg-muted"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={isLoading || !selectedTemplateId}
          className="flex items-center justify-center space-x-2 order-1 sm:order-2"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}