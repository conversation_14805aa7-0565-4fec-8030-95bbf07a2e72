"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { products, accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { ProductBusinessRules } from "./product.rules";
import { randomUUID } from 'crypto';

const productFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
  price: z.number().positive("Price must be a positive number").optional(),
  productType: z.enum([
    "service",
    "digital_service",
    "physical_product",
    "digital_product",
    "subscription",
    "bundle",
  ]),
  revenueAccountId: z.string().min(1, "Revenue account is required"),
  cogsAccountId: z.string().optional(),
  inventoryAccountId: z.string().optional(),
  accountsToCreate: z
    .array(
      z.object({
        accountName: z.string(),
        accountCode: z.string(),
        accountType: z.enum(["revenue", "expense", "asset"]),
      })
    )
    .optional(),
  costBasis: z.number().nonnegative("Cost must be a positive number").optional(),
});

export type NewProduct = z.infer<typeof productFormSchema>;

type ActionResponse = Promise<{
  success: boolean;
  message: string;
  data?: any;
}>;

export async function createProduct(productData: NewProduct): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const validatedFields = productFormSchema.safeParse(productData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid product data provided." };
    }

    const businessRules = new ProductBusinessRules();
    const validation = businessRules.validateProductSetup(validatedFields.data as any);

    if (!validation.isValid) {
      return { success: false, message: validation.errors.join('\n') };
    }

    const { accountsToCreate, ...productDetails } = validatedFields.data;

    // Create any new accounts if they were approved by the user
    if (accountsToCreate && accountsToCreate.length > 0) {
      const newAccountValues = accountsToCreate.map(acc => {
        const normalBalance: 'debit' | 'credit' = (acc.accountType === 'asset' || acc.accountType === 'expense') ? 'debit' : 'credit';
        return {
          id: randomUUID(),
          name: acc.accountName,
          accountNumber: acc.accountCode,
          type: acc.accountType,
          normalBalance: normalBalance,
          organizationId: orgId,
        };
      });
      await db.insert(accounts).values(newAccountValues);
    }
    
    const [newProduct] = await db
      .insert(products)
      .values({
        id: randomUUID(),
        ...productDetails,
        price: (productDetails.price ?? 0).toString(),
        costBasis: (productDetails.costBasis ?? 0).toString(),
        organizationId: orgId,
      })
      .returning();

    revalidatePath("/products");
    return { success: true, message: "Product created successfully.", data: newProduct };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, message: error.errors[0].message };
    }
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to create product.",
    };
  }
} 