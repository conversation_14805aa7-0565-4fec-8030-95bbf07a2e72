"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { NewVendor, vendorFormSchema } from "@/lib/actions/vendors/vendor.schema";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function updateVendor(vendorId: string, vendorData: NewVendor): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const validatedFields = vendorFormSchema.safeParse(vendorData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid vendor data provided." };
    }

    const [updatedVendor] = await db
      .update(vendors)
      .set(validatedFields.data)
      .where(
        and(eq(vendors.id, vendorId), eq(vendors.organizationId, orgId))
      )
      .returning();

    if (!updatedVendor) {
      return { success: false, message: "Vendor not found or you do not have permission to edit it." };
    }

    revalidatePath("/vendors");
    return { success: true, message: "Vendor updated successfully." };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to update vendor.",
    };
  }
} 