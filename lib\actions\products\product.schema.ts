import { z } from "zod";
export const productFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().optional(),
  price: z.coerce.number().positive("Price must be a positive number."),
  type: z.enum(["service", "digital_service", "physical_product", "digital_product", "subscription", "bundle"]),
  revenueAccountId: z.string().min(1, "A revenue account is required."),
  cogsAccountId: z.string().optional(),
  inventoryAccountId: z.string().optional(),
});

export type NewProduct = z.infer<typeof productFormSchema>;

export type FormState = {
  success: boolean;
  message: string;
}; 