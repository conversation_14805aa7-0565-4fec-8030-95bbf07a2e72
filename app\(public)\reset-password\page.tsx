'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { authClient } from '@/lib/auth-client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Logo } from '@/components/general/logo'
import Link from 'next/link'
import { z } from 'zod'
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react'

// Define the validation schema
const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/^(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
    .regex(/^(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
    .regex(/^(?=.*\d)/, 'Password must contain at least one number'),
  confirmPassword: z
    .string()
    .min(1, 'Please confirm your password'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

export default function ResetPasswordPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  
  const [isLoading, setIsLoading] = useState(false)
  const [isValidatingToken, setIsValidatingToken] = useState(true)
  const [tokenValid, setTokenValid] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState<ResetPasswordFormData>({
    password: '',
    confirmPassword: '',
  })
  const [fieldErrors, setFieldErrors] = useState<Partial<Record<keyof ResetPasswordFormData, string>>>({})

  // Validate token on component mount
  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setError('Invalid or missing reset token')
        setIsValidatingToken(false)
        return
      }

      try {
        // You can add a token validation endpoint here if your auth system supports it
        // For now, we'll just check if the token exists and has a valid format
        if (token.length < 10) {
          throw new Error('Invalid token format')
        }
        
        setTokenValid(true)
      } catch (err) {
        setError('Invalid or expired reset token. Please request a new password reset.')
      } finally {
        setIsValidatingToken(false)
      }
    }

    validateToken()
  }, [token])

  const validateField = (field: keyof ResetPasswordFormData, value: string) => {
    try {
      if (field === 'confirmPassword') {
        // For confirmPassword, we need to validate the entire object
        const testData = { ...formData, [field]: value }
        resetPasswordSchema.parse(testData)
      } else {
        resetPasswordSchema.shape[field].parse(value)
      }
      setFieldErrors(prev => ({ ...prev, [field]: undefined }))
    } catch (err) {
      if (err instanceof z.ZodError) {
        const fieldError = err.errors.find(e => e.path.includes(field))
        setFieldErrors(prev => ({ ...prev, [field]: fieldError?.message }))
      }
    }
  }

  const handleInputChange = (field: keyof ResetPasswordFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear any existing error when user starts typing
    if (error) setError(null)
    validateField(field, value)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Validate the entire form
      const validatedData = resetPasswordSchema.parse(formData)
      
      await authClient.resetPassword({
        token: token!,
        newPassword: validatedData.password
      })
      
      setSuccess('Password reset successfully! Redirecting to sign in...')
      // Redirect to sign in page after a short delay
      setTimeout(() => {
        router.push('/signin?reset=success')
      }, 2000)
    } catch (err) {
      if (err instanceof z.ZodError) {
        // Handle validation errors
        const errors: Partial<Record<keyof ResetPasswordFormData, string>> = {}
        err.errors.forEach(error => {
          const field = error.path[0] as keyof ResetPasswordFormData
          errors[field] = error.message
        })
        setFieldErrors(errors)
        setError('Please fix the errors above and try again.')
      } else {
        // Handle authentication errors
        const errorMessage = getAuthErrorMessage(err)
        setError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to get user-friendly error messages
  const getAuthErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error
    
    // Handle different error types
    if (error?.message) {
      const message = error.message.toLowerCase()
      
      if (message.includes('invalid token') || message.includes('expired token')) {
        return 'The reset link is invalid or has expired. Please request a new password reset.'
      }
      if (message.includes('token not found')) {
        return 'Reset token not found. Please request a new password reset.'
      }
      if (message.includes('password too weak') || message.includes('password requirements')) {
        return 'Password does not meet security requirements. Please choose a stronger password.'
      }
      if (message.includes('network') || message.includes('connection')) {
        return 'Network error. Please check your internet connection and try again.'
      }
      if (message.includes('timeout')) {
        return 'Request timed out. Please try again.'
      }
    }
    
    return 'An unexpected error occurred. Please try again.'
  }

  // Password strength indicator
  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, color: 'bg-gray-200', text: '', width: 0, textColor: 'text-gray-500' }
    
    let strength = 0
    if (password.length >= 8) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/\d/.test(password)) strength++
    
    const strengthMap = {
      0: { color: 'bg-gray-200', text: '', width: 0, textColor: 'text-gray-500' },
      1: { color: 'bg-red-500', text: 'Very Weak', width: 25, textColor: 'text-red-600' },
      2: { color: 'bg-orange-500', text: 'Weak', width: 50, textColor: 'text-orange-600' },
      3: { color: 'bg-yellow-500', text: 'Fair', width: 75, textColor: 'text-yellow-600' },
      4: { color: 'bg-green-500', text: 'Strong', width: 100, textColor: 'text-green-600' }
    }
    
    return strengthMap[strength as keyof typeof strengthMap]
  }

  const passwordStrength = getPasswordStrength(formData.password)

  // Show loading state while validating token
  if (isValidatingToken) {
    return (
      <section className="flex min-h-screen bg-zinc-50 px-4 py-16 md:py-32 dark:bg-transparent">
        <div className="bg-card m-auto h-fit w-full max-w-sm rounded-[calc(var(--radius)+.125rem)] border p-0.5 shadow-md dark:[--color-muted:var(--color-zinc-900)]">
          <div className="p-8 pb-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-sm text-muted-foreground">Validating reset link...</p>
          </div>
        </div>
      </section>
    )
  }

  // Show error if token is invalid
  if (!tokenValid) {
    return (
      <section className="flex min-h-screen bg-zinc-50 px-4 py-16 md:py-32 dark:bg-transparent">
        <div className="bg-card m-auto h-fit w-full max-w-sm rounded-[calc(var(--radius)+.125rem)] border p-0.5 shadow-md dark:[--color-muted:var(--color-zinc-900)]">
          <div className="p-8 pb-6">
            <div>
              <Link href="/" aria-label="go home">
                <Logo />
              </Link>
              <h1 className="mb-1 mt-4 text-xl font-semibold">Invalid Reset Link</h1>
              <p className="text-sm text-muted-foreground">
                The password reset link is invalid or has expired.
              </p>
            </div>

            {error && (
              <div className="mt-6 flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-400">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
              </div>
            )}

            <div className="mt-6 space-y-4">
              <Button asChild className="w-full">
                <Link href="/forgot-password">Request New Reset Link</Link>
              </Button>
              <Button asChild variant="outline" className="w-full">
                <Link href="/signin">Back to Sign In</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="flex min-h-screen bg-zinc-50 px-4 py-16 md:py-32 dark:bg-transparent">
      <div className="bg-card m-auto h-fit w-full max-w-sm rounded-[calc(var(--radius)+.125rem)] border p-0.5 shadow-md dark:[--color-muted:var(--color-zinc-900)]">
        <div className="p-8 pb-6">
          <div>
            <Link href="/" aria-label="go home">
              <Logo />
            </Link>
            <h1 className="mb-1 mt-4 text-xl font-semibold">Set New Password</h1>
            <p className="text-sm text-muted-foreground">
              Create a strong password for your account.
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mt-6 flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-400">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mt-6 flex items-center gap-2 rounded-md border border-green-200 bg-green-50 p-3 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
              <CheckCircle className="h-4 w-4 flex-shrink-0" />
              <span>{success}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="mt-6 space-y-6">
            <div className="space-y-2">
              <Label htmlFor="password" className="block text-sm">
                New Password
              </Label>
              <div className="relative">
                <Input 
                  type={showPassword ? 'text' : 'password'}
                  id="password" 
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  disabled={isLoading}
                  className={`pr-10 ${fieldErrors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                  placeholder="Create a strong password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {/* Password Strength Indicator */}
              {formData.password && (
                <div className="space-y-2">
                  <div className="flex h-2 w-full overflow-hidden rounded-full bg-gray-200">
                    <div 
                      className={`h-full transition-all duration-300 ${passwordStrength.color}`}
                      style={{ width: `${passwordStrength.width}%` }}
                    />
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className={`font-medium transition-colors duration-300 ${passwordStrength.textColor}`}>
                      {passwordStrength.text}
                    </span>
                    <span className="text-gray-500">
                      {passwordStrength.strength}/4 criteria met
                    </span>
                  </div>
                </div>
              )}
              
              {fieldErrors.password && (
                <p className="flex items-center gap-1 text-sm text-red-500">
                  <AlertCircle className="h-3 w-3" />
                  {fieldErrors.password}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="block text-sm">
                Confirm Password
              </Label>
              <div className="relative">
                <Input 
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword" 
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  disabled={isLoading}
                  className={`pr-10 ${fieldErrors.confirmPassword ? 'border-red-500 focus:border-red-500' : ''}`}
                  placeholder="Confirm your password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {fieldErrors.confirmPassword && (
                <p className="flex items-center gap-1 text-sm text-red-500">
                  <AlertCircle className="h-3 w-3" />
                  {fieldErrors.confirmPassword}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Resetting Password...' : 'Reset Password'}
            </Button>
          </form>
        </div>

        <div className="bg-muted rounded-(--radius) border p-3">
          <p className="text-accent-foreground text-center text-sm">
            Remember your password ?
            <Button asChild variant="link" className="px-2">
              <Link href="/signin">Sign In</Link>
            </Button>
          </p>
        </div>
      </div>
    </section>
  )
}
