"use server";
import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { eq } from "drizzle-orm";
import { randomUUID } from 'crypto';

// Import template loader (adjust path as needed)
import { getTemplateAccounts } from "@/lib/data/coaTemplates";

// Type for template account
interface TemplateAccount {
  name: string;
  accountNumber?: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  description?: string;
  isHeader?: boolean;
  level?: number;
  parentAccountNumber?: string;
  classification?: string;
  financialStatementSection?: string;
  accountGroup?: string;
  cashFlowCategory?: string;
  displayOrder?: number;
  subcategory?: string;
  gaapCategory?: string;
  ifrsCategory?: string;
  notes?: string;
}

// Allowed enum values
const allowedClassification = ["current", "non_current", "operating", "non_operating", "n_a"];
const allowedAccountGroup = [
  "cash_and_equivalents", "accounts_receivable", "inventory", "prepaid_expenses", "short_term_investments", "property_plant_equipment", "accumulated_depreciation", "intangible_assets", "long_term_investments", "other_assets", "accounts_payable", "accrued_liabilities", "short_term_debt", "unearned_revenue", "long_term_debt", "other_liabilities", "capital_stock", "retained_earnings", "other_equity", "product_sales", "service_revenue", "other_revenue", "cost_of_sales", "selling_expenses", "administrative_expenses", "depreciation_amortization", "interest_expense", "other_expenses", "other"
];
const allowedCashFlowCategory = ["operating", "investing", "financing", "n_a"];
const allowedFinancialStatementSection = [
  "current_assets",
  "non_current_assets",
  "current_liabilities",
  "non_current_liabilities",
  "equity",
  "operating_revenue",
  "other_revenue",
  "cost_of_goods_sold",
  "operating_expenses",
  "other_expenses"
];
const allowedAccountType = ["asset", "liability", "equity", "revenue", "expense"];

function safeEnum<T extends string>(val: any, allowed: readonly T[], fallback: T): T {
  return allowed.includes(val) ? val : fallback;
}

export async function createAccountsFromTemplate(organizationId: string, templateId: string) {
  // Load the template accounts
  const rawTemplateAccounts = await getTemplateAccounts(templateId);
  if (!rawTemplateAccounts || !Array.isArray(rawTemplateAccounts) || rawTemplateAccounts.length === 0) {
    return { success: false, message: "No accounts found for template." };
  }
  const templateAccounts = rawTemplateAccounts;

  // Validate required fields and check for duplicate account numbers
  const accountNumbers = new Set<string>();
  for (const acc of templateAccounts) {
    const checkedType = safeEnum(acc.type, allowedAccountType, 'asset');
    if (!acc.name || !checkedType) {
      return { success: false, message: `Missing required fields for account: ${JSON.stringify(acc)}` };
    }
    if (acc.accountNumber) {
      if (accountNumbers.has(acc.accountNumber)) {
        return { success: false, message: `Duplicate account number in template: ${acc.accountNumber}` };
      }
      accountNumbers.add(acc.accountNumber);
    }
  }

  // Map account type to normal balance
  function getNormalBalance(type: string): 'debit' | 'credit' {
    if (type === 'asset' || type === 'expense') return 'debit';
    if (type === 'liability' || type === 'equity' || type === 'revenue') return 'credit';
    return 'debit'; // default fallback
  }

  // 1. Insert all accounts without parentId, but with level from template
  const insertRecords = templateAccounts.map((acc) => {
    const base = {
      id: randomUUID(),
      organizationId,
      name: String(acc.name),
      accountNumber: acc.accountNumber ? String(acc.accountNumber) : undefined,
      type: safeEnum(acc.type, allowedAccountType, 'asset') as "asset" | "liability" | "equity" | "revenue" | "expense",
      normalBalance: getNormalBalance(acc.type),
      description: acc.description ? String(acc.description) : undefined,
      parentId: null, // Set in step 2
      isHeader: !!acc.isHeader,
      level: typeof acc.level === 'number' ? acc.level : 0,
      isSystem: true,
      isActive: true,
      classification: acc.classification
        ? safeEnum(acc.classification, allowedClassification, 'n_a') as "current" | "non_current" | "operating" | "non_operating" | "n_a"
        : undefined,
      financialStatementSection: acc.financialStatementSection
        ? safeEnum(acc.financialStatementSection, allowedFinancialStatementSection, 'other_expenses') as
            | "current_assets"
            | "non_current_assets"
            | "current_liabilities"
            | "non_current_liabilities"
            | "equity"
            | "operating_revenue"
            | "other_revenue"
            | "cost_of_goods_sold"
            | "operating_expenses"
            | "other_expenses"
        : undefined,
      accountGroup: acc.accountGroup
        ? safeEnum(acc.accountGroup, allowedAccountGroup, 'other') as
            | "cash_and_equivalents"
            | "accounts_receivable"
            | "inventory"
            | "prepaid_expenses"
            | "short_term_investments"
            | "property_plant_equipment"
            | "accumulated_depreciation"
            | "intangible_assets"
            | "long_term_investments"
            | "other_assets"
            | "accounts_payable"
            | "accrued_liabilities"
            | "short_term_debt"
            | "unearned_revenue"
            | "long_term_debt"
            | "other_liabilities"
            | "capital_stock"
            | "retained_earnings"
            | "other_equity"
            | "product_sales"
            | "service_revenue"
            | "other_revenue"
            | "cost_of_sales"
            | "selling_expenses"
            | "administrative_expenses"
            | "depreciation_amortization"
            | "interest_expense"
            | "other_expenses"
            | "other"
        : undefined,
      cashFlowCategory: acc.cashFlowCategory
        ? safeEnum(acc.cashFlowCategory, allowedCashFlowCategory, 'n_a') as "operating" | "investing" | "financing" | "n_a"
        : undefined,
      displayOrder: typeof acc.displayOrder === 'number' ? acc.displayOrder : 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    return {
      ...base,
      ...((acc as any).subcategory ? { subcategory: (acc as any).subcategory } : {}),
      ...((acc as any).gaapCategory ? { gaapCategory: (acc as any).gaapCategory } : {}),
      ...((acc as any).ifrsCategory ? { ifrsCategory: (acc as any).ifrsCategory } : {}),
      ...((acc as any).notes ? { notes: (acc as any).notes } : {}),
    };
  });

  let inserted;
  try {
    inserted = await db.insert(accounts).values(insertRecords).returning({ id: accounts.id, accountNumber: accounts.accountNumber });
  } catch (err) {
    return { success: false, message: `DB insert error: ${err instanceof Error ? err.message : String(err)}` };
  }

  const numberToId: Record<string, string> = {};
  for (const row of inserted) {
    if (row.accountNumber) numberToId[row.accountNumber] = row.id;
  }

  // 2. Update parentId for accounts with parentAccountNumber
  for (const acc of templateAccounts) {
    if (acc.parentAccountNumber && acc.accountNumber && numberToId[acc.accountNumber] && numberToId[acc.parentAccountNumber]) {
      try {
        await db.update(accounts)
          .set({ parentId: numberToId[acc.parentAccountNumber] })
          .where(eq(accounts.id, numberToId[acc.accountNumber]));
      } catch (err) {
        return { success: false, message: `DB parent update error for account ${acc.accountNumber}: ${err instanceof Error ? err.message : String(err)}` };
      }
    }
  }

  return { success: true, count: templateAccounts.length };
} 