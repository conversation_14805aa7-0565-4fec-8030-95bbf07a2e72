"use server";

import { auth } from "@/lib/auth";

export async function handlePostSignUp({ name, email, password }: { name: string; email: string; password: string }) {
  try {
    // 1. Register the user
    await auth.api.signUpEmail({
      body: {
        name,
        email,
        password,
      },
    });
    
    // 2. Better Auth will automatically handle pending invitations
    // No need to manually call acceptPendingInvites
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
} 