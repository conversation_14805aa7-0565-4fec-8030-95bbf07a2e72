import { NextRequest, NextResponse } from "next/server";
import { getOrganizationPlanInfo } from "@/lib/feature-gates";

export async function GET(request: NextRequest) {
  try {
    const planInfo = await getOrganizationPlanInfo();
    return NextResponse.json(planInfo);
  } catch (error) {
    console.error("Error fetching plan info:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
} 