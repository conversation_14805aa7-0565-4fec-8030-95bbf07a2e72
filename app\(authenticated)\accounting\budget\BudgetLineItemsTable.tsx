"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Edit, Trash2 } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { getAccountsForBudget } from "@/lib/actions/budget/get-accounts-for-budget";
import { createBudgetLineItem } from "@/lib/actions/budget/create-budget-line-item";
import { getBudgetLineItems } from "@/lib/actions/budget/get-budget-line-items";
import { useEffect, useState } from "react";

type AccountInfo = {
  id: string;
  name: string;
  accountNumber: string | null;
};

export function BudgetLineItemsTable({ budgetPeriodId }: { budgetPeriodId: string }) {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [newItem, setNewItem] = useState({
    accountId: "",
    budgetedAmount: "",
    notes: "",
  });

  // Convert direct function call to useQuery with optimization
  const { data: accountsData, isLoading: isLoadingAccounts } = useQuery({
    queryKey: ["accountsForBudget"],
    queryFn: getAccountsForBudget,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  // Extract accounts from the query result
  const accounts = accountsData ? [...accountsData.revenue, ...accountsData.expenses] : [];

  const {
    data: lineItems = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["budgetLineItems", budgetPeriodId],
    queryFn: () => getBudgetLineItems(budgetPeriodId),
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const createItemMutation = useMutation({
    mutationFn: createBudgetLineItem,
    onSuccess: () => {
      toast.success("Budget item created successfully.");
      queryClient.invalidateQueries({ queryKey: ["budgetLineItems", budgetPeriodId] });
      setNewItem({ accountId: "", budgetedAmount: "", notes: "" });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleCreate = () => {
    if (!newItem.accountId || !newItem.budgetedAmount) {
      toast.error("Please select an account and enter a budgeted amount.");
      return;
    }
    createItemMutation.mutate({
      budgetPeriodId,
      accountId: newItem.accountId,
      budgetedAmount: parseFloat(newItem.budgetedAmount),
      notes: newItem.notes,
    });
  };

  if (isLoading) return <div>Loading...</div>;
  
  const availableAccounts = accounts.filter(
    account => !lineItems.some(item => item.accountId === account.id)
  );

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Account</TableHead>
            <TableHead className="text-right">Budgeted Amount</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {lineItems.map((item) => (
            <TableRow key={item.id}>
              <TableCell>
                <div className="font-medium">{item.accountName}</div>
                <div className="text-sm text-muted-foreground">{item.accountNumber}</div>
              </TableCell>
              <TableCell className="text-right">${parseFloat(item.budgetedAmount).toFixed(2)}</TableCell>
              <TableCell>{item.notes}</TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon" onClick={() => setIsEditing(item.id)}>
                  <Edit className="h-4 w-4" />
                </Button>
                 <Button variant="ghost" size="icon" className="text-red-500">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
          {/* New Item Row */}
          <TableRow>
            <TableCell>
              <Select
                value={newItem.accountId}
                onValueChange={(value) => setNewItem({ ...newItem, accountId: value })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select account..." />
                </SelectTrigger>
                <SelectContent>
                  {availableAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.accountNumber} - {account.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TableCell>
            <TableCell>
              <Input
                type="number"
                placeholder="0.00"
                value={newItem.budgetedAmount}
                onChange={(e) => setNewItem({ ...newItem, budgetedAmount: e.target.value })}
                className="text-right"
              />
            </TableCell>
            <TableCell>
              <Input
                placeholder="Notes..."
                value={newItem.notes}
                onChange={(e) => setNewItem({ ...newItem, notes: e.target.value })}
              />
            </TableCell>
            <TableCell className="text-right">
              <Button onClick={handleCreate} disabled={createItemMutation.isPending}>
                <Plus className="h-4 w-4 mr-2" /> Add
              </Button>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
} 