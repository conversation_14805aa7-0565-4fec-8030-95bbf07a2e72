"use server";

import { db } from "@/db/drizzle";
import { auditLogs } from "@/db/schema/schema";
import { and, desc, eq } from "drizzle-orm";
import { getAuditContext } from "./context";

/**
 * Retrieve high-risk security alerts for an organization
 */
export async function getSecurityAlerts(organizationId?: string) {
  try {
    const { organizationId: contextOrgId } = await getAuditContext();
    const targetOrgId = organizationId || contextOrgId;

    if (!targetOrgId) {
      return [];
    }

    const alerts = await db
      .select()
      .from(auditLogs)
      .where(
        and(
          eq(auditLogs.organizationId, targetOrgId),
          eq(auditLogs.riskLevel, "high"),
        ),
      )
      .orderBy(desc(auditLogs.createdAt))
      .limit(20);

    return alerts;
  } catch (error) {
    console.error("Failed to retrieve security alerts:", error);
    return [];
  }
} 