import { <PERSON>Header } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { hasPermission } from "@/lib/permissions";
import { getServerUserContext } from "@/lib/server-auth";
import Link from "next/link";
import { AlertTriangle } from "lucide-react";

export default async function BillingStatusPage() {
    const { organization } = await getServerUserContext();
    const canManageBilling = await hasPermission("BILLING_MANAGE");

    let title = "Account Issue";
    let message = "There is an issue with your account's subscription. Please contact your organization owner or administrator.";

    if (organization?.planStatus === 'suspended') {
        title = "Account Suspended";
        if (canManageBilling) {
            message = "Your organization's account has been suspended, likely due to a failed payment. To restore access and ensure your services are not interrupted, please update your billing information.";
        } else {
            message = "Your organization's account has been suspended, likely due to a failed payment. Please contact your organization owner or an administrator to resolve this issue.";
        }
    } else if (organization?.isActive === false) {
        title = "Account Deactivated";
        message = "Your organization's account has been deactivated. Please contact your administrator or our support team for more information.";
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
            <Card className="w-full max-w-lg text-center shadow-lg">
                <CardHeader>
                    <div className="mx-auto bg-red-100 p-3 rounded-full">
                        <AlertTriangle className="h-8 w-8 text-red-600" />
                    </div>
                    <CardTitle className="mt-4 text-2xl font-bold">{title}</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground mb-6">
                        {message}
                    </p>
                    {canManageBilling && (
                        <Button asChild>
                            <Link href="/settings/billing">Manage Billing & Subscription</Link>
                        </Button>
                    )}
                </CardContent>
            </Card>
        </div>
    );
} 