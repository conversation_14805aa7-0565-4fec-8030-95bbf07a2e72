import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Bell, Calendar, DollarSign } from "lucide-react";

interface Payment {
  id: string;
  clientName: string;
  amount: number;
  dueDate: string;
  status: "pending" | "overdue" | "upcoming";
}

import { Skeleton } from "@/components/ui/skeleton";

interface PaymentRemindersProps {
  payments?: Payment[];
  isLoading?: boolean;
}

const PaymentReminders = ({
  payments = [
    {
      id: "1",
      clientName: "Acme Corp",
      amount: 2500,
      dueDate: "2024-04-15",
      status: "pending",
    },
    {
      id: "2",
      clientName: "Tech Solutions",
      amount: 1800,
      dueDate: "2024-04-10",
      status: "overdue",
    },
    {
      id: "3",
      clientName: "Design Studio",
      amount: 3000,
      dueDate: "2024-04-20",
      status: "upcoming",
    },
  ] as Payment[],
  isLoading = false,
}: PaymentRemindersProps) => {
  const getStatusColor = (status: Payment["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500/20 text-yellow-500";
      case "overdue":
        return "bg-red-500/20 text-red-500";
      case "upcoming":
        return "bg-blue-500/20 text-blue-500";
      default:
        return "bg-gray-500/20 text-gray-500";
    }
  };

  return (
    <Card className="w-full h-[400px] bg-black/50 border-white/10">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2 text-white">
            <Bell className="w-5 h-5 text-gray-400" />
            Payment Reminders
          </CardTitle>
          <Badge variant="secondary" className="bg-white/10 text-gray-300">
            {payments.length}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[320px] pr-4">
          <div className="space-y-3">
            {isLoading
              ? Array(3)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  ))
              : payments.map((payment) => (
                  <div
                    key={payment.id}
                    className="p-3 rounded-lg border border-white/10 hover:bg-white/5 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-white">
                        {payment.clientName}
                      </h4>
                      <Badge
                        variant="secondary"
                        className={getStatusColor(payment.status)}
                      >
                        {payment.status}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-4 h-4 text-gray-400" />$
                        {payment.amount.toLocaleString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        {new Date(payment.dueDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default PaymentReminders;