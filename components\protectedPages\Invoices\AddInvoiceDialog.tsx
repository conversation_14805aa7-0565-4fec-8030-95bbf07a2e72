import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";    
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Trash } from "lucide-react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { invoiceFormSchema } from "@/lib/actions/invoices/invoice.schema";
import { useQuery } from "@tanstack/react-query";
import { getInvoiceById } from "@/lib/actions/invoices/get-invoice-by-id";
import { getClients } from "@/lib/actions/clients/get-clients";
import { getProducts } from "@/lib/actions/products/get-products";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { CommandSeparator } from "@/components/ui/command";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

const formSchema = invoiceFormSchema;

type InvoiceFormData = z.infer<typeof formSchema>;

interface AddInvoiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: InvoiceFormData) => void;
  invoiceId?: string | null;
  isPending: boolean;
}

const AddInvoiceDialog = ({
  isOpen,
  onClose,
  onSubmit,
  invoiceId,
  isPending,
}: AddInvoiceDialogProps) => {
  const [popoverOpen, setPopoverOpen] = React.useState(false);
  const isEdit = !!invoiceId;
  const { data: invoice, isLoading: isLoadingInvoice } = useQuery({
    queryKey: ["invoice", invoiceId],
    queryFn: () => invoiceId ? getInvoiceById(invoiceId) : null,
    enabled: !!invoiceId && isOpen,
  });
  const { data: clients = [], isLoading: isLoadingClients } = useQuery({
    queryKey: ["clients"],
    queryFn: getClients,
  });
  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      lineItems: [{ productId: "", description: "", quantity: 1, unitPrice: 0, totalPrice: 0 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "lineItems",
  });

  useEffect(() => {
    if (isOpen && isEdit && invoice) {
      reset({
        clientId: invoice.clientId,
        status: invoice.status || "draft",
        issueDate: new Date(invoice.date),
        dueDate: invoice.dueDate ? new Date(invoice.dueDate) : new Date(),
        totalAmount: parseFloat(invoice.total),
        notes: invoice.notes || "",
        lineItems: invoice.lineItems && invoice.lineItems.length > 0 ? invoice.lineItems : [],
      });
    } else if (isOpen && !isEdit) {
      reset({
        clientId: "",
        status: "draft",
        issueDate: new Date(),
        dueDate: new Date(),
        lineItems: [{ productId: "", description: "", quantity: 1, unitPrice: 0, totalPrice: 0 }],
        totalAmount: 0,
        notes: "",
      });
    }
  }, [invoice, reset, isOpen, isEdit]);

  if ((isEdit && isLoadingInvoice) || isLoadingClients) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Loading Invoice</DialogTitle>
          </DialogHeader>
          <div>Loading...</div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? "Edit Invoice" : "Add New Invoice"}
          </DialogTitle>
          <DialogDescription>
            {isEdit ? "Update the invoice information below." : "Create a new invoice with client and line item details."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Form fields using react-hook-form */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="clientId">Client</Label>
              <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    role="combobox"
                    className="w-full justify-between"
                  >
                    {watch("clientId")
                      ? clients.find((c) => c.id === watch("clientId"))?.name
                      : "Select client..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Search clients..." />
                    <CommandEmpty>No client found.</CommandEmpty>
                    <CommandGroup>
                      {clients.map((client) => (
                        <CommandItem
                          key={client.id}
                          value={client.name}
                          onSelect={(currentValue) => {
                            const selectedClient = clients.find(c => c.name.toLowerCase() === currentValue.toLowerCase());
                            if (selectedClient) {
                                setValue("clientId", selectedClient.id, { shouldValidate: true });
                            }
                            setPopoverOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              watch("clientId") === client.id
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {client.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              {errors.clientId && <p className="text-red-500 text-xs">{errors.clientId.message}</p>}
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select onValueChange={(value: "draft" | "sent" | "paid") => setValue('status', value)} defaultValue={invoice?.status || "draft"}>
                <SelectTrigger id="status"><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Line Items */}
          <div>
            <Label>Line Items</Label>
            {fields.map((field, index) => (
              <div key={field.id} className="flex gap-2 items-center mt-2">
                <Input {...register(`lineItems.${index}.description`)} placeholder="Description" />
                <Input type="number" {...register(`lineItems.${index}.quantity`, { valueAsNumber: true })} placeholder="Qty" />
                <Input type="number" {...register(`lineItems.${index}.unitPrice`, { valueAsNumber: true })} placeholder="Price" />
                <Button type="button" variant="outline" onClick={() => remove(index)}><Trash className="h-4 w-4" /></Button>
              </div>
            ))}
            <Button type="button" onClick={() => append({ productId: "", description: "", quantity: 1, unitPrice: 0, totalPrice: 0 })} className="mt-2">
              <Plus className="h-4 w-4 mr-2" />Add Item
            </Button>
          </div>
          <DialogFooter>
            <Button type="button" variant="ghost" onClick={onClose}>Cancel</Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? "Saving..." : "Save Invoice"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddInvoiceDialog;
