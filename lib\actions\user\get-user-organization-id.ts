"use server";
import { db } from "@/db/drizzle";
import { member } from "@/db/schema/schema";
import { eq, and } from "drizzle-orm";

export async function getUserOrganizationId(userId: string) {
  if (!userId) return null;
  const orgMember = await db
    .select({ organizationId: member.organizationId })
.from(member)
.where(eq(member.userId, userId));

  return orgMember.length > 0 ? orgMember[0].organizationId : null;
} 