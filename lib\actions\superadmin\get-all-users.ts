import { neon } from "@neondatabase/serverless";
import { user } from "@/db/schema/schema";
import { requireSuperadmin } from "./index";

export async function getAllUsersForSuperadmin(session: any) {
  const connStr = process.env.DATABASE_URL || '';
  // Redact password for safety
  const redactedConnStr = connStr.replace(/(postgres(?:ql)?:\/\/[^:]+:)[^@]+(@)/, '$1<REDACTED>$2');
  try {
    await requireSuperadmin(session);
    // Use neon native client for raw SQL
    const sql = neon(process.env.DATABASE_URL!);
    try {
      const test = await sql`SELECT * FROM "user" LIMIT 1`;
    } catch (rawError) {
    }
    // Fetch all users with basic info using neon native client
    const users = await sql`SELECT id, name, email, onboarded AS "isActive", created_at AS "createdAt" FROM "user"`;
    return users;
  } catch (error) {
    throw error;
  }
} 