import { type ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { MoreVertical } from "lucide-react";
import { useState } from "react";

export type Transaction = {
  id: string;
  date: string;
  description: string;
  totalAmount: string;
  status: string | null;
  type: string;
};

export function getColumns({ onEdit, onDelete }: { onEdit: (tx: Transaction) => void; onDelete: (tx: Transaction) => void; }): ColumnDef<Transaction>[] {
  return [
    { accessorKey: "date", header: "Date" },
    { accessorKey: "description", header: "Description" },
    { accessorKey: "totalAmount", header: "Amount" },
    { accessorKey: "status", header: "Status" },
    { accessorKey: "type", header: "Type" },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const tx = row.original;
        const [open, setOpen] = useState(false);
        return (
          <div className="relative">
            <Button variant="ghost" size="icon" onClick={() => setOpen(o => !o)}>
              <MoreVertical className="w-4 h-4" />
            </Button>
            {open && (
              <div className="absolute right-0 z-10 mt-2 w-32 bg-white border rounded shadow-lg">
                <button className="block w-full px-4 py-2 text-left hover:bg-gray-100" onClick={() => { setOpen(false); onEdit(tx); }}>Edit</button>
                <button className="block w-full px-4 py-2 text-left hover:bg-gray-100 text-red-600" onClick={() => { setOpen(false); onDelete(tx); }}>Delete</button>
              </div>
            )}
          </div>
        );
      },
    },
  ];
} 