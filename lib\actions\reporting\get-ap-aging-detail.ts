"use server";

import { db } from "@/db/drizzle";
import { bills, vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, ne, lte } from "drizzle-orm";
import { ageBucket } from "./age-bucket";

export async function getAPAgingDetail(asOfDateStr?: string) {
    // TODO: Fix type issues with this function
    return Promise.resolve([]);
    // const { organization } = await getServerUserContext();
    // const organizationId = organization.id;
    // const asOfDate = asOfDateStr ? new Date(asOfDateStr) : new Date();

    // const openBills = await db.select({
    //     billId: bills.id,
    //     vendorName: vendors.name,
    //     billDate: bills.date,
    //     dueDate: bills.dueDate,
    //     amount: bills.amount,
    //     status: bills.status,
    // }).from(bills)
    //   .leftJoin(vendors, eq(bills.vendorId, vendors.id))
    //   .where(and(
    //       eq(bills.organizationId, organizationId),
    //       ne(bills.status, 'paid'),
    //       ne(bills.status, 'void'),
    //       lte(bills.date, asOfDate)
    //   ));

    // const detailed = openBills.map((bill: any) => {
    //     const bucket = ageBucket(bill.dueDate!, asOfDate);
    //     return {
    //         ...bill,
    //         bucket,
    //         amount: parseFloat(bill.amount)
    //     };
    // });
    
    // return detailed;
} 