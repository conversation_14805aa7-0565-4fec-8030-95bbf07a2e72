"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";
import { revalidatePath } from "next/cache";

type PriceAdjustment =
  | { type: "fixed"; value: number }
  | { type: "percentage_increase"; value: number }
  | { type: "percentage_decrease"; value: number }
  | { type: "amount_increase"; value: number }
  | { type: "amount_decrease"; value: number };

export async function bulkUpdateProductPrice(
  productIds: string[],
  adjustment: PriceAdjustment
) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    if (productIds.length === 0) {
      return { success: false, message: "No products selected." };
    }

    const productsToUpdate = await db
      .select({
        id: products.id,
        price: products.price,
      })
      .from(products)
      .where(
        and(
          eq(products.organizationId, orgId),
          inArray(products.id, productIds)
        )
      );
    
    for (const p of productsToUpdate) {
        let newPrice: number;
        const currentPrice = parseFloat(p.price || '0');

        switch(adjustment.type) {
            case 'fixed':
                newPrice = adjustment.value;
                break;
            case 'percentage_increase':
                newPrice = currentPrice * (1 + adjustment.value / 100);
                break;
            case 'percentage_decrease':
                newPrice = currentPrice * (1 - adjustment.value / 100);
                break;
            case 'amount_increase':
                newPrice = currentPrice + adjustment.value;
                break;
            case 'amount_decrease':
                newPrice = currentPrice - adjustment.value;
                break;
        }

        await db.update(products).set({ price: newPrice.toFixed(2) }).where(eq(products.id, p.id));
    }


    revalidatePath("/products");

    return {
      success: true,
      message: `Successfully updated prices for ${productIds.length} products.`,
    };
  } catch (error) {
    console.error("Failed to bulk update product prices:", error);
    return {
      success: false,
      message: "An error occurred while updating prices.",
    };
  }
} 