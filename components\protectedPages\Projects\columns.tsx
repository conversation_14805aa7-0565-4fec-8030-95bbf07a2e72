"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import * as React from "react";
import { Checkbox } from "@/components/ui/checkbox";

export type Project = {
  id: string;
  name: string;
  description: string | null;
  clientName: string | null;
  status: "not_started" | "in_progress" | "completed" | "on_hold" | "canceled";
  startDate: Date | null;
  endDate: Date | null;
};

interface GetProjectColumnsProps {
  onEdit: (project: Project) => void;
  onDelete: (projectId: string) => void;
}

export const columns: ColumnDef<Project>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "Project Name",
  },
  {
    accessorKey: "clientName",
    header: "Client",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <Badge>{row.getValue("status")}</Badge>,
  },
  {
    accessorKey: "startDate",
    header: "Start Date",
    cell: ({ row }) =>
      row.getValue("startDate")
        ? new Date(row.getValue("startDate") as string).toLocaleDateString()
        : "N/A",
  },
  {
    accessorKey: "endDate",
    header: "End Date",
    cell: ({ row }) =>
      row.getValue("endDate")
        ? new Date(row.getValue("endDate") as string).toLocaleDateString()
        : "N/A",
  },
];

export function getActionColumn({
  onEdit,
  onDelete,
}: GetProjectColumnsProps): ColumnDef<Project> {
  return {
    id: "actions",
    header: () => <div className="text-right">Actions</div>,
    cell: ({ row }) => {
      const project = row.original;
      const [dropdownOpen, setDropdownOpen] = React.useState(false);
      
      return (
        <div className="text-right font-medium">
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem 
                onSelect={(event) => {
                  event.preventDefault();
                  setDropdownOpen(false);
                  onEdit(project);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(project.id)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  };
} 