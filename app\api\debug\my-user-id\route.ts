import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

export async function GET() {
  try {
    const session = await auth.api.getSession({
      headers: await headers()
    });

    if (!session) {
      return NextResponse.json({ 
        error: "Not authenticated",
        message: "Please sign in first"
      });
    }

    return NextResponse.json({
      userId: session.user.id,
      userEmail: session.user.email,
      userName: session.user.name,
      sessionId: session.session.id,
      emailVerified: session.user.emailVerified,
      message: "This is your user ID - it should match the external_customer_id in webhook logs"
    });
  } catch (error) {
    console.error("Error getting user session:", error);
    return NextResponse.json(
      { error: "Failed to get session" },
      { status: 500 }
    );
  }
} 