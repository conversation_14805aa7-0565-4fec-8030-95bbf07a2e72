"use client";

import React, { useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { columns as baseColumns, getActionColumn, Project } from "@/components/protectedPages/Projects/columns";
import ProjectsDataTable from "./projects-data-table";
import AddProjectSheet from "./AddProjectSheet";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  getProjects,
} from "@/lib/actions/projects/get-projects";
import { deleteProjects } from "@/lib/actions/projects/delete-projects";
import { upsertProject } from "@/lib/actions/projects/upsert-project";
import { getClients } from "@/lib/actions/clients/get-clients";
import { RowSelectionState } from "@tanstack/react-table";
import { NewProject } from "@/lib/actions/projects/project.schema";
import DeleteConfirmationDialog from "@/components/general/DeleteConfirmationDialog";
import { Skeleton } from "@/components/ui/skeleton";

export default function ProjectsPageClient() {
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editingProjectId, setEditingProjectId] = React.useState<string | null>(null);
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [projectsToDelete, setProjectsToDelete] = React.useState<string[]>([]);

  const { data: projects = [], isLoading: isLoadingProjects } = useQuery({
    queryKey: ["projects"],
    queryFn: getProjects,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: clientList = [], isLoading: isLoadingClients } = useQuery({
    queryKey: ["clientsForProjects"],
    queryFn: async () => {
      const clients = await getClients();
      return clients.map(({ id, name }) => ({ id, name }));
    },
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const mutation = useMutation({
    mutationFn: async (data: {
      projectData: NewProject;
      id?: string;
    }) => {
      if (data.id) {
        return upsertProject({ ...data.projectData});
      }
      return upsertProject(data.projectData);
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["projects"] });
        setDialogOpen(false);
        setEditingProjectId(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (ids: string[]) => deleteProjects(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["projects"] });
        setRowSelection({});
      } else {
        toast.error(result.message);
      }
      setDeleteDialogOpen(false);
      setProjectsToDelete([]);
    },
    onError: (error) => {
      toast.error(error.message);
      setDeleteDialogOpen(false);
      setProjectsToDelete([]);
    },
  });

  const selectedProjectIds = useMemo(
    () =>
      Object.keys(rowSelection)
        .map((key) => projects[parseInt(key, 10)]?.id)
        .filter(Boolean),
    [rowSelection, projects]
  );

  const handleSubmit = (projectData: NewProject) => {
    const dataToUpsert = editingProjectId
      ? { ...projectData, id: editingProjectId }
      : projectData;
    mutation.mutate({ projectData: dataToUpsert });
  };

  const openDeleteDialog = (ids: string[]) => {
    setProjectsToDelete(ids);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    mutateDelete(projectsToDelete);
  };

  const openEditDialog = (project: Project) => {
    setEditingProjectId(project.id);
    setDialogOpen(true);
  };

  const openNewDialog = () => {
    setEditingProjectId(null);
    setDialogOpen(true);
  };

  const columns = useMemo(() => {
    const actionColumn = getActionColumn({
      onEdit: openEditDialog,
      onDelete: (id) => openDeleteDialog([id]),
    });
    return [...baseColumns, actionColumn];
  }, []);

  if (isLoadingProjects || isLoadingClients) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-28" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Projects</h1>
        <Button onClick={openNewDialog}>Add Project</Button>
      </div>

      {selectedProjectIds.length > 0 && (
        <div className="flex justify-between items-center p-2 bg-muted rounded-md mb-4">
          <span>{selectedProjectIds.length} selected</span>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => openDeleteDialog(selectedProjectIds)}
            disabled={isDeleting}
          >
            Delete Selected
          </Button>
        </div>
      )}

      <ProjectsDataTable
        data={projects}
        columns={columns}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
      <AddProjectSheet
        open={dialogOpen}
        onOpenChange={(open) => {
          if (!open) setEditingProjectId(null);
          setDialogOpen(open);
        }}
        onSubmit={handleSubmit}
        isPending={mutation.isPending}
        projectId={editingProjectId}
      />
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        isPending={isDeleting}
        count={projectsToDelete.length}
      />
    </div>
  );
} 