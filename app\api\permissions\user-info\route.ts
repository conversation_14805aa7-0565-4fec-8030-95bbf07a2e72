import { NextRequest, NextResponse } from "next/server";
import { getUserPermissions } from "@/lib/permissions";

export async function GET(request: NextRequest) {
  try {
    const result = await getUserPermissions();

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { 
        systemPermissions: [],
        role: 'viewer',
        error: error instanceof Error ? error.message : "Unknown error" 
      },
      { status: 500 }
    );
  }
} 