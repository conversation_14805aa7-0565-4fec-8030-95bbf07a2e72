"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { DollarSign, User, Calendar, CreditCard, FileText, Info, ArrowLeft, ArrowRight, Plus } from "lucide-react";
import { moneyPaidActionSchema } from "@/lib/actions/automation/types";
import { getVendors } from "@/lib/actions/vendors/get-vendors";
import { getProducts } from "@/lib/actions/products/get-products";
import { getBills } from "@/lib/actions/bills/get-bills";
import AddVendorSheet from "@/components/protectedPages/Vendors/AddVendorSheet";
import { createVendor } from "@/lib/actions/vendors/create-vendor";
import { toast } from "sonner";

interface MoneyPaidFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const formSchema = moneyPaidActionSchema.extend({
  date: z.string().min(1, "Date is required"),
  vendorId: z.string().min(1, "Vendor is required"),
  billId: z.string().optional().nullable(),
  // Remove productId, make description optional
  description: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

export function MoneyPaidForm({ onSubmit, onCancel, isLoading }: MoneyPaidFormProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAddVendorOpen, setIsAddVendorOpen] = useState(false);
  const [isCreatingVendor, setIsCreatingVendor] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "money_paid",
      date: new Date().toISOString().split('T')[0],
      amount: 0,
      description: "",
      vendorId: "",
      productId: "",
      billId: "",
    },
  });

  const handleCreateVendor = async (vendorData: any) => {
    setIsCreatingVendor(true);
    try {
      const result = await createVendor(vendorData);
      if (result.success) {
        toast.success("Vendor created successfully!");
        setIsAddVendorOpen(false);
        // Refresh vendors list by refetching
        getVendors().then(setVendors);
      } else {
        toast.error(result.message || "Failed to create vendor");
      }
    } catch (error) {
      toast.error("Error creating vendor");
      console.error(error);
    } finally {
      setIsCreatingVendor(false);
    }
  };

  const handleSubmit = async (data: FormData) => {
    setIsProcessing(true);
    try {
      const processedData = {
        ...data,
        date: new Date(data.date),
      };
      onSubmit(processedData);
    } finally {
      setIsProcessing(false);
    }
  };

  // Fetch dropdown data
  const [vendors, setVendors] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [bills, setBills] = useState<any[]>([]);
  useEffect(() => {
    getVendors().then(setVendors);
    getProducts().then(setProducts);
    getBills().then(setBills);
  }, []);

  return (
    <div className="space-y-4">
      {/* Compact Header */}
      <div className="text-center mb-2">
        <div className="mx-auto w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
          <DollarSign className="h-6 w-6 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold mb-1">Vendor/Bill Payment</h3>
        <p className="text-muted-foreground text-sm">
          Record a payment to a vendor for products or services. We'll handle the accounting.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-2">
          <Card>
            <CardHeader className="pb-1">
              <CardTitle className="flex items-center gap-2 text-base">
                <DollarSign className="h-4 w-4" />
                Money Paid
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {/* First row: Vendor, Date, Bill */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-1">
                <FormField
                  control={form.control}
                  name="vendorId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vendor *</FormLabel>
                                              <Select 
                          onValueChange={(value) => {
                            if (value === "__add_new__") {
                              setIsAddVendorOpen(true);
                              // Don't set the form value, just open the sheet
                            } else {
                              field.onChange(value);
                            }
                          }} 
                          value={field.value}
                        >
                        <FormControl>
                          <SelectTrigger className="max-w-xs">
                            <SelectValue placeholder="Select vendor" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {vendors.map((vendor) => (
                            <SelectItem key={vendor.id} value={vendor.id}>{vendor.name}</SelectItem>
                          ))}
                          <SelectItem 
                            value="__add_new__" 
                            className="text-primary font-medium cursor-pointer"
                          >
                            <Plus className="h-4 w-4 mr-2 inline" />
                            + Add new Vendor
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="date"
                            className="pr-2 w-full max-w-xs"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="billId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bill (optional)</FormLabel>
                      <Select
                        onValueChange={val => field.onChange(val === undefined ? null : val)}
                        value={field.value || undefined}
                      >
                        <FormControl>
                          <SelectTrigger className="max-w-xs">
                            <SelectValue placeholder="Bill (optional)" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {bills.map((bill) => (
                            <SelectItem key={bill.id} value={bill.id}>
                              {bill.billNumber || bill.id}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* Second row: Amount only */}
              <div className="grid grid-cols-1 md:grid-cols-1 gap-1">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            placeholder="0.00"
                            className="pr-2 w-full max-w-xs pl-8"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        How much did you pay?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* Description below (optional) */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe this transaction (optional)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Summary */}
          {form.watch("amount") > 0 && (
            <Card className="border-primary/20 bg-primary/5">
              <CardHeader>
                <CardTitle className="text-lg">Payment Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-semibold text-lg text-blue-600">
                      ${form.watch("amount").toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Date:</span>
                    <span className="font-medium">{form.watch("date") || "Not specified"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Description:</span>
                    <span className="font-medium">{form.watch("description") || "Not specified"}</span>
                  </div>
                  <Separator className="my-3" />
                  <div className="text-sm text-muted-foreground">
                    <p className="font-medium mb-1">What will happen:</p>
                    <ul className="space-y-1">
                      <li>✅ Your cash/bank account will decrease by ${form.watch("amount").toFixed(2)}</li>
                      <li>✅ Expense will be recorded (or payables reduced if paying a bill)</li>
                      <li>✅ Your financial reports will update automatically</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
              disabled={isProcessing || isLoading}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={isProcessing || isLoading}
            >
              {isProcessing ? "Processing..." : "Record Payment"}
              {!isProcessing && <ArrowRight className="h-4 w-4 ml-2" />}
            </Button>
          </div>
        </form>
      </Form>

      {/* Add Vendor Sheet */}
      <AddVendorSheet
        open={isAddVendorOpen}
        onOpenChange={setIsAddVendorOpen}
        onSubmit={handleCreateVendor}
        isPending={isCreatingVendor}
        vendorId={null}
      />
    </div>
  );
} 