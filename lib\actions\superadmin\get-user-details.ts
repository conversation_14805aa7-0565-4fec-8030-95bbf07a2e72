import { db } from "@/db/drizzle";
import { user } from "@/db/schema/schema";
import { requireSuperadmin } from "./index";
import { eq } from "drizzle-orm";

export async function getUserDetailsForSuperadmin(session: any, targetUserId: string) {
  await requireSuperadmin(session);
  const result = await db.select({
    id: user.id,
    name: user.name,
    email: user.email,
    emailVerified: user.emailVerified,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    onboarded: user.onboarded,
    twoFactorEnabled: user.twoFactorEnabled,
  }).from(user).where(eq(user.id, targetUserId)).limit(1);
  return result[0] || null;
} 