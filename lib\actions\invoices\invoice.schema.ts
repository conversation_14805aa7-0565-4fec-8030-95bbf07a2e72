import { z } from "zod";
export const lineItemSchema = z.object({
  productId: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  quantity: z.coerce.number().positive(),
  unitPrice: z.coerce.number().positive(),
  totalPrice: z.coerce.number().positive(),
});
export const invoiceFormSchema = z.object({
  clientId: z.string().min(1, "Please select a client."),
  invoiceNumber: z.string().min(1, "Invoice number is required."),
  currency: z.string().min(2, "Currency is required."),
  issueDate: z.coerce.date(),
  dueDate: z.coerce.date().optional(),
  status: z.enum(["draft", "sent", "paid", "overdue", "void"]),
  lineItems: z.array(lineItemSchema).min(1, "Invoice needs at least one item."),
  totalAmount: z.coerce.number().positive(),
  tax: z.coerce.number().optional(),
  notes: z.string().optional(),
});
export type FormState = {
  success: boolean;
  message: string;
  invoiceId?: string;
}; 