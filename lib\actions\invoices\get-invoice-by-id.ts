"use server";

import { db } from "@/db/drizzle";
import { invoices, invoiceItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, desc } from "drizzle-orm";
import { z } from "zod";
import { lineItemSchema } from "@/lib/actions/invoices/invoice.schema";
import { Invoice } from "@/components/protectedPages/Invoices/columns";

type LineItem = z.infer<typeof lineItemSchema>;

export async function getInvoiceById(invoiceId: string): Promise<Invoice & { lineItems: LineItem[] }> {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  const invoice = await db.query.invoices.findFirst({
    where: and(eq(invoices.id, invoiceId), eq(invoices.organizationId, orgId)),
    with: {
      client: { columns: { name: true } },
    },
  });
  if (!invoice) throw new Error('Invoice not found');
  const lineItems = await db.query.invoiceItems.findMany({
    where: eq(invoiceItems.invoiceId, invoiceId),
  });
  return {
    ...invoice,
    clientName: invoice.client?.name ?? 'N/A',
    total: invoice.total,
    status: invoice.status as 'draft' | 'sent' | 'paid' | 'overdue' | 'void',
    dueDate: invoice.dueDate ? new Date(invoice.dueDate) : null,
    date: new Date(invoice.date),
    lineItems: lineItems.map(item => ({
      productId: typeof item.productId === 'string' ? item.productId : '',
      description: item.description,
      quantity: item.quantity,
      unitPrice: parseFloat(item.unitPrice),
      totalPrice: parseFloat(item.total),
    })),
  };
} 