# Application Development Best Practices

This document outlines key best practices derived from the recent refactoring of the Invoices module. Applying these principles consistently across the application will lead to a more robust, secure, and maintainable codebase.

---

### 1. **Server-Side Authentication Patterns**

-   **Principle:** Choose the right server-side authentication method based on the use case. Not all protected logic should behave the same way.

-   **Implementation:** We have two primary patterns for handling auth on the server.

-   #### **A. The Standard Redirect-Based Check (`getServerUserContext`)**
    -   **When to Use:** Use this for any standard page or API route where the user **must** be fully logged in and onboarded to proceed. This should be your default choice for 90% of cases.
    -   **What it Does:** It fetches the complete user, organization, and permissions context. Crucially, it **automatically redirects** the user to the appropriate page (`/signin`, `/onboarding`, etc.) if they don't meet the requirements.
    -   **Benefit:** This keeps your page components extremely clean, as they don't need to contain any logic for handling unauthenticated or non-onboarded users.
    -   **Location:** `import { getServerUserContext } from '@/lib/server-auth'`

-   #### **B. The "Safe" Non-Redirecting Check (Custom Session Fetch)**
    -   **When to Use:** Use this for special cases where the page must handle multiple authentication states without redirecting. The primary example is the `/accept-invite` page, which needs to render different UI for logged-out users, users with the wrong email, and correctly authenticated users.
    -   **What it Does:** It fetches the current session but **does not redirect**. If the user is not authenticated, it simply returns `null`.
    -   **Benefit:** This allows a server action to return structured data (e.g., `{ success: true, status: 'auth_required' }`) to a client component. The client can then use this data to intelligently update the UI (e.g., show "Sign In" buttons) instead of the user being abruptly redirected.
    -   **Example Implementation (inside a server action file):**
        ```typescript
        const getSession = async () => {
          try {
            // This requires `import { headers } from "next/headers";`
            return await auth.api.getSession({ headers: headers() });
          } catch (error) {
            // Fails gracefully if session is not available
            return null;
          }
        };
        ```

---

### 2. **Simplified & Secure Server Actions**

-   **Principle:** Server actions should be clean, focused, and type-safe.
-   **Implementation:**
    -   Design actions to perform a single task (e.g., `createInvoice`, `deleteInvoice`).
    -   Actions should accept strongly-typed, Zod-validated data objects as arguments instead of raw `FormData`. This moves validation to the server and leverages modern TypeScript features.
    -   Avoid `useFormState` for simple operations. Return a clear success/error object (`{ success: boolean, message: string }`).
    -   **Naming:** Group related actions in a single file (e.g., `lib/actions/team/invitation.actions.ts`). If a file handles multiple facets of a feature (like fetching details *and* performing an action), use a general name for the file.

---

### 3. **Efficient Bulk Operations**

-   **Principle:** Minimize database round-trips.
-   **Implementation:** For bulk actions (e.g., deleting multiple invoices), create a dedicated server action (e.g., `deleteInvoices`) that accepts an array of IDs. Perform the operation in a single, efficient database transaction rather than looping through individual delete calls from the client.

---

### 4. **Server-Centric Data Fetching**

-   **Principle:** Fetch data on the server whenever possible to simplify the client and improve performance.
-   **Implementation:** In Next.js, use the `page.tsx` Server Component to fetch all necessary data for a route. Pass this data down to client components as initial props (`<InvoicesPageClient initialInvoices={...} />`). Avoid making `fetch` calls from the client to internal API routes for the initial data load.

---

### 5. **Modern Client-Side State & UI Updates**

-   **Principle:** Let the framework handle UI updates after mutations. Avoid complex manual state management on the client.
-   **Implementation:**
    -   **Pending States:** Use the `useTransition` hook to track the pending state of server actions and provide immediate feedback to the user (e.g., disabling buttons, showing spinners).
    -   **Data Revalidation:** After a successful server action, call `revalidatePath('/')` to automatically refresh the data for the affected route. This ensures the UI is always in sync with the database without any manual state updates on the client.
    -   **Error Handling:** Use a `try/catch` block within the transition to handle potential errors from server actions and display user-friendly notifications (e.g., using `toast`).

---

### 6. **Robust & Accessible Form Components**

-   **Principle:** Build forms that are both powerful and predictable.
-   **Implementation:**
    -   **Complex Forms:** Use `react-hook-form` with `@hookform/resolvers/zod` for robust, end-to-end type-safe forms.
    -   **Custom Inputs (e.g., Combobox):** When using libraries like `cmdk` for searchable dropdowns, ensure the `value` prop is set to the text the user will search for (e.g., `client.name`). The `onSelect` handler should then use this search value to find the corresponding object and set its unique ID in the form state. This provides a good user experience while maintaining data integrity.

---

### 7. **Schema Organization and "use server" Compliance**

-   **Principle:** Maintain clean separation between schemas and server actions while ensuring Next.js compliance.
-   **Implementation:**
    -   **Schema Organization:**
        -   **Server Actions:** Group related server actions into a single file within the `lib/actions/` directory (e.g., `lib/actions/invoices.actions.ts`). This keeps logic for a specific feature area (like invoices) together.
        -   **Schema Files:** Create dedicated schema files in `lib/actions/<feature>/` for all Zod validation schemas and TypeScript types (e.g., `lib/actions/bills/bill.schema.ts`, `lib/actions/accounts/account.schema.ts`).
        -   **Clear Naming:** Use descriptive names for actions (e.g., `createInvoice`, `getInvoiceById`).
        -   **Import Pattern:** Import schemas and types from schema files: `import { billFormSchema, type NewBill } from "@/lib/actions/bills/bill.schema"`
    -   **Error Handling:**
        -   Use `try...catch` blocks in server actions to handle potential errors gracefully.

---

### 8. **Sheet Component Optimization & Responsive Design**

-   **Principle:** Create sheet components that provide excellent user experience across all device types and screen sizes.
-   **Implementation:**

#### **Responsive Sheet Sizing**
```typescript
// Progressive width sizing for optimal experience
className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none"
```
-   **Mobile First:** Start with 95% width for mobile devices
-   **Progressive Scaling:** Reduce width on larger screens for better content consumption
-   **No Max Width:** Use `!max-w-none` to override default constraints

#### **Structured Layout Pattern**
```typescript
// Standard sheet structure for consistency
<SheetContent className="p-0 gap-0 overflow-hidden">
  <SheetHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
    {/* Fixed header with responsive padding */}
  </SheetHeader>
  
  <div className="flex-1 overflow-y-auto px-4 pb-4 sm:px-6 sm:pb-6">
    {/* Scrollable content with responsive spacing */}
  </div>
</SheetContent>
```

#### **Content Organization**
-   **Card-Based Layout:** Use cards to group related information with consistent spacing
-   **Responsive Typography:** Scale text sizes across breakpoints (`text-xs sm:text-sm md:text-base`)
-   **Flexible Grids:** Use responsive grid layouts (`grid-cols-1 md:grid-cols-3`)
-   **Icon Scaling:** Adjust icon sizes for device type (`h-3 w-3 sm:h-4 sm:w-4`)

#### **Loading States & Error Handling**
```typescript
// Consistent loading patterns
{isLoading ? (
  <div className="flex items-center justify-center py-12 sm:py-16">
    <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
    <span className="ml-2 sm:ml-3 text-sm">Loading...</span>
  </div>
) : content}
```

#### **Action Patterns**
-   **Bottom Actions:** Place primary actions at the bottom with clear visual separation
-   **Responsive Button Layout:** Stack on mobile, inline on desktop (`flex-col sm:flex-row`)
-   **Disabled States:** Properly disable actions during loading/processing
-   **Confirmation Patterns:** Use separate confirmation dialogs for destructive actions

#### **Accessibility & UX**
-   **Focus Management:** Ensure proper focus handling when opening/closing sheets
-   **Keyboard Navigation:** Support standard keyboard interactions
-   **Screen Reader Support:** Provide proper ARIA labels and descriptions
-   **Touch Targets:** Ensure interactive elements meet minimum size requirements on mobile

#### **Performance Optimization**
-   **Lazy Loading:** Load sheet content only when needed
-   **Memoization:** Use `useMemo` and `useCallback` for expensive computations
-   **Conditional Rendering:** Only render heavy components when sheet is open
-   **Data Fetching:** Use React Query for efficient data management with caching

#### **Reusable Components**
-   **Consistent Patterns:** Standardize common UI patterns (headers, actions, loading states)
-   **Prop Interfaces:** Define clear TypeScript interfaces for sheet component props
-   **Composition:** Build complex sheets by composing smaller, focused components
-   **Theme Support:** Ensure all components work properly in both light and dark modes

---

### 9. **Data Flow and State Management in Sheets**

-   **Principle:** Maintain predictable data flow and state synchronization between sheets and parent components.
-   **Implementation:**
    -   **Callback Pattern:** Use callback props for notifying parent components of changes (`onSuccess`, `onClose`, `onUpdate`)
    -   **Optimistic Updates:** Invalidate React Query cache after successful mutations to ensure UI consistency
    -   **Error Boundaries:** Implement proper error handling to prevent sheet crashes from affecting the parent component
    -   **State Cleanup:** Reset component state when sheet closes to prevent stale data issues

---

### 10. **Billing & Subscription Management with Polar.sh**

-   **Principle:** Leverage Better Auth's Polar.sh integration for seamless subscription billing.
-   **Implementation:**
    -   **Checkout Trigger:** Use the `authClient.checkout()` method in pricing components to initiate subscription purchases:
    ```typescript
    await authClient.checkout({
      // Any Polar Product ID can be passed here
      products: ["e651f46d-ac20-4f26-b769-ad088b123df2"],
      // Or, if you setup "products" in the Checkout Config, you can pass the slug
      slug: "professional-monthly",
    });
    ```
    -   **Product Configuration:** Products are pre-configured in the Better Auth setup with slugs matching our pricing tiers (`starter-monthly`, `professional-annual`, etc.)
    -   **Authentication Required:** All checkout flows require authenticated users (`authenticatedUsersOnly: true`)
    -   **Success Handling:** Configure success redirect URL to handle post-purchase flow (`successUrl: "/success?checkout_id={CHECKOUT_ID}"`)
    -   **Pricing Components:** Call the checkout method from pricing tier buttons/cards to enable one-click subscription purchases 