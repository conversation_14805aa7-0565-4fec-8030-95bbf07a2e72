"use server";

import { db } from "@/db/drizzle";
import { accounts, journalEntries, transactions } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, gte, lte, desc } from "drizzle-orm";

export async function getGeneralLedger(fromDateStr?: string, toDateStr?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    const fromDate = fromDateStr ? new Date(fromDateStr) : undefined;
    const toDate = toDateStr ? new Date(toDateStr) : undefined;

    let transactionFilter = [eq(transactions.organizationId, organizationId)];
    if(fromDate) transactionFilter.push(gte(transactions.date, fromDate));
    if(toDate) transactionFilter.push(lte(transactions.date, toDate));

    const allJournalEntries = await db.select({
        transactionId: transactions.id,
        date: transactions.date,
        description: transactions.description,
        accountId: journalEntries.accountId,
        accountName: accounts.name,
        accountType: accounts.type,
        debit: journalEntries.debitAmount,
        credit: journalEntries.creditAmount
    })
    .from(journalEntries)
    .leftJoin(transactions, eq(journalEntries.transactionId, transactions.id))
    .leftJoin(accounts, eq(journalEntries.accountId, accounts.id))
    .where(and(...transactionFilter))
    .orderBy(desc(transactions.date), desc(transactions.id));

    // Group entries by account
    const ledger = allJournalEntries.reduce((acc, entry) => {
        if (!entry.accountId) return acc;
        if (!acc[entry.accountId]) {
            acc[entry.accountId] = {
                accountName: entry.accountName,
                accountType: entry.accountType,
                entries: [],
                debitTotal: 0,
                creditTotal: 0,
                balance: 0
            };
        }

        const accountLedger = acc[entry.accountId];
        accountLedger.entries.push({
            transactionId: entry.transactionId,
            date: entry.date,
            description: entry.description,
            debit: parseFloat(entry.debit ?? "0"),
            credit: parseFloat(entry.credit ?? "0")
        });

        accountLedger.debitTotal += parseFloat(entry.debit ?? "0");
        accountLedger.creditTotal += parseFloat(entry.credit ?? "0");

        return acc;
    }, {} as Record<string, any>);

    // Calculate balances
    for (const accountId in ledger) {
        const accountInfo = await db.query.accounts.findFirst({
            where: eq(accounts.id, accountId)
        });

        if (accountInfo?.normalBalance === 'debit') {
            ledger[accountId].balance = ledger[accountId].debitTotal - ledger[accountId].creditTotal;
        } else {
            ledger[accountId].balance = ledger[accountId].creditTotal - ledger[accountId].debitTotal;
        }
    }

    return ledger;
} 