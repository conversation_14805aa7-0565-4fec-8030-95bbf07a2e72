"use client";

import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import TrialBalance from "./TrialBalance";
import BalanceSheet from "./BalanceSheet";
import IncomeStatement from "./IncomeStatement";
import CashFlowStatement from "./CashFlowStatement";
import BudgetVsActualReport from "./BudgetVsActualReport";
import VendorPerformanceReport from "./VendorPerformanceReport";
import ExpenseAnalysisReport from "./ExpenseAnalysisReport";
import ARAgingSummary from "./ARAgingSummary";
import ARAgingDetail from "./ARAgingDetail";
import APAgingSummary from "./APAgingSummary";
import APAgingDetail from "./APAgingDetail";
import { DateRangePicker } from "@/components/general/date-range-picker";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { FileDown, FileSpreadsheet, ChevronDown } from "lucide-react";
import jsPDF from "jspdf";
import * as XLSX from "xlsx";
import { useSearchParams } from "next/navigation";
import { DateRange } from "react-day-picker";
import DetailedReports from "./DetailedReports";
import { getReportingData } from "@/lib/actions/reporting/get-reporting-data";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";

const REPORT_OPTIONS = [
  { value: "trialBalance", label: "Trial Balance" },
  { value: "balanceSheet", label: "Balance Sheet" },
  { value: "profitLoss", label: "Profit & Loss Statement" },
  { value: "cashFlow", label: "Cash Flow Statement" },
  { value: "budgetVsActual", label: "Budget vs Actual" },
  { value: "vendorPerformance", label: "Vendor Performance Report" },
  { value: "expenseAnalysis", label: "Expense Category Analysis" },
  { value: "arAgingSummary", label: "A/R Aging Summary" },
  { value: "arAgingDetail", label: "A/R Aging Detail" },
  { value: "apAgingSummary", label: "A/P Aging Summary" },
  { value: "apAgingDetail", label: "A/P Aging Detail" },
];

export default function ReportingPageClient({ 
  initialDateRange, 
  organizationName 
}: { 
  initialDateRange?: { from?: string, to?: string };
  organizationName: string;
}) {
  const searchParams = useSearchParams();
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    const from = initialDateRange?.from ?? searchParams.get("from");
    const to = initialDateRange?.to ?? searchParams.get("to");
    if (from && to) {
      return { from: new Date(from), to: new Date(to) };
    }
    return undefined;
  });

  const [selectedReport, setSelectedReport] = useState<string>("trialBalance");

  const { data, isLoading, isError } = useQuery({
    queryKey: ["reportingData", dateRange],
    queryFn: () => getReportingData(dateRange?.from?.toISOString(), dateRange?.to?.toISOString()),
    enabled: !!dateRange?.from && !!dateRange?.to,
  });

  useEffect(() => {
    // Update URL without navigation-based refetching
    if (dateRange?.from && dateRange?.to) {
      const params = new URLSearchParams(window.location.search);
      params.set("from", dateRange.from.toISOString().slice(0, 10));
      params.set("to", dateRange.to.toISOString().slice(0, 10));
      window.history.replaceState(null, "", `/reporting?${params.toString()}`);
    }
  }, [dateRange]);

  function getReportData() {
    if (!data) return null;
    switch (selectedReport) {
      case "trialBalance":
        return data.trialBalance;
      case "balanceSheet":
        return data.balanceSheet;
      case "profitLoss":
        return data.incomeStatement;
      case "cashFlow":
        return data.cashFlowStatement;
      case "budgetVsActual":
        return data.budgetVsActual;
      case "vendorPerformance":
        return data.vendorPerformance;
      case "expenseAnalysis":
        return data.expenseAnalysis;
      case "arAgingSummary":
        return data.arAgingSummary;
      case "arAgingDetail":
        return data.arAgingDetail;
      case "apAgingSummary":
        return data.apAgingSummary;
      case "apAgingDetail":
        return data.apAgingDetail;
      default:
        return null;
    }
  }

  function exportToCSV() {
    const report = getReportData();
    if (!report) return;
    let rows: any[][] = [];
    
    // Add organization header
    rows.push([organizationName]);
    rows.push([]); // Empty row for spacing
    
    // Add date range header
    if (dateRange?.from && dateRange?.to) {
      rows.push([`Report Period: ${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`]);
      rows.push([]); // Empty row for spacing
    }
    
    switch (selectedReport) {
      case "trialBalance":
        if (data?.trialBalance) {
          rows.push(["Trial Balance"]);
          rows.push([]);
          rows.push(["Account", "Account Number", "Debit", "Credit"]);
          data.trialBalance.accounts.forEach((ab: any) => {
            rows.push([
              ab.account?.name || "Unknown",
              ab.account?.account_number || "",
              ab.debit_total > 0 ? `$${ab.debit_total.toFixed(2)}` : "-",
              ab.credit_total > 0 ? `$${ab.credit_total.toFixed(2)}` : "-"
            ]);
          });
          rows.push([]);
          rows.push(["Total", "", `$${data.trialBalance.totalDebits.toFixed(2)}`, `$${data.trialBalance.totalCredits.toFixed(2)}`]);
          
          // Add balance check
          if (data.trialBalance.totalDebits !== data.trialBalance.totalCredits) {
            rows.push([]);
            rows.push(["Warning: Trial balance is not balanced"]);
            rows.push([`Difference: $${Math.abs(data.trialBalance.totalDebits - data.trialBalance.totalCredits).toFixed(2)}`]);
          }
        }
        break;
        
      case "balanceSheet":
        if (data?.balanceSheet) {
          rows.push(["Balance Sheet"]);
          rows.push([]);
          
          // Assets Section
          rows.push(["ASSETS"]);
          rows.push(["Account", "Amount"]);
          data.balanceSheet.assets?.forEach((asset: any) => {
            rows.push([
              asset.account?.name || "Unknown",
              `$${asset.balance.toFixed(2)}`
            ]);
          });
          rows.push(["Total Assets", `$${data.balanceSheet.totalAssets.toFixed(2)}`]);
          rows.push([]);
          
          // Liabilities Section
          rows.push(["LIABILITIES"]);
          rows.push(["Account", "Amount"]);
          data.balanceSheet.liabilities?.forEach((liability: any) => {
            rows.push([
              liability.account?.name || "Unknown",
              `$${liability.balance.toFixed(2)}`
            ]);
          });
          rows.push(["Total Liabilities", `$${data.balanceSheet.totalLiabilities.toFixed(2)}`]);
          rows.push([]);
          
          // Equity Section
          rows.push(["EQUITY"]);
          rows.push(["Account", "Amount"]);
          data.balanceSheet.equity?.forEach((eq: any) => {
            rows.push([
              eq.account?.name || "Unknown",
              `$${eq.balance.toFixed(2)}`
            ]);
          });
          rows.push(["Total Equity", `$${data.balanceSheet.totalEquity.toFixed(2)}`]);
          rows.push([]);
          
          // Total Liabilities & Equity
          rows.push(["Total Liabilities & Equity", `$${(data.balanceSheet.totalLiabilities + data.balanceSheet.totalEquity).toFixed(2)}`]);
          
          // Balance check
          const difference = Math.abs(data.balanceSheet.totalAssets - (data.balanceSheet.totalLiabilities + data.balanceSheet.totalEquity));
          if (difference > 0.01) {
            rows.push([]);
            rows.push(["Warning: Balance sheet is not balanced"]);
            rows.push([`Difference: $${difference.toFixed(2)}`]);
          }
        }
        break;
        
      case "profitLoss":
        if (data?.incomeStatement) {
          rows.push(["Profit & Loss Statement"]);
          rows.push([]);
          
          // Revenue Section
          rows.push(["REVENUE"]);
          rows.push(["Account", "Amount"]);
          data.incomeStatement.revenue?.forEach((rev: any) => {
            rows.push([
              rev.account?.name || "Unknown",
              `$${rev.total.toFixed(2)}`
            ]);
          });
          const totalRevenue = data.incomeStatement.revenue?.reduce((sum: number, rev: any) => sum + (rev.total || 0), 0) || 0;
          rows.push(["Total Revenue", `$${totalRevenue.toFixed(2)}`]);
          rows.push([]);
          
          // Expenses Section
          rows.push(["EXPENSES"]);
          rows.push(["Account", "Amount"]);
          data.incomeStatement.expenses?.forEach((exp: any) => {
            rows.push([
              exp.account?.name || "Unknown",
              `$${exp.total.toFixed(2)}`
            ]);
          });
          const totalExpenses = data.incomeStatement.expenses?.reduce((sum: number, exp: any) => sum + (exp.total || 0), 0) || 0;
          rows.push(["Total Expenses", `$${totalExpenses.toFixed(2)}`]);
          rows.push([]);
          
          // Net Income
          const netIncome = totalRevenue - totalExpenses;
          rows.push(["Net Income", `$${netIncome.toFixed(2)}`]);
        }
        break;
        
      case "cashFlow":
        if (data?.cashFlowStatement) {
          rows.push(["Cash Flow Statement"]);
          rows.push([]);
          
          rows.push(["Operating Activities"]);
          rows.push(["Net Cash from Operating", `$${data.cashFlowStatement.netCashFromOperating.toFixed(2)}`]);
          rows.push([]);
          
          rows.push(["Investing Activities"]);
          rows.push(["Net Cash from Investing", `$${data.cashFlowStatement.netCashFromInvesting.toFixed(2)}`]);
          rows.push([]);
          
          rows.push(["Financing Activities"]);
          rows.push(["Net Cash from Financing", `$${data.cashFlowStatement.netCashFromFinancing.toFixed(2)}`]);
          rows.push([]);
          
          rows.push(["Net Change in Cash", `$${data.cashFlowStatement.netChangeInCash.toFixed(2)}`]);
        }
        break;
        
      case "arAgingSummary":
        if (data?.arAgingSummary) {
          rows.push(["A/R Aging Summary"]);
          rows.push([]);
          rows.push(["Aging Period", "Amount"]);
          rows.push(["Current", `$${data.arAgingSummary.current.toFixed(2)}`]);
          rows.push(["1-30 Days", `$${data.arAgingSummary["1-30"].toFixed(2)}`]);
          rows.push(["31-60 Days", `$${data.arAgingSummary["31-60"].toFixed(2)}`]);
          rows.push(["61-90 Days", `$${data.arAgingSummary["61-90"].toFixed(2)}`]);
          rows.push(["91+ Days", `$${data.arAgingSummary["91+"].toFixed(2)}`]);
          rows.push([]);
          rows.push(["Total Outstanding", `$${data.arAgingSummary.total.toFixed(2)}`]);
        }
        break;
        
      case "arAgingDetail":
        if (data?.arAgingDetail) {
          rows.push(["A/R Aging Detail"]);
          rows.push([]);
          rows.push(["Invoice #", "Client", "Date", "Due Date", "Days Past Due", "Aging", "Amount"]);
          data.arAgingDetail.forEach((inv: any) => {
            rows.push([
              inv.invoiceNumber || "N/A",
              inv.clientName || "Unknown Client",
              inv.date ? new Date(inv.date).toLocaleDateString() : "N/A",
              inv.dueDate ? new Date(inv.dueDate).toLocaleDateString() : "N/A",
              inv.daysPastDue || 0,
              inv.agingBucket || "N/A",
              `$${(inv.amount || 0).toFixed(2)}`
            ]);
          });
        }
        break;
        
      case "apAgingSummary":
        if (data?.apAgingSummary) {
          rows.push(["A/P Aging Summary"]);
          rows.push([]);
          rows.push(["Aging Period", "Amount"]);
          rows.push(["Current", `$${data.apAgingSummary.current.toFixed(2)}`]);
          rows.push(["1-30 Days", `$${data.apAgingSummary["1-30"].toFixed(2)}`]);
          rows.push(["31-60 Days", `$${data.apAgingSummary["31-60"].toFixed(2)}`]);
          rows.push(["61-90 Days", `$${data.apAgingSummary["61-90"].toFixed(2)}`]);
          rows.push(["91+ Days", `$${data.apAgingSummary["91+"].toFixed(2)}`]);
          rows.push([]);
          rows.push(["Total Outstanding", `$${data.apAgingSummary.total.toFixed(2)}`]);
        }
        break;
        
      case "apAgingDetail":
        if (data?.apAgingDetail) {
          rows.push(["A/P Aging Detail"]);
          rows.push([]);
          rows.push(["Bill #", "Vendor", "Date", "Due Date", "Days Past Due", "Aging", "Amount"]);
          data.apAgingDetail.forEach((bill: any) => {
            rows.push([
              bill.billNumber || "N/A",
              bill.vendorName || "Unknown Vendor",
              bill.date ? new Date(bill.date).toLocaleDateString() : "N/A",
              bill.dueDate ? new Date(bill.dueDate).toLocaleDateString() : "N/A",
              bill.daysPastDue || 0,
              bill.agingBucket || "N/A",
              `$${(bill.amount || 0).toFixed(2)}`
            ]);
          });
        }
        break;
        
      case "budgetVsActual":
        if (data?.budgetVsActual) {
          rows.push(["Budget vs Actual Report"]);
          rows.push([]);
          rows.push(["Account", "Budgeted", "Actual", "Variance"]);
          (data.budgetVsActual.report ?? []).forEach((item: any) => {
            rows.push([
              item.accountName || "Unknown",
              `$${(item.budgeted || 0).toFixed(2)}`,
              `$${(item.actual || 0).toFixed(2)}`,
              `$${(item.variance || 0).toFixed(2)}`
            ]);
          });
          rows.push([]);
          rows.push(["Totals", 
            `$${(data.budgetVsActual.totals?.budgeted || 0).toFixed(2)}`,
            `$${(data.budgetVsActual.totals?.actual || 0).toFixed(2)}`,
            `$${(data.budgetVsActual.totals?.variance || 0).toFixed(2)}`
          ]);
        }
        break;
        
      case "vendorPerformance":
        if (data?.vendorPerformance) {
          rows.push(["Vendor Performance Report"]);
          rows.push([]);
          rows.push(["Vendor", "Total Billed", "Bill Count", "Average Bill Amount", "On-Time Payment Rate", "Average Payment Time"]);
          data.vendorPerformance.forEach((vendor: any) => {
            rows.push([
              vendor.vendorName || "Unknown",
              `$${(vendor.totalBilled || 0).toFixed(2)}`,
              vendor.billCount || 0,
              `$${(vendor.averageBillAmount || 0).toFixed(2)}`,
              vendor.onTimePaymentRate || "N/A",
              vendor.averagePaymentTime || "N/A"
            ]);
          });
        }
        break;
        
      case "expenseAnalysis":
        if (data?.expenseAnalysis) {
          rows.push(["Expense Analysis Report"]);
          rows.push([]);
          rows.push(["Total Expenses", `$${(data.expenseAnalysis.totalExpenses || 0).toFixed(2)}`]);
          rows.push([]);
          rows.push(["Categories"]);
          rows.push(["Category", "Amount", "Percentage"]);
          data.expenseAnalysis.report?.forEach((cat: any) => {
            rows.push([
              cat.category || "Unknown",
              `$${(cat.total || 0).toFixed(2)}`,
              `${(cat.percentage || 0).toFixed(1)}%`
            ]);
          });
        }
        break;
        
      default:
        return;
    }

    if (rows.length === 0) return;

    const worksheet = XLSX.utils.aoa_to_sheet(rows);
    const workbook = XLSX.utils.book_new();
    const reportName = REPORT_OPTIONS.find(opt => opt.value === selectedReport)?.label || selectedReport;
    XLSX.utils.book_append_sheet(workbook, worksheet, reportName);
    XLSX.writeFile(workbook, `${organizationName.replace(/[^a-zA-Z0-9]/g, '_')}-${reportName.replace(/\s+/g, '_')}_Report.xlsx`);
  }

  function exportToPDF() {
    const report = getReportData();
    if (!report) return;
    
    const doc = new jsPDF();
    const reportName = REPORT_OPTIONS.find(opt => opt.value === selectedReport)?.label || selectedReport;
    
    // Add header with organization name and date range
    doc.setFontSize(16);
    doc.text(organizationName, 20, 20);
    doc.setFontSize(12);
    doc.text(reportName, 20, 30);
    doc.setFontSize(10);
    if (dateRange?.from && dateRange?.to) {
      doc.text(`Period: ${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`, 20, 40);
    }
    doc.setFontSize(12);
    
    let yPosition = 55;
    const lineHeight = 10;
    
    switch (selectedReport) {
      case "trialBalance":
        if (data?.trialBalance) {
          doc.text("Account", 20, yPosition);
          doc.text("Debit", 100, yPosition);
          doc.text("Credit", 150, yPosition);
          yPosition += lineHeight;
          
          data.trialBalance.accounts.forEach((account: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(account.account?.name || "Unknown", 20, yPosition);
            doc.text((account.debit_total || 0).toLocaleString(), 100, yPosition);
            doc.text((account.credit_total || 0).toLocaleString(), 150, yPosition);
            yPosition += lineHeight;
          });
          
          yPosition += lineHeight;
          doc.text("Total:", 20, yPosition);
          doc.text((data.trialBalance.totalDebits || 0).toLocaleString(), 100, yPosition);
          doc.text((data.trialBalance.totalCredits || 0).toLocaleString(), 150, yPosition);
        }
        break;
        
      case "balanceSheet":
        if (data?.balanceSheet) {
          doc.text("Assets", 20, yPosition);
          yPosition += lineHeight;
          data.balanceSheet.assets?.forEach((asset: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(asset.account?.name || "Unknown", 30, yPosition);
            doc.text(`$${(asset.balance || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
          
          yPosition += lineHeight;
          doc.text("Liabilities", 20, yPosition);
          yPosition += lineHeight;
          data.balanceSheet.liabilities?.forEach((liability: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(liability.account?.name || "Unknown", 30, yPosition);
            doc.text(`$${(liability.balance || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
          
          yPosition += lineHeight;
          doc.text("Equity", 20, yPosition);
          yPosition += lineHeight;
          data.balanceSheet.equity?.forEach((eq: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(eq.account?.name || "Unknown", 30, yPosition);
            doc.text(`$${(eq.balance || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      case "profitLoss":
        if (data?.incomeStatement) {
          doc.text("Revenue", 20, yPosition);
          yPosition += lineHeight;
          data.incomeStatement.revenue?.forEach((rev: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(rev.account?.name || "Unknown", 30, yPosition);
            doc.text(`$${(rev.total || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
          
          yPosition += lineHeight;
          doc.text("Expenses", 20, yPosition);
          yPosition += lineHeight;
          data.incomeStatement.expenses?.forEach((exp: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(exp.account?.name || "Unknown", 30, yPosition);
            doc.text(`$${(exp.total || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      case "arAgingSummary":
        if (data?.arAgingSummary) {
          doc.text("Aging Period", 20, yPosition);
          doc.text("Amount", 120, yPosition);
          yPosition += lineHeight;
          
          Object.entries(data.arAgingSummary).forEach(([period, amount]) => {
            if (period === "total") return;
            doc.text(period, 20, yPosition);
            doc.text(`$${(amount as number || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
          
          yPosition += lineHeight;
          doc.text("Total Outstanding:", 20, yPosition);
          doc.text(`$${(data.arAgingSummary.total || 0).toLocaleString()}`, 120, yPosition);
        }
        break;
        
      case "arAgingDetail":
        if (data?.arAgingDetail) {
          doc.text("Invoice #", 20, yPosition);
          doc.text("Client", 60, yPosition);
          doc.text("Amount", 120, yPosition);
          doc.text("Days Past Due", 150, yPosition);
          yPosition += lineHeight;
          
          data.arAgingDetail.slice(0, 20).forEach((invoice: any) => { // Limit for PDF
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(invoice.invoiceNumber || "N/A", 20, yPosition);
            doc.text((invoice.clientName || "Unknown").slice(0, 15), 60, yPosition);
            doc.text(`$${(invoice.amount || 0).toLocaleString()}`, 120, yPosition);
            doc.text((invoice.daysPastDue || 0).toString(), 150, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      case "apAgingSummary":
        if (data?.apAgingSummary) {
          doc.text("Aging Period", 20, yPosition);
          doc.text("Amount", 120, yPosition);
          yPosition += lineHeight;
          
          Object.entries(data.apAgingSummary).forEach(([period, amount]) => {
            if (period === "total") return;
            doc.text(period, 20, yPosition);
            doc.text(`$${(amount as number || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
          
          yPosition += lineHeight;
          doc.text("Total Outstanding:", 20, yPosition);
          doc.text(`$${(data.apAgingSummary.total || 0).toLocaleString()}`, 120, yPosition);
        }
        break;
        
      case "apAgingDetail":
        if (data?.apAgingDetail) {
          doc.text("Bill #", 20, yPosition);
          doc.text("Vendor", 60, yPosition);
          doc.text("Amount", 120, yPosition);
          doc.text("Days Past Due", 150, yPosition);
          yPosition += lineHeight;
          
          data.apAgingDetail.slice(0, 20).forEach((bill: any) => { // Limit for PDF
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(bill.billNumber || "N/A", 20, yPosition);
            doc.text((bill.vendorName || "Unknown").slice(0, 15), 60, yPosition);
            doc.text(`$${(bill.amount || 0).toLocaleString()}`, 120, yPosition);
            doc.text((bill.daysPastDue || 0).toString(), 150, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      case "cashFlow":
        if (data?.cashFlowStatement) {
          doc.text("Cash Flow Statement", 20, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text("Operating Activities:", 20, yPosition);
          yPosition += lineHeight;
          doc.text(`Net Cash from Operating: $${(data.cashFlowStatement.netCashFromOperating || 0).toLocaleString()}`, 30, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text("Investing Activities:", 20, yPosition);
          yPosition += lineHeight;
          doc.text(`Net Cash from Investing: $${(data.cashFlowStatement.netCashFromInvesting || 0).toLocaleString()}`, 30, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text("Financing Activities:", 20, yPosition);
          yPosition += lineHeight;
          doc.text(`Net Cash from Financing: $${(data.cashFlowStatement.netCashFromFinancing || 0).toLocaleString()}`, 30, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text(`Net Change in Cash: $${(data.cashFlowStatement.netChangeInCash || 0).toLocaleString()}`, 20, yPosition);
        }
        break;
        
      case "budgetVsActual":
        if (data?.budgetVsActual) {
          doc.text("Budget vs Actual Report", 20, yPosition);
          yPosition += lineHeight * 2;
          doc.text("Account", 20, yPosition);
          doc.text("Budgeted", 80, yPosition);
          doc.text("Actual", 120, yPosition);
          doc.text("Variance", 160, yPosition);
          yPosition += lineHeight;
          (data.budgetVsActual.report ?? []).slice(0, 15).forEach((item: any) => { // Limit for PDF
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text((item.accountName ?? '').slice(0, 20), 20, yPosition);
            doc.text(`$${(item.budgeted || 0).toLocaleString()}`, 80, yPosition);
            doc.text(`$${(item.actual || 0).toLocaleString()}`, 120, yPosition);
            doc.text(`$${(item.variance || 0).toLocaleString()}`, 160, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      case "vendorPerformance":
        if (data?.vendorPerformance) {
          doc.text("Vendor Performance Report", 20, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text("Vendor Performance:", 20, yPosition);
          yPosition += lineHeight;
          data.vendorPerformance.slice(0, 10).forEach((vendor: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(vendor.vendorName || "Unknown", 30, yPosition);
            doc.text(`$${(vendor.totalBilled || 0).toLocaleString()}`, 120, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      case "expenseAnalysis":
        if (data?.expenseAnalysis) {
          doc.text("Expense Analysis Report", 20, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text("Total Expenses:", 20, yPosition);
          doc.text(`$${(data.expenseAnalysis.totalExpenses || 0).toLocaleString()}`, 120, yPosition);
          yPosition += lineHeight * 2;
          
          doc.text("Categories:", 20, yPosition);
          yPosition += lineHeight;
          data.expenseAnalysis.report?.slice(0, 15).forEach((cat: any) => {
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(cat.category || "Unknown", 30, yPosition);
            doc.text(`$${(cat.total || 0).toLocaleString()}`, 120, yPosition);
            doc.text(`${(cat.percentage || 0).toFixed(1)}%`, 150, yPosition);
            yPosition += lineHeight;
          });
        }
        break;
        
      default:
        doc.text("This report type is not yet supported for PDF export.", 20, yPosition);
        break;
    }
    
    doc.save(`${organizationName.replace(/[^a-zA-Z0-9]/g, '_')}-${reportName.replace(/\s+/g, '_')}_Report.pdf`);
  }
  
  const renderContent = () => {
    if (isLoading) {
      return <Skeleton className="h-96 w-full" />;
    }
    if (isError) {
      return <div className="text-red-500 text-center p-8">Failed to load reporting data.</div>;
    }
    if (!dateRange?.from || !dateRange?.to) {
      return <div className="text-center p-8">Please select a date range to view reports.</div>;
    }
    if (!data) {
      return <div className="text-center p-8">No data available for the selected date range.</div>;
    }

    switch (selectedReport) {
      case "trialBalance":
        return data.trialBalance ? <TrialBalance data={data.trialBalance} /> : <div>No trial balance data available</div>;
      
      case "balanceSheet":
        return data.balanceSheet ? <BalanceSheet data={data.balanceSheet} /> : <div>No balance sheet data available</div>;
      
      case "profitLoss":
        return data.incomeStatement ? <IncomeStatement data={data.incomeStatement} /> : <div>No profit & loss data available</div>;
      
      case "cashFlow":
        return data.cashFlowStatement ? <CashFlowStatement data={data.cashFlowStatement} /> : <div>No cash flow data available</div>;
      
      case "budgetVsActual":
        return data.budgetVsActual ? (
          <BudgetVsActualReport
            data={{
              lineItems: (data.budgetVsActual.report ?? []).map((item: any) => ({
                accountId: item.accountId || '',
                accountName: item.accountName || '',
                accountType: item.accountType || '',
                budgetedAmount: item.budgeted ?? 0,
                actualAmount: item.actual ?? 0,
                variance: item.variance ?? 0,
                variancePercent: 0,
                period: data.budgetVsActual.period?.name || ''
              })),
              summary: data.budgetVsActual.totals
                ? {
                    totalBudgetedRevenue: data.budgetVsActual.totals.budgeted ?? 0,
                    totalActualRevenue: data.budgetVsActual.totals.actual ?? 0,
                    revenueVariance: data.budgetVsActual.totals.variance ?? 0,
                    revenueVariancePercent: 0,
                    totalBudgetedExpenses: 0,
                    totalActualExpenses: 0,
                    expenseVariance: 0,
                    expenseVariancePercent: 0,
                    budgetedNetIncome: 0,
                    actualNetIncome: 0,
                    netIncomeVariance: 0,
                    netIncomeVariancePercent: 0,
                  }
                : {
                    totalBudgetedRevenue: 0,
                    totalActualRevenue: 0,
                    revenueVariance: 0,
                    revenueVariancePercent: 0,
                    totalBudgetedExpenses: 0,
                    totalActualExpenses: 0,
                    expenseVariance: 0,
                    expenseVariancePercent: 0,
                    budgetedNetIncome: 0,
                    actualNetIncome: 0,
                    netIncomeVariance: 0,
                    netIncomeVariancePercent: 0,
                  },
              period: data.budgetVsActual.period?.name || ''
            }}
          />
        ) : <div>No budget vs actual data available</div>;
      
      case "vendorPerformance":
        // Map backend data to expected Vendor interface
        const vendorPerf = {
          vendors: (data.vendorPerformance || []).map((vendor: any) => ({
            vendorId: vendor.vendorId || '',
            vendorName: vendor.vendorName || '',
            totalSpent: vendor.totalBilled || 0,
            invoiceCount: vendor.billCount || 0,
            averageInvoiceAmount: vendor.averageBillAmount || 0,
            paymentTerms: 'N/A',
            averagePaymentDays: 0,
            discountsTaken: 0,
            discountsAvailable: 0,
            discountRate: 0,
            onTimePaymentRate: 0,
            category: 'General'
          })),
          totalSpent: (data.vendorPerformance || []).reduce((sum: number, vendor: any) => sum + (vendor.totalBilled || 0), 0),
          totalInvoices: (data.vendorPerformance || []).reduce((sum: number, vendor: any) => sum + (vendor.billCount || 0), 0),
          averagePaymentDays: 0,
          totalDiscountsTaken: 0,
          totalDiscountsAvailable: 0,
          period: ''
        };
        return <VendorPerformanceReport data={vendorPerf} />;
      
      case "expenseAnalysis":
        return data.expenseAnalysis ? (
          <ExpenseAnalysisReport
            data={{
              categories: (data.expenseAnalysis.report ?? []).map((cat: any) => ({
                category: cat.category ?? '',
                total: cat.total ?? 0,
                percentage: cat.percentage ?? 0,
                currentPeriod: 0,
                previousPeriod: 0,
                budgeted: 0,
                variance: 0,
                variancePercent: 0,
                period: '',
                percentOfTotal: cat.percentOfTotal ?? 0,
                trend: cat.trend ?? 0,
                subcategories: cat.subcategories ?? [],
              })),
              totalExpenses: data.expenseAnalysis.totalExpenses ?? 0,
              totalBudgeted: 0,
              totalVariance: 0,
              totalVariancePercent: 0,
              period: ''
            }}
          />
        ) : <div>No expense analysis data available</div>;
      
      case "arAgingSummary":
        return data.arAgingSummary ? (
          <ARAgingSummary
            data={{
              agingBuckets: {
                current: data.arAgingSummary.current ?? 0,
                '1-30': data.arAgingSummary['1-30'] ?? 0,
                '31-60': data.arAgingSummary['31-60'] ?? 0,
                '61-90': data.arAgingSummary['61-90'] ?? 0,
                '90+': data.arAgingSummary['91+'] ?? 0,
              },
              totalOutstanding: data.arAgingSummary.total ?? 0,
              totalInvoices: 0
            }}
          />
        ) : <div>No A/R aging summary data available</div>;
      
      case "arAgingDetail":
        return data.arAgingDetail ? (
          <ARAgingDetail
            data={{
              invoices: (data.arAgingDetail ?? []).map((inv: any) => ({
                id: inv.invoiceId || '',
                invoiceNumber: inv.invoiceNumber || '',
                date: inv.invoiceDate || null,
                dueDate: inv.dueDate || null,
                clientId: inv.clientId || '',
                clientName: inv.clientName || '',
                total: inv.total || '',
                status: inv.status || '',
                daysPastDue: inv.daysPastDue || 0,
                agingBucket: inv.agingBucket || inv.bucket || '',
                amount: inv.amount || 0
              })),
              totalAmount: (data.arAgingDetail ?? []).reduce((sum: number, inv: any) => sum + (inv.amount ?? 0), 0)
            }}
          />
        ) : <div>No A/R aging detail data available</div>;
      
      case "apAgingSummary":
        return data.apAgingSummary ? (
          <APAgingSummary
            data={{
              agingBuckets: {
                current: data.apAgingSummary.current ?? 0,
                '1-30': data.apAgingSummary['1-30'] ?? 0,
                '31-60': data.apAgingSummary['31-60'] ?? 0,
                '61-90': data.apAgingSummary['61-90'] ?? 0,
                '90+': data.apAgingSummary['91+'] ?? 0,
              },
              totalOutstanding: data.apAgingSummary.total ?? 0,
              totalBills: 0
            }}
          />
        ) : <div>No A/P aging summary data available</div>;
      
      case "apAgingDetail":
        return data.apAgingDetail ? (
          <APAgingDetail
            data={{
              bills: data.apAgingDetail ?? [],
              totalAmount: (data.apAgingDetail ?? []).reduce((sum: number, bill: any) => sum + (bill.amount ?? 0), 0)
            }}
          />
        ) : <div>No A/P aging detail data available</div>;
      
      default:
        return <div className="text-center p-8">Please select a report type to view.</div>;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between mb-4">
        <DateRangePicker value={dateRange} onDateRangeChange={setDateRange} />
        <div className="flex gap-2 items-center">
          <select
            className="border rounded px-2 py-1 bg-black/80 text-white"
            value={selectedReport}
            onChange={e => setSelectedReport(e.target.value)}
          >
            {REPORT_OPTIONS.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" disabled={!data}>
                <FileDown className="w-4 h-4 mr-2" />
                Export to
                <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={exportToCSV}>
                <FileSpreadsheet className="w-4 h-4 mr-2" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToPDF}>
                <FileDown className="w-4 h-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {renderContent()}
    </div>
  );
} 