"use client";

import { useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Upgrade, Lock, Users, FileText } from "lucide-react";
import { authClient } from "@/lib/auth-client";

interface FeatureGateProps {
  feature?: string;
  checkType?: "feature" | "user_limit" | "transaction_limit";
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

interface PlanInfo {
  planType: string;
  planLimits: {
    users: number;
    transactions: number;
    features: string[];
  };
  usage: {
    users: {
      current: number;
      limit: number;
      percentage: number;
    };
    transactions: {
      current: number;
      limit: number;
      percentage: number;
    };
  };
  status: {
    isActive: boolean;
    planStatus: string;
    trialEndsAt?: string;
    gracePeriodEndsAt?: string;
  };
}

export function FeatureGate({
  feature,
  checkType = "feature",
  children,
  fallback,
  showUpgradePrompt = true,
}: FeatureGateProps) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [reason, setReason] = useState<string>("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkAccess() {
      try {
        const response = await fetch("/api/feature-gates/check", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            feature,
            checkType,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setHasAccess(data.allowed);
          setReason(data.reason || "");
        } else {
          setHasAccess(false);
          setReason("Unable to verify access");
        }
      } catch (error) {
        console.error("Error checking feature access:", error);
        setHasAccess(false);
        setReason("Error checking access");
      } finally {
        setLoading(false);
      }
    }

    checkAccess();
  }, [feature, checkType]);

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return <UpgradePrompt reason={reason} feature={feature} checkType={checkType} />;
  }

  return null;
}

export function UpgradePrompt({
  reason,
  feature,
  checkType,
}: {
  reason: string;
  feature?: string;
  checkType?: string;
}) {
  const handleUpgrade = async () => {
    try {
      await authClient.checkout({
        products: ["professional-monthly"],
        slug: "professional-monthly",
      });
    } catch (error) {
      console.error("Upgrade error:", error);
    }
  };

  const getIcon = () => {
    switch (checkType) {
      case "user_limit":
        return <Users className="w-5 h-5" />;
      case "transaction_limit":
        return <FileText className="w-5 h-5" />;
      default:
        return <Lock className="w-5 h-5" />;
    }
  };

  const getTitle = () => {
    switch (checkType) {
      case "user_limit":
        return "User Limit Reached";
      case "transaction_limit":
        return "Transaction Limit Reached";
      default:
        return "Feature Not Available";
    }
  };

  return (
    <Card className="border-dashed">
      <CardHeader className="text-center pb-2">
        <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
          {getIcon()}
        </div>
        <CardTitle className="text-lg">{getTitle()}</CardTitle>
        <CardDescription>{reason}</CardDescription>
      </CardHeader>
      <CardContent className="text-center space-y-3">
        <Badge variant="outline" className="text-xs">
          Upgrade Required
        </Badge>
        <Button onClick={handleUpgrade} className="w-full" size="sm">
          <Upgrade className="w-4 h-4 mr-2" />
          Upgrade Plan
        </Button>
      </CardContent>
    </Card>
  );
}

export function PlanUsageCard() {
  const [planInfo, setPlanInfo] = useState<PlanInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchPlanInfo() {
      try {
        const response = await fetch("/api/organization/plan-info", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          setPlanInfo(data);
        }
      } catch (error) {
        console.error("Error fetching plan info:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchPlanInfo();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="h-2 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!planInfo) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">
            {planInfo.planType.charAt(0).toUpperCase() + planInfo.planType.slice(1)} Plan
          </CardTitle>
          <Badge variant={planInfo.status.isActive ? "secondary" : "destructive"}>
            {planInfo.status.planStatus}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Users Usage */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="flex items-center">
              <Users className="w-4 h-4 mr-1" />
              Users
            </span>
            <span className="text-muted-foreground">
              {planInfo.usage.users.current} / {planInfo.usage.users.limit === Infinity ? "∞" : planInfo.usage.users.limit}
            </span>
          </div>
          {planInfo.usage.users.limit !== Infinity && (
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  planInfo.usage.users.percentage > 80 ? "bg-red-500" : 
                  planInfo.usage.users.percentage > 60 ? "bg-yellow-500" : "bg-green-500"
                }`}
                style={{ width: `${Math.min(planInfo.usage.users.percentage, 100)}%` }}
              ></div>
            </div>
          )}
        </div>

        {/* Transactions Usage */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="flex items-center">
              <FileText className="w-4 h-4 mr-1" />
              Transactions
            </span>
            <span className="text-muted-foreground">
              {planInfo.usage.transactions.current} / {planInfo.usage.transactions.limit === Infinity ? "∞" : planInfo.usage.transactions.limit}
            </span>
          </div>
          {planInfo.usage.transactions.limit !== Infinity && (
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  planInfo.usage.transactions.percentage > 80 ? "bg-red-500" : 
                  planInfo.usage.transactions.percentage > 60 ? "bg-yellow-500" : "bg-green-500"
                }`}
                style={{ width: `${Math.min(planInfo.usage.transactions.percentage, 100)}%` }}
              ></div>
            </div>
          )}
        </div>

        {/* Upgrade button if nearing limits */}
        {(planInfo.usage.users.percentage > 80 || planInfo.usage.transactions.percentage > 80) && (
          <Button size="sm" className="w-full" onClick={() => {
            authClient.checkout({
              products: ["professional-monthly"],
              slug: "professional-monthly",
            });
          }}>
            <Upgrade className="w-4 h-4 mr-2" />
            Upgrade Plan
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

export function RequireFeature({
  feature,
  children,
  fallback,
}: {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <FeatureGate feature={feature} fallback={fallback}>
      {children}
    </FeatureGate>
  );
}

export function RequireUserLimit({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <FeatureGate checkType="user_limit" fallback={fallback}>
      {children}
    </FeatureGate>
  );
}

export function RequireTransactionLimit({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <FeatureGate checkType="transaction_limit" fallback={fallback}>
      {children}
    </FeatureGate>
  );
} 