"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

export async function getProductById(productId: string) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const product = await db.query.products.findFirst({
        where: and(eq(products.id, productId), eq(products.organizationId, orgId)),
        with: {
            revenueAccount: true,
            inventoryAccount: true,
            category: true,
        }
    });

    return product;
  } catch (error) {
    console.error("Failed to fetch product by id:", error);
    return null;
  }
} 