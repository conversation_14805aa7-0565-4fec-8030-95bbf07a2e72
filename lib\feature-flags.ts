/**
 * Feature Flag System for NextGen Business
 * Simple but scalable feature flag management
 */

export interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  userPercentage?: number; // For gradual rollout (0-100)
  userIds?: string[]; // Specific user targeting
  createdAt: Date;
  updatedAt: Date;
}

// Default feature flags
const DEFAULT_FLAGS: Record<string, FeatureFlag> = {
  new_onboarding_flow: {
    key: 'new_onboarding_flow',
    name: 'New Onboarding Flow',
    description: 'Enable the redesigned onboarding flow with business type selection',
    enabled: true, // Enable by default for dev
    userPercentage: 100, // All users
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  subscription_first_flow: {
    key: 'subscription_first_flow', 
    name: 'Subscription First Flow',
    description: 'Legacy onboarding flow requiring subscription before organization setup',
    enabled: false, // Disable legacy flow
    userPercentage: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
};

/**
 * Check if a feature flag is enabled for a user
 */
export async function isFeatureEnabled(
  flagKey: string, 
  userId?: string,
  userEmail?: string
): Promise<boolean> {
  try {
    // 1. Check environment variable override (highest priority)
    const envKey = `FEATURE_${flagKey.toUpperCase()}`;
    const envValue = process.env[envKey];
    if (envValue !== undefined) {
      return envValue.toLowerCase() === 'true';
    }

    // 2. Check database (if implemented)
    const dbFlag = await getFeatureFlagFromDb(flagKey);
    if (dbFlag) {
      return evaluateFlag(dbFlag, userId, userEmail);
    }

    // 3. Fall back to default flags
    const defaultFlag = DEFAULT_FLAGS[flagKey];
    if (defaultFlag) {
      return evaluateFlag(defaultFlag, userId, userEmail);
    }

    // 4. Default to false if flag doesn't exist
    return false;
  } catch (error) {
    console.error(`Error checking feature flag ${flagKey}:`, error);
    // Fail safely - return default flag value or false
    return DEFAULT_FLAGS[flagKey]?.enabled || false;
  }
}

/**
 * Evaluate flag conditions (percentage rollout, user targeting)
 */
function evaluateFlag(flag: FeatureFlag, userId?: string, userEmail?: string): boolean {
  if (!flag.enabled) {
    return false;
  }

  // Check specific user targeting
  if (flag.userIds && userId) {
    return flag.userIds.includes(userId);
  }

  // Check percentage rollout
  if (flag.userPercentage !== undefined && flag.userPercentage < 100) {
    if (!userId) return false;
    
    // Use consistent hash of userId to determine if user is in rollout
    const hash = simpleHash(userId);
    const userPercentile = hash % 100;
    return userPercentile < flag.userPercentage;
  }

  return flag.enabled;
}

/**
 * Simple hash function for consistent user bucketing
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Get feature flag from database (placeholder for future implementation)
 */
async function getFeatureFlagFromDb(flagKey: string): Promise<FeatureFlag | null> {
  // TODO: Implement database storage when needed
  // For now, return null to use defaults
  return null;
}

/**
 * Set feature flag (for admin/development use)
 */
export async function setFeatureFlag(
  flagKey: string, 
  enabled: boolean, 
  options?: {
    userPercentage?: number;
    userIds?: string[];
    description?: string;
  }
): Promise<void> {
  try {
    // For dev environment, we'll use environment variables
    // In production, this would update the database
    
    console.log(`Feature flag updated: ${flagKey} = ${enabled}`, options);
    
    // TODO: Implement database storage
    // await updateFeatureFlagInDb(flagKey, enabled, options);
  } catch (error) {
    console.error(`Error setting feature flag ${flagKey}:`, error);
    throw error;
  }
}

/**
 * Get all feature flags (for admin dashboard)
 */
export async function getAllFeatureFlags(): Promise<Record<string, FeatureFlag>> {
  try {
    // TODO: Get from database when implemented
    // For now, return defaults with any environment overrides
    const flags = { ...DEFAULT_FLAGS };
    
    // Apply environment variable overrides
    Object.keys(flags).forEach(key => {
      const envKey = `FEATURE_${key.toUpperCase()}`;
      const envValue = process.env[envKey];
      if (envValue !== undefined) {
        flags[key] = {
          ...flags[key],
          enabled: envValue.toLowerCase() === 'true',
          updatedAt: new Date(),
        };
      }
    });
    
    return flags;
  } catch (error) {
    console.error('Error getting feature flags:', error);
    return DEFAULT_FLAGS;
  }
}

/**
 * React hook for using feature flags in components
 */
export function useFeatureFlag(flagKey: string, userId?: string, userEmail?: string) {
  // Note: This is a simplified version for server components
  // In a real app, you might want to use React Query or SWR for caching
  return {
    isEnabled: async () => await isFeatureEnabled(flagKey, userId, userEmail),
    flagKey,
  };
}

/**
 * Middleware helper for checking feature flags in API routes
 */
export async function requireFeatureFlag(flagKey: string, userId?: string) {
  const enabled = await isFeatureEnabled(flagKey, userId);
  if (!enabled) {
    throw new Error(`Feature '${flagKey}' is not enabled for this user`);
  }
  return true;
}

// Export commonly used flags for type safety
export const FEATURE_FLAGS = {
  NEW_ONBOARDING_FLOW: 'new_onboarding_flow',
  SUBSCRIPTION_FIRST_FLOW: 'subscription_first_flow',
} as const;

export type FeatureFlagKey = typeof FEATURE_FLAGS[keyof typeof FEATURE_FLAGS]; 