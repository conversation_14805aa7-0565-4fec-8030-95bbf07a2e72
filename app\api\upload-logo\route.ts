import { NextRequest, NextResponse } from "next/server";
import { writeFile } from "fs/promises";
import path from "path";

export async function POST(request: NextRequest) {
  const formData = await request.formData();
  const file = formData.get("file") as File | null;
  if (!file) {
    return NextResponse.json({ error: "No file uploaded" }, { status: 400 });
  }
  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);
  const ext = file.name.split(".").pop();
  const fileName = `org-logo-${Date.now()}.${ext}`;
  const filePath = path.join(process.cwd(), "public", "uploads", "org-logos", fileName);
  await writeFile(filePath, buffer);
  const publicUrl = `/uploads/org-logos/${fileName}`;
  return NextResponse.json({ url: publicUrl });
} 