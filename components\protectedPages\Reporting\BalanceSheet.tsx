"use client";
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface BalanceSheetProps {
  data: {
    assets: any[];
    liabilities: any[];
    equity: any[];
    totalAssets: number;
    totalLiabilities: number;
    totalEquity: number;
  };
  loading?: boolean;
}

const BalanceSheet = ({ data, loading = false }: BalanceSheetProps) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  const isEmpty =
    data.assets.length === 0 &&
    data.liabilities.length === 0 &&
    data.equity.length === 0;

  if (isEmpty) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-white">Balance Sheet</h2>
        <div className="text-center py-8 bg-black/50 rounded-lg border border-white/10">
          <div className="max-w-md mx-auto space-y-4 text-gray-300">
            <p className="text-lg">No financial data available yet</p>
            <p>To get started with your balance sheet:</p>
            <ul className="list-disc list-inside text-left space-y-2">
              <li>Set up your Chart of Accounts</li>
              <li>Add your initial account balances</li>
              <li>Record your transactions</li>
            </ul>
            <Button asChild className="mt-4">
              <a href="/accounting/chart-of-accounts">
                <PlusCircle className="mr-2 h-4 w-4" />
                Set Up Chart of Accounts
              </a>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white">Balance Sheet</h2>
      <div className="grid grid-cols-2 gap-6">
        {/* Assets */}
        <div>
          <h3 className="text-lg font-medium text-white mb-2">Assets</h3>
          {data.assets.length === 0 ? (
            <div className="text-center py-4 bg-black/30 rounded-lg border border-white/10">
              <p className="text-gray-300">No asset accounts found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Account</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="text-gray-300">
                {data.assets.map((ab) => (
                  <TableRow key={ab.account.id}>
                    <TableCell>{ab.account.name}</TableCell>
                    <TableCell className="text-right">
                      ${ab.balance.toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className="font-bold text-white">
                  <TableCell>Total Assets</TableCell>
                  <TableCell className="text-right">
                    ${data.totalAssets.toFixed(2)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          )}
        </div>
        {/* Liabilities & Equity */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-white mb-2">Liabilities</h3>
            {data.liabilities.length === 0 ? (
              <div className="text-center py-4 bg-black/30 rounded-lg border border-white/10">
                <p className="text-gray-300">No liability accounts found</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="text-gray-300">
                  {data.liabilities.map((ab) => (
                    <TableRow key={ab.account.id}>
                      <TableCell>{ab.account.name}</TableCell>
                      <TableCell className="text-right">
                        ${ab.balance.toFixed(2)}
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow className="font-bold text-white">
                    <TableCell>Total Liabilities</TableCell>
                    <TableCell className="text-right">
                      ${data.totalLiabilities.toFixed(2)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            )}
          </div>
          <div>
            <h3 className="text-lg font-medium text-white mb-2">Equity</h3>
            {data.equity.length === 0 ? (
              <div className="text-center py-4 bg-black/30 rounded-lg border border-white/10">
                <p className="text-gray-300">No equity accounts found</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="text-gray-300">
                  {data.equity.map((ab) => (
                    <TableRow key={ab.account.id}>
                      <TableCell>{ab.account.name}</TableCell>
                      <TableCell className="text-right">
                        ${ab.balance.toFixed(2)}
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow className="font-bold text-white">
                    <TableCell>Total Equity</TableCell>
                    <TableCell className="text-right">
                      ${data.totalEquity.toFixed(2)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            )}
          </div>
          {(data.liabilities.length > 0 || data.equity.length > 0) && (
            <Table>
              <TableBody>
                <TableRow className="font-bold text-lg text-white">
                  <TableCell>Total Liabilities & Equity</TableCell>
                  <TableCell className="text-right">
                    ${(data.totalLiabilities + data.totalEquity).toFixed(2)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          )}
        </div>
      </div>
      {Math.abs(data.totalAssets - (data.totalLiabilities + data.totalEquity)) > 0.01 && (
        <div className="text-red-500 text-sm">
          Warning: Balance sheet is not balanced. Difference: $
          {Math.abs(data.totalAssets - (data.totalLiabilities + data.totalEquity)).toFixed(2)}
        </div>
      )}
    </div>
  );
};

export default BalanceSheet; 