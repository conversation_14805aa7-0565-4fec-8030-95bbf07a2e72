"use server";

import { db } from "@/db/drizzle";
import { organizationSessions } from "@/db/schema/schema";
import { eq, and, sql } from "drizzle-orm";
import { getAuditContext, getSecurityContext } from "./context";

/**
 * Track organization session activity
 */
export async function trackSessionActivity(
  action: "start" | "activity" | "end" = "activity",
): Promise<void> {
  try {
    const { userId, organizationId, sessionId } = await getAuditContext();

    if (!userId || !organizationId || !sessionId) {
      return;
    }

    const context = await getSecurityContext();

    if (action === "start") {
      // Create new session record
      await db.insert(organizationSessions).values({
        organizationId,
        userId,
        sessionId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        location: context.location
          ? JSON.parse(JSON.stringify(context.location))
          : null,
        pageViews: 1,
        actionsPerformed: 0,
      });
    } else if (action === "activity") {
      // Update existing session
      await db
        .update(organizationSessions)
        .set({
          lastActivity: new Date(),
          pageViews: sql`page_views + 1`,
        })
        .where(
          and(
            eq(organizationSessions.organizationId, organizationId),
            eq(organizationSessions.userId, userId),
            eq(organizationSessions.sessionId, sessionId),
            eq(organizationSessions.isActive, true),
          ),
        );
    } else if (action === "end") {
      // End session
      await db
        .update(organizationSessions)
        .set({
          endedAt: new Date(),
          isActive: false,
        })
        .where(
          and(
            eq(organizationSessions.organizationId, organizationId),
            eq(organizationSessions.userId, userId),
            eq(organizationSessions.sessionId, sessionId),
          ),
        );
    }
  } catch (error) {
    console.error("Failed to track session activity:", error);
    // Don't throw
  }
}

/**
 * Increment the actions_performed count for the current session
 */
export async function incrementSessionActions(): Promise<void> {
  try {
    const { userId, organizationId, sessionId } = await getAuditContext();

    if (!userId || !organizationId || !sessionId) {
      return;
    }

    await db
      .update(organizationSessions)
      .set({
        actionsPerformed: sql`actions_performed + 1`,
        lastActivity: new Date(),
      })
      .where(
        and(
          eq(organizationSessions.organizationId, organizationId),
          eq(organizationSessions.userId, userId),
          eq(organizationSessions.sessionId, sessionId),
          eq(organizationSessions.isActive, true),
        ),
      );
  } catch (error) {
    console.error("Failed to increment session actions:", error);
    // Don't throw
  }
} 