"use client";

import { useEffect, useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";

interface Org {
  organizationId: string;
  name: string;
  role: string;
  status: string;
  joinedAt: string;
}

interface SuperadminUserOrgsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
  userName: string;
}

export function SuperadminUserOrgsModal({ open, onOpenChange, userId, userName }: SuperadminUserOrgsModalProps) {
  const [orgs, setOrgs] = useState<Org[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!open) return;
    setLoading(true);
    setError(null);
    fetch(`/api/supermegacoolpanel/get-user-orgs?userId=${userId}`)
      .then(res => {
        if (!res.ok) throw new Error("Failed to fetch organizations");
        return res.json();
      })
      .then(setOrgs)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, [open, userId]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl w-full">
        <DialogTitle>Organizations for {userName}</DialogTitle>
        {loading ? (
          <div>Loading organizations...</div>
        ) : error ? (
          <div className="text-destructive">{error}</div>
        ) : orgs.length === 0 ? (
          <div className="text-muted-foreground">No organizations found for this user.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Joined</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orgs.map((org) => (
                <TableRow key={org.organizationId}>
                  <TableCell>{org.name}</TableCell>
                  <TableCell>{org.role}</TableCell>
                  <TableCell>{org.status}</TableCell>
                  <TableCell>{new Date(org.joinedAt).toLocaleDateString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </DialogContent>
    </Dialog>
  );
} 