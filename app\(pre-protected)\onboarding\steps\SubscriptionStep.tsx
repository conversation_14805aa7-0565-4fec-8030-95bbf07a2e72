"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { ArrowLeft, Check, Star, Zap, Shield } from "lucide-react";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { useDebouncedCallback } from 'use-debounce';
import { updateOnboardingStep } from "@/lib/actions/onboarding/updateOnboardingStep";
import { ONBOARDING_STEPS } from "@/lib/onboarding-constants";

interface SubscriptionStepProps {
  onNext: (data: any) => void;
  onPrevious: () => void;
  isLoading: boolean;
}

interface PlanOption {
  id: string;
  name: string;
  price: number;
  interval: string;
  description: string;
  features: string[];
  recommended?: boolean;
  icon: any;
  limits: {
    transactions: string;
    users: string;
    storage: string;
  };
}

const plans: PlanOption[] = [
  {
    id: "starter",
    name: "Starter",
    price: 29,
    interval: "month",
    description: "Perfect for testing our accounting automation",
    icon: Zap,
    features: [
      "Automated transaction categorization",
      "Basic financial reporting",
      "Email support",
      "Bank account connections",
      "Invoice management"
    ],
    limits: {
      transactions: "50/month",
      users: "1 user",
      storage: "5GB"
    }
  },
  {
    id: "professional",
    name: "Professional",
    price: 129,
    interval: "month",
    description: "Ideal for freelancers and consultants",
    icon: Star,
    recommended: true,
    features: [
      "Everything in Starter",
      "Project tracking & profitability",
      "Client collaboration portal",
      "Advanced financial reporting",
      "Priority support",
      "Custom invoice branding"
    ],
    limits: {
      transactions: "500/month",
      users: "3 users",
      storage: "25GB"
    }
  },
  {
    id: "business",
    name: "Business",
    price: 499,
    interval: "month",
    description: "For growing businesses with complex needs",
    icon: Shield,
    features: [
      "Everything in Professional",
      "Multi-location support",
      "Advanced analytics dashboard",
      "Team management & permissions",
      "API access",
      "Dedicated account manager"
    ],
    limits: {
      transactions: "2,500/month",
      users: "15 users",
      storage: "100GB"
    }
  },
  {
    id: "unlimited",
    name: "Unlimited",
    price: 999,
    interval: "month",
    description: "Enterprise-grade with no limits",
    icon: Shield,
    features: [
      "Everything in Business",
      "Unlimited users & transactions",
      "White-glove onboarding",
      "Custom development",
      "24/7 dedicated support",
      "SLA guarantee (99.9% uptime)"
    ],
    limits: {
      transactions: "Unlimited",
      users: "Unlimited",
      storage: "Unlimited"
    }
  }
];

export function SubscriptionStep({ 
  onNext, 
  onPrevious, 
  isLoading 
}: SubscriptionStepProps) {
  const { updateData, data: onboardingData } = useOnboardingStore();
  // Initialize from onboarding store if available
  const initialPlan = onboardingData?.subscription?.planType || "professional";
  const initialIsAnnual = onboardingData?.subscription?.interval === "annual";
  const [selectedPlan, setSelectedPlan] = useState<string>(initialPlan);
  const [isAnnual, setIsAnnual] = useState(initialIsAnnual);

  // Debounced auto-save for subscription
  const debouncedAutoSave = useDebouncedCallback((planId: string, annual: boolean) => {
    const plan = plans.find(p => p.id === planId);
    if (plan) {
      const subscriptionData = {
        planType: plan.id,
        interval: annual ? "annual" : "monthly",
        price: getPrice(plan),
        features: plan.features,
        limits: plan.limits,
        recommended: plan.recommended || false
      };
      updateData({ subscription: subscriptionData });
    }
  }, 2000);

  useEffect(() => {
    debouncedAutoSave(selectedPlan, isAnnual);
  }, [selectedPlan, isAnnual, debouncedAutoSave]);

  // Update onboarding store live on plan or interval change
  const handlePlanChange = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleIntervalChange = (annual: boolean) => {
    setIsAnnual(annual);
  };

  const handleNext = async () => {
    const plan = plans.find(p => p.id === selectedPlan);
    if (plan) {
      const subscriptionData = {
        planType: plan.id,
        interval: isAnnual ? "annual" : "monthly",
        price: getPrice(plan),
        features: plan.features,
        limits: plan.limits,
        recommended: plan.recommended || false
      };
      // Merge orgDetails fields into organization for review step
      const orgDetails: any = onboardingData.orgDetails || {};
      const organization = {
        ...onboardingData.organization,
        email: orgDetails.email,
        industry: orgDetails.industry,
        website: orgDetails.website,
        legalEntity: orgDetails.legalEntity,
      };
      const mergedData = {
        ...onboardingData,
        organization,
        subscription: subscriptionData,
      };
      // Persist to backend
      if (onboardingData.userId) {
        try {
          await updateOnboardingStep(
            onboardingData.userId,
            ONBOARDING_STEPS.SUBSCRIPTION,
            mergedData
          );
          // Update the onboarding store with the merged data so the review step has all info
          updateData(mergedData);
        } catch (err) {
          console.error("Failed to persist subscription step:", err);
        }
      }
      onNext({ planType: plan.id, interval: isAnnual ? "annual" : "monthly" });
    }
  };

  const getPrice = (plan: PlanOption) => {
    // Annual pricing from homepage: roughly 10/12 of monthly (about 17% savings)
    const annualPricing: Record<string, number> = {
      starter: 290,
      professional: 1290, 
      business: 4990,
      unlimited: 9990
    };
    
    if (isAnnual) {
      const annualPrice = annualPricing[plan.id] || plan.price * 10;
      return Math.floor(annualPrice / 12);
    }
    return plan.price;
  };

  const getAnnualSavings = (plan: PlanOption) => {
    const monthlyCost = plan.price * 12;
    const annualCost = getPrice(plan) * 12;
    return monthlyCost - annualCost;
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6 px-6">
      {/* Header */}
      <div className="mx-auto max-w-2xl space-y-3 text-center">
        <h1 className="text-center text-4xl font-semibold lg:text-5xl">
          Simple, Transparent Pricing
        </h1>
        <p className="text-muted-foreground">
          Choose the plan that fits your business needs. Start with a 7-day free trial.
        </p>
      </div>
      
      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <span className={`text-sm ${!isAnnual ? 'font-semibold' : 'text-muted-foreground'}`}>
          Monthly
        </span>
        <Switch
          checked={isAnnual}
          onCheckedChange={handleIntervalChange}
        />
        <span className={`text-sm ${isAnnual ? 'font-semibold' : 'text-muted-foreground'}`}>
          Annual
        </span>
        {isAnnual && (
          <Badge variant="secondary" className="ml-2">
            Save up to 17%
          </Badge>
        )}
      </div>

      {/* Plans */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {plans.map((plan) => {
          const isSelected = selectedPlan === plan.id;
          const price = getPrice(plan);
          
          return (
            <Card 
              key={plan.id} 
              className={`relative cursor-pointer transition-all duration-200 ${
                plan.recommended ? 'border-primary ring-2 ring-primary' : ''
              } ${
                isSelected ? 'ring-2 ring-blue-500 border-blue-500' : ''
              }`}
              onClick={() => handlePlanChange(plan.id)}
            >
              {plan.recommended && (
                <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary">
                  Most Popular
                </Badge>
              )}
              
              {isSelected && (
                <div className="absolute -top-2 -right-2 z-10">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
              
              <CardHeader className="pb-4">
                <CardTitle className="font-medium">{plan.name}</CardTitle>
                
                <div className="my-2">
                  <span className="text-3xl font-bold">
                    ${price}
                  </span>
                  <span className="text-muted-foreground">
                    {isAnnual ? '/mo billed annually' : '/mo'}
                  </span>
                </div>

                <CardDescription className="text-sm min-h-[32px]">
                  {plan.description}
                </CardDescription>
                
                <div className="pt-1 text-xs text-muted-foreground space-y-0">
                  <div>
                    {plan.limits.users}
                  </div>
                  <div>
                    {plan.limits.transactions}
                  </div>
                </div>

                <Button 
                  onClick={handleNext}
                  disabled={isLoading}
                  className={`mt-3 w-full ${plan.recommended ? '' : 'variant-outline'}`}
                  variant={plan.recommended ? 'default' : 'outline'}
                  size="sm"
                >
                  {isLoading ? "Loading..." : "Start 7-Day Trial"}
                </Button>
              </CardHeader>

              <CardContent className="space-y-3 pt-0">
                <hr className="border-dashed" />

                <ul className="list-outside space-y-2 text-xs">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="size-3 mt-0.5 text-green-600 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          );
        })}
      </div>

      
      <div className="text-center text-sm text-muted-foreground">
        <p>
          All plans include a 7-day free trial. Credit card required but no charges until trial ends. 
          Cancel anytime during the trial period with no charges.
        </p>
      </div>

      {/* Trial Notice */}
      <Card className="bg-blue-900/30 border-blue-500/30">
        <CardContent className="p-6 text-center">
          <h3 className="text-lg font-semibold text-blue-300 mb-2">
            ✨ Start with a 7-day free trial
          </h3>
          <p className="text-blue-200">
            Try all features risk-free. Cancel anytime during your trial period.
          </p>
        </CardContent>
      </Card>

      {/* Additional Info */}
      <Card className="bg-green-900/30 border-green-500/30">
        <CardContent className="p-6 text-center">
          <div className="flex items-center justify-center space-x-2 mb-3">
            <Shield className="w-5 h-5 text-green-400" />
            <h4 className="font-medium text-green-300">30-Day Money-Back Guarantee</h4>
          </div>
          <p className="text-green-200 text-sm">
            Try NextGen Business risk-free. If you're not completely satisfied within 30 days, 
            we'll refund your payment in full. No questions asked.
          </p>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
          className="flex items-center justify-center space-x-2 order-2 sm:order-1"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Previous</span>
        </Button>
        <Button
          onClick={handleNext}
          disabled={!selectedPlan || isLoading}
          className="flex items-center justify-center space-x-2 order-1 sm:order-2"
        >
          <span>{isLoading ? "Loading..." : "Continue"}</span>
        </Button>
      </div>
    </div>
  );
} 