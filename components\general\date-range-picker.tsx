"use client"

import * as React from "react"
import { addDays, format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfQuarter, endOfQuarter, startOfYear, endOfYear, subWeeks, subMonths, subYears } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DateRangePickerProps extends React.HTMLAttributes<HTMLDivElement> {
  onDateRangeChange: (range: DateRange | undefined) => void;
  value?: DateRange;
}

const presets = [
    { label: "Today", range: () => ({ from: new Date(), to: new Date() })},
    { label: "Last 7 Days", range: () => ({ from: addDays(new Date(), -6), to: new Date() })},
    { label: "Last Week", range: () => {
        // Always get the previous full week (Monday-Sunday)
        const now = new Date();
        const lastWeekStart = startOfWeek(subWeeks(now, 1), { weekStartsOn: 1 });
        const lastWeekEnd = endOfWeek(subWeeks(now, 1), { weekStartsOn: 1 });
        return { from: lastWeekStart, to: lastWeekEnd };
    }},
    { label: "Last 30 Days", range: () => ({ from: addDays(new Date(), -29), to: new Date() })},
    { label: "Last Month", range: () => { const d = subMonths(new Date(), 1); return { from: startOfMonth(d), to: endOfMonth(d) }}},
    { label: "This Year", range: () => ({ from: startOfYear(new Date()), to: endOfYear(new Date())})},
    { label: "Last Year", range: () => { const d = subYears(new Date(), 1); return { from: startOfYear(d), to: endOfYear(d) }}},
    { label: "Q1", range: () => { const d = startOfYear(new Date()); return { from: startOfQuarter(d), to: endOfQuarter(d)}}},
    { label: "Q2", range: () => { let d = startOfYear(new Date()); d = addDays(endOfQuarter(d), 1); return { from: startOfQuarter(d), to: endOfQuarter(d)}}},
    { label: "Q3", range: () => { let d = startOfYear(new Date()); d = addDays(endOfQuarter(addDays(endOfQuarter(d), 1)), 1); return { from: startOfQuarter(d), to: endOfQuarter(d)}}},
    { label: "Q4", range: () => { const d = endOfYear(new Date()); return { from: startOfQuarter(d), to: endOfQuarter(d)}}},
    { label: "All time", range: () => ({ from: new Date(2020, 0, 1), to: endOfYear(new Date()) })},
]

export function DateRangePicker({ className, onDateRangeChange, value }: DateRangePickerProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(value ?? {
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  });
  const [activePreset, setActivePreset] = React.useState<string | undefined>();

  React.useEffect(() => {
    if (value && (value.from !== date?.from || value.to !== date?.to)) {
      setDate(value);
    }
  }, [value]);

  // Call onDateRangeChange when component initializes with default value
  React.useEffect(() => {
    if (!value && date?.from && date?.to) {
      onDateRangeChange(date);
    }
  }, []); // Only run on mount
  
  const handlePresetClick = (presetLabel: string) => {
    const preset = presets.find(p => p.label === presetLabel);
    if(preset) {
        const newRange = preset.range();
        setDate(newRange);
        setActivePreset(presetLabel);
        onDateRangeChange(newRange);
    }
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-[260px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 flex" align="start">
          <div className="flex flex-col space-y-2 p-2 border-r">
             {presets.map(p => (
                 <Button 
                    key={p.label} 
                    variant="ghost" 
                    className={cn("justify-start", activePreset === p.label && "bg-muted font-bold")}
                    onClick={() => handlePresetClick(p.label)}
                >
                     {p.label}
                 </Button>
             ))}
          </div>
          <div className="flex flex-col">
            <Calendar
              captionLayout="dropdown"
              fromYear={1900}
              toYear={new Date().getFullYear()}
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={(newDate) => {
                setDate(newDate);
                setActivePreset(undefined);
                onDateRangeChange(newDate);
              }}
              numberOfMonths={2}
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
