"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";
import { getServerUserContext } from "@/lib/server-auth";
import { productSoldSchema } from "@/lib/actions/automation/types";
import { createJournalAutomationService } from "@/lib/actions/automation/journal-automation";

// Response types
interface BusinessActionResponse {
  success: boolean;
  transactionId?: string;
  message: string;
  error?: string;
  warnings?: string[];
  journalEntries?: any[];
}

/**
 * Process Product Sale
 * Business-friendly action: "I sold products to a customer"
 */
export async function processProductSold(
  data: z.infer<typeof productSoldSchema>
): Promise<BusinessActionResponse> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    // Validate input
    const validatedData = productSoldSchema.parse(data);
    
    // Create journal automation service
    const journalService = await createJournalAutomationService(orgId);
    
    // Process the business action
    const result = await journalService.processBusinessAction(validatedData);
    
    if (result.success) {
      revalidatePath("/transactions");
      revalidatePath("/dashboard");
      revalidatePath("/products");
      
      const itemCount = validatedData.items.length;
      const totalAmount = validatedData.items.reduce((sum, item) => sum + item.totalPrice, 0);
      
      return {
        success: true,
        transactionId: result.transactionId,
        message: `Sale of ${itemCount} item${itemCount > 1 ? 's' : ''} to ${validatedData.customerName} ($${totalAmount.toFixed(2)}) recorded successfully!`,
        warnings: result.warnings,
        journalEntries: result.journalEntries
      };
    } else {
      return {
        success: false,
        message: "Failed to process sale",
        error: result.error
      };
    }
  } catch (error) {
    console.error("Error processing product sale:", error);
    return {
      success: false,
      message: "Failed to process sale",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 