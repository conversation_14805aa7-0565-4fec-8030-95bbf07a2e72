/**
 * Business Action Types - Phase 2: Transaction UX Revolution
 * 
 * This system transforms complex accounting terminology into intuitive business language.
 * Instead of "Debit Cash, Credit AR", users interact with "Received Payment from Customer"
 */

import { z } from "zod";

// Core Business Action Types
export type BusinessActionType =
  | "money_received"
  | "money_paid";

// Payment Methods
export type PaymentMethod = 
  | "cash"
  | "credit_card"
  | "bank_transfer"
  | "check"
  | "digital_wallet"
  | "financing"
  | "other";

// Base Business Action
export interface BaseBusinessAction {
  id?: string;
  type: BusinessActionType;
  date: Date;
  amount: number;
  description: string;
  reference?: string;
  attachments?: string[];
  metadata?: Record<string, any>;
}

// Minimal base schema for new actions
export const baseBusinessActionSchema = z.object({
  type: z.enum(["money_received", "money_paid"]),
  date: z.date(),
  amount: z.number().positive(),
  description: z.string().min(1),
  reference: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

export interface MoneyReceivedAction extends BaseBusinessAction {
  type: "money_received";
  // Add any additional fields if needed
}

export interface MoneyPaidAction extends BaseBusinessAction {
  type: "money_paid";
  vendorId: string;
  productId: string;
  billId?: string;
}

export const moneyReceivedActionSchema = baseBusinessActionSchema.extend({
  type: z.literal("money_received"),
  // Add any additional fields if needed
});

export const moneyPaidActionSchema = baseBusinessActionSchema.extend({
  type: z.literal("money_paid"),
  vendorId: z.string().min(1, "Vendor is required"),
  productId: z.string().min(1, "Product/Service is required"),
  billId: z.string().optional(),
});

// Supporting Types
export interface SaleItem {
  productId?: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxAmount?: number;
}

export interface PurchaseItem {
  productId?: string;
  productName: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  taxAmount?: number;
}

export type ExpenseCategory = 
  | "office_supplies"
  | "travel"
  | "meals_entertainment"
  | "utilities"
  | "rent_lease"
  | "insurance"
  | "professional_services"
  | "marketing_advertising"
  | "software_subscriptions"
  | "equipment_maintenance"
  | "telecommunications"
  | "shipping_delivery"
  | "bank_fees"
  | "other";

// Union type for all business actions
export type BusinessAction = 
  | MoneyReceivedAction
  | MoneyPaidAction;

// Business Action Context for intelligent processing
export interface BusinessContext {
  organizationId: string;
  defaultAccounts: {
    cash?: string;
    bankChecking?: string;
    accountsReceivable?: string;
    accountsPayable?: string;
    salesRevenue?: string;
    costOfGoodsSold?: string;
    inventory?: string;
    equipment?: string;
    accumulatedDepreciation?: string;
  };
  taxSettings: {
    defaultTaxRate: number;
    taxAccountId?: string;
    collectSalesTax: boolean;
  };
  businessType: string;
  currency: string;
  fiscalYearStart: Date;
}

// Business Action Descriptions - User-friendly explanations
export const BUSINESS_ACTION_DESCRIPTIONS: Record<BusinessActionType, {
  title: string;
  description: string;
  examples: string[];
  icon: string;
}> = {
  money_received: {
    title: "I received money",
    description: "Record when you or your business receives money from any source.",
    examples: [
      "Customer paid me",
      "Received a refund",
      "Other income"
    ],
    icon: "💰"
  },
  money_paid: {
    title: "I paid money",
    description: "Record when you or your business pays money for any reason.",
    examples: [
      "Paid a bill",
      "Bought supplies",
      "Other expense"
    ],
    icon: "💸"
  }
}; 