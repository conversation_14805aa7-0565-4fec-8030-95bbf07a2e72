import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Link,
  Preview,
  Section,
  Text,
  Tailwind,
  Font,
} from '@react-email/components';

interface VerifyAccountEmailProps {
  userName: string;
  userEmail: string;
  verifyUrl: string;
}

const VerifyAccountEmail = ({ userName, userEmail, verifyUrl }: VerifyAccountEmailProps) => {
  return (
    <Html lang="en" dir="ltr">
      <Head>
        <Font
          fontFamily="Poppins"
          fallbackFontFamily="Arial"
          webFont={{
            url: 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap',
            format: 'woff2',
          }}
        />
      </Head>
      <Tailwind>
        <Preview>Welcome to NextGenBusiness! Verify your email to unlock your complete business management solution.</Preview>
        <Body className="bg-[#222121] font-sans py-[40px]">
          <Container className="bg-white rounded-[12px] max-w-[600px] mx-auto px-[48px] py-[40px]">
            {/* Header */}
            <Section className="text-center mb-[32px]">
              <Heading className="text-[28px] font-bold text-[#222121] m-0 mb-[8px]">
                Welcome to NextGenBusiness
              </Heading>
            </Section>

            {/* Welcome & Value Proposition */}
            <Section className="mb-[24px]">
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[16px]">
                Hello {userName},
              </Text>
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[16px]">
                Thank you for choosing <strong>NextGenBusiness</strong> to transform your business. We&apos;re excited to help you streamline your bookkeeping, automate your accounting, and gain complete control over your business operations.
              </Text>
            </Section>

            {/* Feature Highlights */}
            <Section className="mb-[24px]">
              <Text className="text-[16px] text-[#222121] leading-[24px] font-medium mb-[8px]">
                Here&apos;s what you can do with NextGenBusiness:
              </Text>
              <ul style={{ paddingLeft: 20, margin: 0 }}>
                <li style={{ marginBottom: 8 }}>
                  <span style={{ color: '#22c55e', fontWeight: 700 }}></span> <strong>Automated Bookkeeping:</strong> Our software is smart and remembers how to automatically categorize your transactions. 
                </li>
                <li style={{ marginBottom: 8 }}>
                  <span style={{ color: '#22c55e', fontWeight: 700 }}></span> <strong>Real-time Business Insights:</strong> Make informed decisions with up-to-date dashboards.
                </li>
                <li style={{ marginBottom: 8 }}>
                  <span style={{ color: '#22c55e', fontWeight: 700 }}></span> <strong>Tax-ready Reports:</strong> Prepare for tax season with confidence.
                </li>
                <li style={{ marginBottom: 8 }}>
                  <span style={{ color: '#6366f1', fontWeight: 700 }}></span> <strong>And many more planned features are coming soon!</strong> <br />
                  <span style={{ color: '#222121', fontWeight: 400 }}>Make sure to <Link href="https://nextgenbusiness.com/blog" className="text-[#bc00fe] underline">read our blog</Link> to stay informed about the latest updates.</span>
                </li>
              </ul>
            </Section>

            {/* Verification Call-to-Action */}
            <Section className="mb-[32px]">
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[16px]">
                To get started, please verify your email address by clicking the button below.<br />
                <span className="text-[14px] text-[#666666]">This helps us keep your account secure and unlocks all features of your new platform.</span>
              </Text>
              <div className="text-center">
                <Button
                  href={verifyUrl}
                  className="bg-[#bc00fe] text-white px-[32px] py-[16px] rounded-[8px] text-[16px] font-semibold no-underline box-border inline-block"
                >
                  Verify Email Address
                </Button>
              </div>
            </Section>

            {/* Alternative Link */}
            <Section className="mb-[32px]">
              <Text className="text-[14px] text-[#666666] leading-[20px] mb-[8px]">
                If the button doesn&apos;t work, copy and paste this link into your browser:
              </Text>
              <Link
                href={verifyUrl}
                className="text-[#bc00fe] text-[14px] break-all underline"
              >
                {verifyUrl}
              </Link>
            </Section>

            {/* Onboarding Help */}
            <Section className="mb-[32px]">
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[8px]">
                Need help getting set up? Our team is ready to assist you. Simply reply to this email or schedule a free onboarding call with one of our business experts.
              </Text>
            </Section>

            {/* Security Notice */}
            <Section className="bg-[#f1f1f1] rounded-[8px] p-[20px] mb-[32px]">
              <Text className="text-[14px] text-[#222121] leading-[20px] m-0 mb-[8px] font-semibold">
                🔒 Security Notice
              </Text>
              <Text className="text-[14px] text-[#666666] leading-[20px] m-0">
                If you didn&apos;t create an account with us, please ignore this email. Your email address will not be added to our system.
              </Text>
            </Section>

            {/* Footer */}
            <Section className="border-t border-solid border-[#e5e5e5] pt-[24px]">
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0 mb-[8px]">
                This email was sent to {userEmail}
              </Text>
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0 mb-[8px]">
                NextGenBusiness
              </Text>
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0">
                <Link href="https://nextgenbusiness.com/unsubscribe" className="text-[#bc00fe] underline">Unsubscribe</Link> |
                <Link href="https://nextgenbusiness.com/privacy" className="text-[#bc00fe] underline ml-[4px]">Privacy Policy</Link> |
                <Link href="https://nextgenbusiness.com/contact" className="text-[#bc00fe] underline ml-[4px]">Contact Support</Link>
              </Text>
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0 mt-[16px]">
                © {new Date().getFullYear()} Alvecomm. All rights reserved.
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default VerifyAccountEmail;