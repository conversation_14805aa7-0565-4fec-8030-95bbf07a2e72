"use server";

import { db } from "@/db/drizzle";
import { budgetLineItems, accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, inArray } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { randomUUID } from 'crypto';

export async function importBudgetFromCSV(
  budgetPeriodId: string,
  parsedItems: Array<{
    accountIdentifier: string;
    budgetedAmount: number;
    notes: string;
  }>
) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    const accountIdentifiers = parsedItems.map((item) => item.accountIdentifier);
    const existingAccounts = await db
      .select({ id: accounts.id, name: accounts.name, accountNumber: accounts.accountNumber })
      .from(accounts)
      .where(and(eq(accounts.organizationId, organizationId)));

    const accountMap = new Map<string, string>();
    existingAccounts.forEach(acc => {
        if(acc.accountNumber) accountMap.set(acc.accountNumber, acc.id);
        accountMap.set(acc.name, acc.id);
    });

    const itemsToInsert = parsedItems
      .map((item) => {
        const accountId = accountMap.get(item.accountIdentifier);
        if (!accountId) {
            console.warn(`Account "${item.accountIdentifier}" not found. Skipping.`);
            return null;
        };
        return {
          id: randomUUID(),
          budgetPeriodId,
          accountId,
          budgetedAmount: String(item.budgetedAmount),
          notes: item.notes,
        };
      })
      .filter((item): item is NonNullable<typeof item> => item !== null);

    if (itemsToInsert.length > 0) {
      // Consider using .onConflictDoUpdate() for more robust imports
      await db.insert(budgetLineItems).values(itemsToInsert);
    }

    revalidatePath("/accounting/budget");
    return { success: true, message: `Imported ${itemsToInsert.length} items.` };
  } catch (error) {
    console.error("Error importing budget from CSV:", error);
    return { error: "Failed to import budget from CSV." };
  }
} 