"use server";

import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

// Server action to fetch accounts for transaction dialog
export async function getAccountsForTransaction() {
  const context = await getServerUserContext();
  const organizationId = context.organization.id;

  const result = await db
    .select({ id: accounts.id, name: accounts.name, type: accounts.type })
    .from(accounts)
    .where(and(
      eq(accounts.organizationId, organizationId),
      eq(accounts.isActive, true),
      eq(accounts.isHeader, false)
    ));
  return result;
} 