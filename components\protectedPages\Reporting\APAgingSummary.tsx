import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface APAgingSummaryData {
  agingBuckets: {
    current: number;
    "1-30": number;
    "31-60": number;
    "61-90": number;
    "90+": number;
  };
  totalOutstanding: number;
  totalBills: number;
}

interface Props {
  data: APAgingSummaryData;
}

export default function APAgingSummary({ data }: Props) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (amount: number, total: number) => {
    if (total === 0) return "0%";
    return `${((amount / total) * 100).toFixed(1)}%`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Accounts Payable Aging Summary</CardTitle>
        <CardDescription>
          Outstanding payables by aging period ({data.totalBills} bills)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Aging Period</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead className="text-right">Percentage</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">Current (Not Due)</TableCell>
              <TableCell className="text-right">{formatCurrency(data.agingBuckets.current)}</TableCell>
              <TableCell className="text-right">{formatPercentage(data.agingBuckets.current, data.totalOutstanding)}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">1-30 Days</TableCell>
              <TableCell className="text-right">{formatCurrency(data.agingBuckets["1-30"])}</TableCell>
              <TableCell className="text-right">{formatPercentage(data.agingBuckets["1-30"], data.totalOutstanding)}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">31-60 Days</TableCell>
              <TableCell className="text-right text-yellow-600">{formatCurrency(data.agingBuckets["31-60"])}</TableCell>
              <TableCell className="text-right">{formatPercentage(data.agingBuckets["31-60"], data.totalOutstanding)}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">61-90 Days</TableCell>
              <TableCell className="text-right text-orange-600">{formatCurrency(data.agingBuckets["61-90"])}</TableCell>
              <TableCell className="text-right">{formatPercentage(data.agingBuckets["61-90"], data.totalOutstanding)}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">Over 90 Days</TableCell>
              <TableCell className="text-right text-red-600">{formatCurrency(data.agingBuckets["90+"])}</TableCell>
              <TableCell className="text-right">{formatPercentage(data.agingBuckets["90+"], data.totalOutstanding)}</TableCell>
            </TableRow>
            <TableRow className="border-t-2 font-semibold">
              <TableCell>Total Outstanding</TableCell>
              <TableCell className="text-right">{formatCurrency(data.totalOutstanding)}</TableCell>
              <TableCell className="text-right">100%</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
} 