"use server";

import { db } from "@/db/drizzle";
import { projects } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { Project } from "@/components/protectedPages/Projects/columns";

export async function getProjectById(projectId: string): Promise<Project | null> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const projectRow = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), eq(projects.organizationId, orgId)),
      with: {
        client: {
          columns: {
            name: true,
          },
        },
      },
    });
    if (!projectRow) return null;
    return {
      ...projectRow,
      clientName: projectRow.client?.name ?? "N/A",
    };
  } catch (error) {
    return null;
  }
} 