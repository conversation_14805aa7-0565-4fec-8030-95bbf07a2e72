"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { projects } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { projectFormSchema } from "@/lib/actions/projects/project.schema";
import { randomUUID } from 'crypto';

const upsertProjectSchema = projectFormSchema.extend({
  id: z.string().optional(),
});

export async function upsertProject(data: z.infer<typeof projectFormSchema>) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const validatedData = upsertProjectSchema.parse(data);
    const { id, ...projectData } = validatedData;

    if (id) {
      const [updatedProject] = await db
        .update(projects)
        .set(projectData)
        .where(and(eq(projects.id, id), eq(projects.organizationId, orgId)))
        .returning();

      if (!updatedProject) {
        return {
          success: false,
          message: "Project not found or you do not have permission to edit it.",
        };
      }
      revalidatePath("/projects");
      return {
        success: true,
        message: "Project updated successfully.",
      };
    } else {
      const [newProject] = await db
        .insert(projects)
        .values({
          id: randomUUID(),
          ...projectData,
          organizationId: orgId,
        })
        .returning();
      revalidatePath("/projects");
      return {
        success: true,
        message: "Project created successfully.",
      };
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, message: JSON.stringify(error.errors) };
    }
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to upsert project.",
    };
  }
} 