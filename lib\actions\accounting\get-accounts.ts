"use server";

import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";
import { Account } from "@/components/protectedPages/Accounting/columns";

export async function getAccounts(): Promise<Account[]> {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;

  const accountList = await db
    .select()
    .from(accounts)
    .where(eq(accounts.organizationId, orgId))
    .orderBy(accounts.name);

  return accountList;
} 