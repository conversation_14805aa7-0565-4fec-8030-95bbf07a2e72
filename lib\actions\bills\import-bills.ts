"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { bills, billItems, vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { NewBill, billFormSchema } from "@/lib/actions/bills/bill.schema";
import { and, eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { createBill } from "./create-bill";

type ImportedBill = Omit<NewBill, 'vendorId'> & { vendorName: string };

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function importBills(importedBills: ImportedBill[]): ActionResponse {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;

  if (!importedBills || importedBills.length === 0) {
    return { success: false, message: "No bills to import." };
  }

  try {
    const vendorNames = [...new Set(importedBills.map(b => b.vendorName))];
    const existingVendors = await db
        .select({ id: vendors.id, name: vendors.name })
        .from(vendors)
        .where(and(
            eq(vendors.organizationId, orgId),
            inArray(vendors.name, vendorNames)
        ));

    const vendorNameToIdMap = new Map(existingVendors.map(v => [v.name.toLowerCase(), v.id]));
    
    const errors: string[] = [];
    const billsToInsert: (NewBill & { organizationId: string })[] = [];

    for (const importedBill of importedBills) {
        const vendorId = vendorNameToIdMap.get(importedBill.vendorName.toLowerCase());
        if (!vendorId) {
            errors.push(`Vendor "${importedBill.vendorName}" not found for bill number ${importedBill.billNumber}.`);
            continue;
        }

        const validatedFields = billFormSchema.safeParse({
            ...importedBill,
            vendorId: vendorId,
        });

        if (!validatedFields.success) {
            errors.push(`Invalid data for bill number ${importedBill.billNumber}: ${validatedFields.error.message}`);
            continue;
        }

        billsToInsert.push({ ...validatedFields.data, organizationId: orgId });
    }

    if (errors.length > 0) {
      return { success: false, message: `Import failed with ${errors.length} errors: ${errors.join(", ")}` };
    }

    await db.transaction(async (tx) => {
      for (const billData of billsToInsert) {
        const { lineItems, ...newBillData } = billData;
        const total = lineItems.reduce((acc, item) => acc + item.quantity * item.unitPrice, 0);

        const [bill] = await tx
          .insert(bills)
          .values({
            ...newBillData,
            subtotal: String(total),
            total: String(total),
          })
          .returning();

        const itemsToInsert = lineItems.map((item) => ({
          ...item,
          billId: bill.id,
          unitPrice: String(item.unitPrice),
          total: String(item.quantity * item.unitPrice),
        }));

        if (itemsToInsert.length > 0) {
            await tx.insert(billItems).values(itemsToInsert);
        }
      }
    });

    revalidatePath("/bills");
    return { success: true, message: `Successfully imported ${billsToInsert.length} bills.` };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : "Failed to import bills." };
  }
} 