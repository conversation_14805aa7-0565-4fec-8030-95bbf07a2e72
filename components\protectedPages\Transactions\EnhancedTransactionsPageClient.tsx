"use client";

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Sparkles, 
  Calculator, 
  TrendingUp, 
  Filter, 
  Download,
  Plus,
  Zap,
  BookOpen,
  BarChart3,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  Info
} from "lucide-react";
import { toast } from "sonner";

// Import existing components
import { TransactionsDataTable } from "./transactions-data-table";
import { getColumns, type Transaction } from "./columns";
import AddOrEditTransactionDialog from "./AddTransactionDialog";

// Import new business action components
import { BusinessActionWizard } from "@/components/business-actions/BusinessActionWizard";

// Import actions
import {
  createTransaction,
  updateTransaction,
  deleteTransaction,
  getTransactions,
} from "@/lib/actions/transactions";
import { getBusinessActionHistory } from "@/lib/actions/automation";

// Types
type MutateData = { id?: string; formData: FormData };

interface TransactionMetrics {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  transactionCount: number;
}

export default function EnhancedTransactionsPageClient() {
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingTransactionId, setEditingTransactionId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("business-actions");

  // Existing transaction queries
  const { data: transactions = [], isLoading, isError } = useQuery<Transaction[]>({
    queryKey: ["transactions"],
    queryFn: getTransactions,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  // Business action history
  // const { data: businessActionHistory = { success: false, history: [] }, isLoading: isLoadingHistory } = useQuery({
  //   queryKey: ["business-action-history"],
  //   queryFn: () => getBusinessActionHistory(50),
  // });

  // Calculate metrics
  const metrics = useMemo(() => {
    if (!transactions.length) {
      return { totalIncome: 0, totalExpenses: 0, netIncome: 0, transactionCount: 0 };
    }

    const income = transactions
      .filter(tx => tx.type === "simple_income")
      .reduce((sum, tx) => sum + parseFloat(tx.totalAmount), 0);
    
    const expenses = transactions
      .filter(tx => tx.type === "simple_expense")
      .reduce((sum, tx) => sum + parseFloat(tx.totalAmount), 0);

    return {
      totalIncome: income,
      totalExpenses: expenses,
      netIncome: income - expenses,
      transactionCount: transactions.length
    };
  }, [transactions]);

  // Existing mutations
  const { mutate, isPending } = useMutation<any, Error, MutateData>({
    mutationFn: (data: MutateData) =>
      data.id ? updateTransaction(data.id, data.formData) : createTransaction(data.formData),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["transactions"] });
        queryClient.invalidateQueries({ queryKey: ["business-action-history"] });
        setDialogOpen(false);
        setEditingTransactionId(null);
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });
  
  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (id: string) => deleteTransaction(id),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["transactions"] });
        queryClient.invalidateQueries({ queryKey: ["business-action-history"] });
      } else {
        toast.error(result.message);
      }
    },
    onError: (error) => toast.error(error.message),
  });

  // Event handlers
  const handleEdit = (tx: Transaction) => {
    setEditingTransactionId(tx.id);
    setDialogOpen(true);
  };
  
  const handleDelete = (tx: Transaction) => {
    if (window.confirm("Are you sure you want to delete this transaction?")) {
      mutateDelete(tx.id);
    }
  };
  
  const handleSubmit = (formData: FormData) => {
    mutate({ id: editingTransactionId ?? undefined, formData });
  };
  
  const openNewDialog = () => {
    setEditingTransactionId(null);
    setDialogOpen(true);
  };

  const columns = useMemo(() => getColumns({ onEdit: handleEdit, onDelete: handleDelete }), []);

  // Handle business action completion
  const handleBusinessActionComplete = () => {
    queryClient.invalidateQueries({ queryKey: ["transactions"] });
    queryClient.invalidateQueries({ queryKey: ["business-action-history"] });
    toast.success("Business transaction recorded successfully!");
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="h-96 bg-muted rounded"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert className="max-w-md mx-auto mt-8">
        <AlertDescription>
          Error loading transactions. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold">Business Transactions</h1>
          <p className="text-muted-foreground">
            Record business activities with simple language - no accounting knowledge required
          </p>
        </div>
        <div className="flex gap-2">
          <BusinessActionWizard 
            trigger={
              <Button size="lg" className="h-12">
                <Sparkles className="h-5 w-5 mr-2" />
                Record Business Action
              </Button>
            }
          />
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Income</p>
                <p className="text-2xl font-bold text-green-600">
                  ${metrics.totalIncome.toLocaleString()}
                </p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <ArrowUpRight className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
                <p className="text-2xl font-bold text-red-600">
                  ${metrics.totalExpenses.toLocaleString()}
                </p>
              </div>
              <div className="p-2 bg-red-100 rounded-lg">
                <ArrowDownRight className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Net Income</p>
                <p className={`text-2xl font-bold ${metrics.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${metrics.netIncome.toLocaleString()}
                </p>
              </div>
              <div className={`p-2 rounded-lg ${metrics.netIncome >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                <DollarSign className={`h-6 w-6 ${metrics.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Transactions</p>
                <p className="text-2xl font-bold">
                  {metrics.transactionCount}
                </p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="business-actions" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Business Actions
          </TabsTrigger>
          <TabsTrigger value="accounting-view" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Accounting View
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Business Actions Tab */}
        <TabsContent value="business-actions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Business Actions
              </CardTitle>
              <CardDescription>
                Record common business activities with simple, intuitive forms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <BusinessActionWizard 
                  trigger={
                    <Card className="cursor-pointer hover:shadow-md transition-all group">
                      <CardContent className="p-4 text-center">
                        <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-green-200 transition-colors">
                          <DollarSign className="h-6 w-6 text-green-600" />
                        </div>
                        <h3 className="font-medium">Customer Payment</h3>
                        <p className="text-sm text-muted-foreground">I got paid by a customer</p>
                      </CardContent>
                    </Card>
                  }
                />
                <BusinessActionWizard 
                  trigger={
                    <Card className="cursor-pointer hover:shadow-md transition-all group">
                      <CardContent className="p-4 text-center">
                        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-blue-200 transition-colors">
                          <ArrowDownRight className="h-6 w-6 text-blue-600" />
                        </div>
                        <h3 className="font-medium">Vendor Payment</h3>
                        <p className="text-sm text-muted-foreground">I paid a vendor</p>
                      </CardContent>
                    </Card>
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Recent Business Actions */}
          {/* {businessActionHistory.success && businessActionHistory.history.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Business Actions</CardTitle>
                <CardDescription>
                  Your latest business transactions in simple language
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {businessActionHistory.history.slice(0, 5).map((action: any) => (
                    <div key={action.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div>
                          <p className="font-medium">{action.friendlyDescription}</p>
                          <p className="text-sm text-muted-foreground">{action.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">${action.amount.toFixed(2)}</p>
                        <Badge variant="outline" className="text-xs">
                          {action.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )} */}

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Smart Automation:</strong> When you record business actions, we automatically 
              create proper accounting journal entries behind the scenes. Your books stay accurate 
              without you needing to understand debits, credits, or chart of accounts!
            </AlertDescription>
          </Alert>
        </TabsContent>

        {/* Accounting View Tab */}
        <TabsContent value="accounting-view" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Traditional Accounting View
              </CardTitle>
              <CardDescription>
                For accountants and power users who prefer working with journal entries
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
                <Button onClick={openNewDialog}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Transaction
                </Button>
              </div>
              <TransactionsDataTable columns={columns} data={transactions} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Transaction Analytics
              </CardTitle>
              <CardDescription>
                Insights into your business transaction patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Analytics Coming Soon</h3>
                <p className="text-muted-foreground">
                  We're building powerful analytics to help you understand your business better.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Traditional Transaction Dialog */}
      <AddOrEditTransactionDialog
        open={dialogOpen}
        setOpen={setDialogOpen}
        transactionId={editingTransactionId}
        onSave={handleSubmit}
      />
    </div>
  );
} 