"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  createTransaction,
  updateTransaction,
  getTransactionAuditLog,
  getTransactionById,
} from "@/lib/actions/transactions";
import { getVendors } from "@/lib/actions/vendors/get-vendors";
import { toast } from "sonner";
import { CheckIcon } from "lucide-react";
import { motion, AnimatePresence } from "motion/react";
import { useQuery } from "@tanstack/react-query";
import { getAccountsForTransaction } from "@/lib/actions/accounting/get-accounts-for-transaction";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";
import { getTransactionAuditLog as getTransactionAuditLogAction } from "@/lib/actions/transactions/get-transaction-audit-log";
import { Badge } from "@/components/ui/badge";
import { getBills } from "@/lib/actions/bills/get-bills";

// Minimal Account type for selector
type Account = {
  id: string;
  name: string;
  type: string;
};

export interface TransactionFormData {
  id?: string;
  transactionType: string;
  amount: string;
  accountId: string;
  date: string;
  description: string;
}

interface AddOrEditTransactionDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  transactionId?: string | null;
  onSave?: (tx: any) => void;
}

export default function AddOrEditTransactionDialog({ open, setOpen, transactionId, onSave }: AddOrEditTransactionDialogProps) {
  const isEdit = !!transactionId;
  const { data: transactionData, isLoading: isLoadingTransaction } = useQuery({
    queryKey: ["transaction", transactionId],
    queryFn: () => (transactionId ? getTransactionById(transactionId) : null),
    enabled: !!transactionId && open,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  const { data: accountsForTx = { income: [], expense: [] } } = useQuery({
    queryKey: ["accountsForTransaction"],
    queryFn: getAccountsForTransaction,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  const { data: allAccounts = [] } = useQuery({
    queryKey: ["allAccounts"],
    queryFn: getAccounts,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  const { data: vendors = [] } = useQuery({
    queryKey: ["vendors"],
    queryFn: getVendors,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  const [transactionType, setTransactionType] = useState("expense");
  const { data: bills = [] } = useQuery({
    queryKey: ["billsForTransaction"],
    queryFn: getBills,
    enabled: transactionType === "expense",
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  const [amount, setAmount] = useState("");
  const [accountId, setAccountId] = useState("");
  const [date, setDate] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const firstFieldRef = useRef<HTMLInputElement | null>(null);
  const [showCheckmark, setShowCheckmark] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const pendingCloseRef = useRef<null | (() => void)>(null);
  const [showAudit, setShowAudit] = useState(false);
  const [categoryId, setCategoryId] = useState<string | undefined>();
  const [isRecurring, setIsRecurring] = useState(false);
  const [billId, setBillId] = useState("");

  // Move accounts declaration here so it's available for useEffect below
  const accounts = React.useMemo(() => {
    return Array.isArray(accountsForTx)
      ? accountsForTx
      : transactionType === "income"
        ? accountsForTx?.income ?? []
        : accountsForTx?.expense ?? [];
  }, [accountsForTx, transactionType]);

  // Keyword-based account suggestion logic - use useMemo instead of useEffect to prevent infinite loops
  const suggestedAccounts = React.useMemo(() => {
    if (!description || !accounts.length) {
      return [];
    }
    // Simple keyword extraction: split, lowercase, remove stopwords
    const stopwords = ["for", "the", "and", "a", "of", "to", "in", "on", "at", "with", "by", "is", "was", "it", "as", "an", "be", "are", "from", "this", "that", "or", "but", "not", "your", "my", "our", "their", "his", "her", "its"]; 
    const words = description
      .toLowerCase()
      .split(/[^a-z0-9]+/)
      .filter(w => w && !stopwords.includes(w));
    // Score accounts by keyword matches in name/type
    const scored = accounts.map(acc => {
      const text = (acc.name + " " + acc.type).toLowerCase();
      let score = 0;
      for (const word of words) {
        if (text.includes(word)) score++;
      }
      return { ...acc, _score: score };
    });
    const matches = scored.filter(acc => acc._score > 0).sort((a, b) => b._score - a._score);
    return matches.slice(0, 3);
  }, [description, accounts]);

  // Auto-select top suggestion when suggestedAccounts change and no account is selected
  useEffect(() => {
    if (!accountId && suggestedAccounts.length > 0) {
      setAccountId(suggestedAccounts[0].id);
    }
  }, [suggestedAccounts, accountId]);

  // Detect if form is dirty
  const isDirty = React.useMemo(() => {
    if (!open) return false;
    if (isEdit && transactionData) {
      return (
        transactionType !== (transactionData.type === "simple_income" ? "income" : "expense") ||
        amount !== transactionData.totalAmount ||
        accountId !== (transactionData.journalEntries?.[0]?.accountId || "") ||
        date !== (transactionData.date ? new Date(transactionData.date).toISOString().split("T")[0] : "") ||
        description !== transactionData.description
      );
    } else {
      return (
        transactionType || amount || accountId || date || description
      );
    }
  }, [transactionType, amount, accountId, date, description, isEdit, transactionData, open]);

  // Autofocus first field when dialog opens
  useEffect(() => {
    if (open && firstFieldRef.current) {
      firstFieldRef.current.focus();
    }
  }, [open]);

  // Reset errors on open/initialData change
  useEffect(() => {
    setErrors({});
  }, [open, transactionData]);

  const reset = () => {
    setTransactionType("expense");
    setAmount("");
    setAccountId("");
    setDate(new Date().toISOString().split("T")[0]);
    setDescription("");
  };

  // TODO: Refactor this component to align with the data from the server and database.
  // The current implementation is causing linter errors because the shape of the
  // transactionData object does not match what the form expects. This may require
  // updating the database schema and the server actions.
  //
  // useEffect(() => {
  //   if (isEdit && transactionData) {
  //     const isIncome = transactionData.type === 'simple_income';
  //     const primaryEntry = transactionData.journalEntries.find(je =>
  //       isIncome ? je.debit !== "0.00" : je.credit !== "0.00"
  //     );
  //     setTransactionType(isIncome ? "income" : "expense");
  //     setAmount(transactionData.totalAmount);
  //     setAccountId(primaryEntry?.accountId || "");
  //     setDescription(transactionData.description || "");
  //     if (transactionData.date) {
  //       setDate(new Date(transactionData.date).toISOString().split("T")[0]);
  //     }
  //   } else {
  //     reset();
  //   }
  // }, [isEdit, transactionData, open]);

  // Fetch audit log when editing a transaction
  const { data: auditLog = [], isLoading: isLoadingAuditLog } = useQuery({
    queryKey: ["transactionAuditLog", transactionId],
    queryFn: () => transactionId ? getTransactionAuditLogAction(transactionId) : Promise.resolve([]),
    enabled: isEdit && !!transactionId && open,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!transactionType) newErrors.transactionType = "Type is required.";
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) newErrors.amount = "Amount must be greater than 0.";
    if (!accountId) newErrors.accountId = "Account is required.";
    if (!date) newErrors.date = "Date is required.";
    if (!description) newErrors.description = "Description is required.";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return;
    if (!validate()) return;
    setLoading(true);
    const formData = new FormData();
    formData.append("transactionType", transactionType);
    formData.append("amount", amount);
    formData.append("accountId", accountId);
    formData.append("date", date);
    formData.append("description", description);
    // Add billId if present
    if (billId) formData.append("billId", billId);
    let result;
    if (isEdit && transactionId) {
      formData.append("id", transactionId);
      result = await updateTransaction(transactionId, formData);
    } else {
      result = await createTransaction(formData);
    }
    setLoading(false);
    if (result.success) {
      toast.success(result.message);
      setShowCheckmark(true);
      setTimeout(() => {
        setShowCheckmark(false);
        setOpen(false);
        if (onSave) {
          onSave({
            id: isEdit ? transactionId : Math.random().toString(36).slice(2),
            date,
            description,
            totalAmount: amount,
            status: String("posted"),
            type: transactionType === "income" ? "simple_income" : "simple_expense",
            accountId,
            transactionType,
          });
        }
      }, 1000);
    } else {
      toast.error(result.message);
    }
  };

  // Intercept dialog close
  const handleOpenChange = (nextOpen: boolean) => {
    if (!nextOpen && isDirty && !showCheckmark) {
      setShowConfirm(true);
      // Prevent dialog from closing
      return;
    }
    setOpen(nextOpen);
  };

  // Confirm close handler
  const handleConfirmClose = () => {
    setShowConfirm(false);
    setOpen(false);
  };

  // Cancel close handler
  const handleCancelClose = () => {
    setShowConfirm(false);
  };

  if (isEdit && isLoadingTransaction) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Loading Transaction</DialogTitle>
          </DialogHeader>
          <div>Loading transaction...</div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        aria-modal="true"
        role="dialog"
        aria-labelledby="transaction-dialog-title"
        className="bg-nextgen-background text-nextgen-accent font-poppins p-6 rounded-lg max-w-lg shadow-lg border border-nextgen-primary max-h-[90vh] overflow-y-auto"
      >
        <DialogHeader>
          <DialogTitle id="transaction-dialog-title">{isEdit ? "Edit Transaction" : "Add Transaction"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="mb-4 space-y-2">
            <label htmlFor="transaction-type" className="sr-only">Transaction Type</label>
            <Select value={transactionType} onValueChange={setTransactionType} disabled={loading}>
              <SelectTrigger className="w-full min-h-[44px] text-base md:text-lg border border-nextgen-primary focus:ring-2 focus:ring-nextgen-primary" id="transaction-type" aria-label="Transaction Type" aria-describedby={errors.transactionType ? "transaction-type-error" : undefined}>
                <SelectValue placeholder="Transaction Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="income">Income</SelectItem>
                <SelectItem value="expense">Expense</SelectItem>
              </SelectContent>
            </Select>
            {errors.transactionType && <div id="transaction-type-error" className="text-red-500 text-xs" aria-live="polite" role="alert">{errors.transactionType}</div>}
            <label htmlFor="amount" className="sr-only">Amount</label>
            <input
              id="amount"
              type="number"
              className="w-full rounded border border-nextgen-primary px-3 py-3 text-base md:text-lg focus:ring-2 focus:ring-nextgen-primary bg-nextgen-background text-nextgen-accent"
              value={amount}
              onChange={e => setAmount(e.target.value)}
              placeholder="Amount"
              min="0"
              required
              disabled={loading}
              ref={firstFieldRef}
              aria-label="Amount"
              aria-describedby={errors.amount ? "amount-error" : undefined}
            />
            {errors.amount && <div id="amount-error" className="text-red-500 text-xs" aria-live="polite" role="alert">{errors.amount}</div>}
            <label htmlFor="account" className="sr-only">Account</label>
            <Select value={accountId} onValueChange={setAccountId} disabled={loading || accounts.length === 0}>
              <SelectTrigger className="w-full min-h-[44px] text-base md:text-lg border border-nextgen-primary focus:ring-2 focus:ring-nextgen-primary" id="account" aria-label="Account" aria-describedby={errors.accountId ? "account-error" : undefined}>
                <SelectValue placeholder={accounts.length === 0 ? "Loading accounts..." : "Account"} />
              </SelectTrigger>
              <SelectContent>
                {suggestedAccounts.length > 0 && (
                  <>
                    <div className="px-2 py-1 text-xs text-muted-foreground">Suggested</div>
                    {suggestedAccounts.map(acc => (
                      <SelectItem key={acc.id} value={acc.id} className={"bg-green-50 font-semibold flex items-center"}>
                        {acc.name} ({acc.type})
                        <Badge className="ml-2" variant="secondary">AI Suggestion</Badge>
                      </SelectItem>
                    ))}
                    <div className="border-b my-1" />
                  </>
                )}
                {accounts.map((acc: any) => (
                  <SelectItem key={acc.id} value={acc.id} className={suggestedAccounts.some(s => s.id === acc.id) ? "bg-green-50" : ""}>
                    {acc.name} ({acc.type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.accountId && <div id="account-error" className="text-red-500 text-xs" aria-live="polite" role="alert">{errors.accountId}</div>}
            {/* Bill selector for expense transactions */}
            {transactionType === "expense" && (
              <div>
                <label htmlFor="bill" className="block text-sm font-medium text-gray-700 mb-1">Bill (optional)</label>
                <Select value={billId} onValueChange={setBillId} disabled={loading || bills.length === 0}>
                  <SelectTrigger className="w-full min-h-[44px] text-base md:text-lg border border-nextgen-primary focus:ring-2 focus:ring-nextgen-primary" id="bill" aria-label="Bill">
                    <SelectValue placeholder={bills.length === 0 ? "Loading bills..." : "Select bill (optional)"} />
                  </SelectTrigger>
                  <SelectContent>
                    {bills.map((bill: any) => (
                      <SelectItem key={bill.id} value={bill.id}>
                        {bill.billNumber || bill.id} - {bill.vendorName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            <label htmlFor="date" className="sr-only">Date</label>
            <input
              id="date"
              type="date"
              className="w-full rounded border border-nextgen-primary px-3 py-3 text-base md:text-lg focus:ring-2 focus:ring-nextgen-primary bg-nextgen-background text-nextgen-accent"
              value={date}
              onChange={e => setDate(e.target.value)}
              placeholder="Date"
              required
              disabled={loading}
              aria-label="Date"
              aria-describedby={errors.date ? "date-error" : undefined}
            />
            {errors.date && <div id="date-error" className="text-red-500 text-xs" aria-live="polite" role="alert">{errors.date}</div>}
            <label htmlFor="description" className="sr-only">Description</label>
            <input
              id="description"
              type="text"
              className="w-full rounded border border-nextgen-primary px-3 py-3 text-base md:text-lg focus:ring-2 focus:ring-nextgen-primary bg-nextgen-background text-nextgen-accent"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Description"
              required
              disabled={loading}
              aria-label="Description"
              aria-describedby={errors.description ? "description-error" : undefined}
            />
            {errors.description && <div id="description-error" className="text-red-500 text-xs" aria-live="polite" role="alert">{errors.description}</div>}
          </div>
          {/* Audit Trail Section (edit mode only) */}
          {isEdit && (
            <div className="mt-6">
              <button
                type="button"
                className="text-nextgen-primary underline text-sm mb-2"
                onClick={() => setShowAudit(v => !v)}
                aria-expanded={showAudit}
                aria-controls="audit-log-section"
              >
                {showAudit ? "Hide" : "Show"} Audit Trail
              </button>
              {showAudit && (
                <div id="audit-log-section" className="bg-gray-50 border border-gray-200 rounded p-3 max-h-60 overflow-y-auto text-xs">
                  {auditLog.length === 0 ? (
                    <div className="text-gray-400">No history yet.</div>
                  ) : (
                    <ul className="space-y-2">
                      {auditLog.map((log, i) => (
                        <li key={log.id || i} className="border-b last:border-b-0 pb-2">
                          <div className="font-semibold text-nextgen-primary">{log.action.toUpperCase()} <span className="text-gray-400">by {log.userId}</span></div>
                          <div className="text-gray-400">{new Date(log.createdAt).toLocaleString()}</div>
                          <div className="mt-1">
                            <span className="font-semibold">Before:</span>
                            <pre className="bg-gray-100 rounded p-1 overflow-x-auto">{log.before ? JSON.stringify(log.before, null, 2) : "-"}</pre>
                          </div>
                          <div className="mt-1">
                            <span className="font-semibold">After:</span>
                            <pre className="bg-gray-100 rounded p-1 overflow-x-auto">{log.after ? JSON.stringify(log.after, null, 2) : "-"}</pre>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)} type="button" disabled={loading || showCheckmark} className="border border-nextgen-primary text-nextgen-primary hover:bg-nextgen-primary hover:text-nextgen-accent focus-visible:ring-2 focus-visible:ring-nextgen-primary px-4 py-2 rounded-md min-w-[100px]">
              Cancel
            </Button>
            <Button type="submit" disabled={loading || showCheckmark} className="bg-nextgen-primary text-nextgen-accent hover:bg-nextgen-accent hover:text-nextgen-primary focus-visible:ring-2 focus-visible:ring-nextgen-primary px-4 py-2 rounded-md min-w-[100px] font-semibold">
              {loading ? <span className="animate-spin mr-2 w-4 h-4 border-2 border-t-transparent border-white rounded-full inline-block align-middle" /> : null}
              {loading ? (isEdit ? "Saving..." : "Saving...") : isEdit ? "Save Changes" : "Save"}
            </Button>
          </DialogFooter>
        </form>
        <AnimatePresence>
          {showCheckmark && (
            <motion.div
              key="checkmark"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", stiffness: 200, damping: 18, duration: 0.4 }}
              className="absolute inset-0 flex items-center justify-center bg-nextgen-background/80 z-50"
              aria-live="polite"
              role="status"
            >
              <CheckIcon className="text-nextgen-primary drop-shadow-lg" size={36} aria-label="Success" />
              <span className="sr-only">Transaction saved successfully</span>
            </motion.div>
          )}
        </AnimatePresence>
        {/* Unsaved changes confirmation dialog */}
        <Dialog open={showConfirm} onOpenChange={setShowConfirm}>
          <DialogContent className="max-w-xs">
            <DialogHeader>
              <DialogTitle>Discard changes?</DialogTitle>
              <DialogDescription>
                You have unsaved changes. Are you sure you want to close?
              </DialogDescription>
            </DialogHeader>
            <div className="flex gap-4 justify-center">
              <Button onClick={handleCancelClose} className="border border-nextgen-primary text-nextgen-primary hover:bg-nextgen-primary hover:text-nextgen-accent focus-visible:ring-2 focus-visible:ring-nextgen-primary px-4 py-2 rounded-md min-w-[100px]">Cancel</Button>
              <Button onClick={handleConfirmClose} className="bg-nextgen-primary text-nextgen-accent hover:bg-nextgen-accent hover:text-nextgen-primary focus-visible:ring-2 focus-visible:ring-nextgen-primary px-4 py-2 rounded-md min-w-[100px] font-semibold">Discard</Button>
            </div>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
} 