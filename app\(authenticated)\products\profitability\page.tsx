import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { eq } from "drizzle-orm";
import { headers } from "next/headers";
import ProfitabilityClient from "@/components/protectedPages/Products/ProfitabilityClient";

// Helper to get user's organizationId (copied from clients/vendors)
async function getUserOrganizationId(userId: string) {
  const { organizationMembers } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: organizationMembers.organizationId })
    .from(organizationMembers)
    .where(eq(organizationMembers.userId, userId));
  return orgs[0]?.organizationId || null;
}

export default async function ProfitabilityPage() {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;
  if (!userId) {
    return <div className="container mx-auto py-10">Not authenticated.</div>;
  }
  const organizationId = await getUserOrganizationId(userId);
  if (!organizationId) {
    return <div className="container mx-auto py-10">No organization found.</div>;
  }
  // Fetch profitability data (for now, fetch all products; can be extended for more detailed profitability)
  const profitabilityData = await db
    .select()
    .from(products)
    .where(eq(products.organizationId, organizationId));

  return <ProfitabilityClient data={profitabilityData} />;
}
