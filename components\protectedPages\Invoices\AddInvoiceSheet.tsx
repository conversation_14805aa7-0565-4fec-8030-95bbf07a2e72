"use client";

import React, { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { toast } from "sonner";
import { 
  Plus, 
  Trash, 
  FileText, 
  User, 
  Calendar, 
  DollarSign, 
  Loader2, 
  Check, 
  ChevronsUpDown,
  MessageSquare
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getInvoiceById } from "@/lib/actions/invoices/get-invoice-by-id";
import { getClients } from "@/lib/actions/clients/get-clients";
import { getProducts } from "@/lib/actions/products/get-products";
import { getInvoices } from "@/lib/actions/invoices/get-invoices";
import { z } from "zod";
import { invoiceFormSchema } from "@/lib/actions/invoices/invoice.schema";
import { cn } from "@/lib/utils";
import { createInvoice } from "@/lib/actions/invoices/create-invoice";
import { updateInvoice } from "@/lib/actions/invoices/update-invoice";
import { refundClientCredit } from "@/lib/actions/invoices/refund-client-credit";

type InvoiceFormData = z.infer<typeof invoiceFormSchema>;

interface Client {
  id: string;
  name: string;
}

interface AddInvoiceSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: InvoiceFormData) => void;
  invoiceId?: string | null;
  isPending: boolean;
}

export default function AddInvoiceSheet({
  isOpen,
  onClose,
  onSubmit,
  invoiceId,
  isPending,
}: AddInvoiceSheetProps) {
  const [popoverOpen, setPopoverOpen] = React.useState(false);
  const isEdit = !!invoiceId;
  const [clientCredit, setClientCredit] = React.useState<number>(0);
  const [appliedCredit, setAppliedCredit] = React.useState<number>(0);

  // Fetch invoice details if editing
  const { data: invoice, isLoading: isLoadingInvoice } = useQuery({
    queryKey: ["invoice", invoiceId],
    queryFn: () => invoiceId ? getInvoiceById(invoiceId) : null,
    enabled: !!invoiceId && isOpen,
  });

  // Fetch clients
  const { data: clients = [], isLoading: isLoadingClients } = useQuery({
    queryKey: ["clients"],
    queryFn: getClients,
  });

  const today = new Date();
  const defaultDueDate = new Date();
  defaultDueDate.setDate(today.getDate() + 30); // 30 days from today

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      issueDate: today,
      dueDate: defaultDueDate,
      status: "draft",
      lineItems: [{ description: "", quantity: 1, unitPrice: 0.01, totalPrice: 0.01 }],
      totalAmount: 0.01,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "lineItems",
  });

  useEffect(() => {
    if (isOpen && isEdit && invoice) {
      reset({
        clientId: invoice.clientId,
        status: (invoice.status as "draft" | "sent" | "paid") || "draft",
        issueDate: new Date(invoice.date),
        dueDate: invoice.dueDate ? new Date(invoice.dueDate) : new Date(),
        totalAmount: parseFloat(invoice.total),
        notes: invoice.notes || "",
        lineItems: invoice.lineItems && invoice.lineItems.length > 0 ? invoice.lineItems : [{ description: "", quantity: 1, unitPrice: 0.01, totalPrice: 0.01 }],
      });
    } else if (isOpen && !isEdit) {
      const today = new Date();
      const defaultDueDate = new Date();
      defaultDueDate.setDate(today.getDate() + 30);
      
      reset({
        clientId: "",
        status: "draft",
        issueDate: today,
        dueDate: defaultDueDate,
        lineItems: [{ description: "", quantity: 1, unitPrice: 0.01, totalPrice: 0.01 }],
        totalAmount: 0.01,
        notes: "",
      });
    }
  }, [invoice, reset, isOpen, isEdit]);

  // Watch for client selection and fetch overpaid credit
  useEffect(() => {
    const clientId = watch("clientId");
    if (clientId) {
      getInvoices().then((allInvoices) => {
        const credit = allInvoices
          .filter(inv => inv.clientId === clientId && Number(inv.overpaid) > 0)
          .reduce((sum, inv) => sum + Number(inv.overpaid), 0);
        setClientCredit(credit);
        setAppliedCredit(0); // Reset if client changes
      });
    } else {
      setClientCredit(0);
      setAppliedCredit(0);
    }
  }, [watch("clientId")]);

  const lineItems = watch("lineItems") || [];
  const total = lineItems.reduce((sum, item) => sum + (item.quantity || 0) * (item.unitPrice || 0), 0);

  // Update total amount whenever line items change
  useEffect(() => {
    setValue("totalAmount", Math.max(0.01, total));
  }, [total, setValue]);

  // Adjust total with applied credit
  const adjustedTotal = Math.max(0, total - appliedCredit);

  const handleFormSubmit = (data: InvoiceFormData) => {
    console.log("Form validation errors:", errors);
    console.log("Raw form data:", data);
    
    // Transform the data to match the schema requirements
    const transformedData = {
      ...data,
      totalAmount: Math.max(0.01, total - appliedCredit),
      lineItems: data.lineItems.map(item => ({
        productId: item.productId || undefined, // Use undefined for optional field
        description: item.description.trim(),
        quantity: Math.max(1, item.quantity || 1),
        unitPrice: Math.max(0.01, item.unitPrice || 0.01),
        totalPrice: Math.max(0.01, (item.quantity || 1) * (item.unitPrice || 0.01))
      })),
      dueDate: data.dueDate || data.issueDate,
    };
    
    console.log("Transformed data being submitted:", transformedData);
    
    // Validate the transformed data
    const validation = invoiceFormSchema.safeParse(transformedData);
    if (!validation.success) {
      console.error("Validation failed:", validation.error);
      toast.error("Please fix the form errors before submitting.");
      return;
    }
    
    console.log("✅ Validation passed! Calling onSubmit function...");
    try {
      onSubmit(transformedData);
      console.log("✅ onSubmit function called successfully");
    } catch (error) {
      console.error("❌ Error in onSubmit function:", error);
      toast.error("An error occurred while submitting the invoice.");
    }
  };

  const formatDateForInput = (date: Date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "sent":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "paid":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const handleSheetClose = (open: boolean) => {
    if (!isPending && !isLoadingInvoice) {
      onClose();
    }
  };

  // Show loading state
  if ((isEdit && isLoadingInvoice) || isLoadingClients) {
    return (
      <Sheet open={isOpen} onOpenChange={handleSheetClose}>
        <SheetContent 
          side="right" 
          className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
          aria-describedby="loading-description"
        >
          <SheetHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
            <SheetTitle className="text-lg sm:text-xl md:text-2xl flex items-center gap-2">
              <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
              {isEdit ? "Edit Invoice" : "Create New Invoice"}
            </SheetTitle>
            <SheetDescription id="loading-description">
              Loading invoice details, please wait...
            </SheetDescription>
          </SheetHeader>
          <div className="flex items-center justify-center py-12 sm:py-16">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 sm:ml-3 text-sm">Loading invoice details...</span>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  const handleRefundCredit = async () => {
    const clientId = watch("clientId");
    if (!clientId || clientCredit <= 0) return;
    const result = await refundClientCredit(clientId, clientCredit);
    toast.success(result.message);
    // Refresh available credit
    getInvoices().then((allInvoices) => {
      const credit = allInvoices
        .filter(inv => inv.clientId === clientId && Number(inv.overpaid) > 0)
        .reduce((sum, inv) => sum + Number(inv.overpaid), 0);
      setClientCredit(credit);
      setAppliedCredit(0);
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={handleSheetClose}>
      <SheetContent 
        side="right" 
        className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
        aria-describedby="invoice-form-description"
      >
        <SheetHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
          <SheetTitle className="text-lg sm:text-xl md:text-2xl flex items-center gap-2">
            <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
            {isEdit ? "Edit Invoice" : "Create New Invoice"}
          </SheetTitle>
          <SheetDescription id="invoice-form-description">
            {isEdit ? "Modify the invoice details below" : "Fill out the form below to create a new invoice"}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit(handleFormSubmit, (errors) => {
            console.log("Form validation failed:", errors);
            toast.error("Please fix the form errors before submitting.");
          })} className="h-full flex flex-col">
            <div className="flex-1 px-4 pb-4 sm:px-6 sm:pb-6 space-y-4 sm:space-y-6 mt-4 sm:mt-6">
              
              {/* Invoice Information Card */}
              <Card className="shadow-sm">
                <CardHeader className="pb-3 sm:pb-4">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                    <CardTitle className="text-base sm:text-lg">Invoice Information</CardTitle>
                  </div>
                  <CardDescription className="text-xs sm:text-sm">
                    Enter the basic invoice details and client information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3 sm:space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                    {/* Client Selection */}
                    <div className="space-y-1 sm:space-y-2 md:col-span-2">
                      <Label htmlFor="clientId" className="text-xs sm:text-sm font-medium">Client *</Label>
                      <input type="hidden" {...register("clientId")} />
                      <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            id="clientId"
                            type="button"
                            variant="outline"
                            role="combobox"
                            className="w-full justify-between h-8 sm:h-10"
                            disabled={isLoadingClients}
                          >
                            {watch("clientId")
                              ? clients.find((c: Client) => c.id === watch("clientId"))?.name
                              : "Select client..."}
                            <ChevronsUpDown className="ml-2 h-3 w-3 sm:h-4 sm:w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput placeholder="Search clients..." />
                            <CommandEmpty>No client found.</CommandEmpty>
                            <CommandGroup>
                              {clients.map((client: Client) => (
                                <CommandItem
                                  key={client.id}
                                  value={client.name}
                                  onSelect={(currentValue) => {
                                    const selectedClient = clients.find(c => c.name.toLowerCase() === currentValue.toLowerCase());
                                    if (selectedClient) {
                                      setValue("clientId", selectedClient.id, { shouldValidate: true });
                                    }
                                    setPopoverOpen(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      watch("clientId") === client.id
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {client.name}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      {errors.clientId && (
                        <p className="text-red-500 text-xs">{errors.clientId.message}</p>
                      )}
                    </div>

                    {/* Issue Date */}
                    <div className="space-y-1 sm:space-y-2">
                      <Label htmlFor="issueDate" className="text-xs sm:text-sm font-medium">Issue Date *</Label>
                      <Input 
                        id="issueDate"
                        type="date" 
                        value={watch("issueDate") ? formatDateForInput(watch("issueDate")) : formatDateForInput(today)}
                        onChange={(e) => setValue("issueDate", new Date(e.target.value), { shouldValidate: true })}
                        className="h-8 sm:h-10"
                      />
                      {errors.issueDate && (
                        <p className="text-red-500 text-xs">{errors.issueDate.message}</p>
                      )}
                    </div>

                    {/* Due Date */}
                    <div className="space-y-1 sm:space-y-2">
                      <Label htmlFor="dueDate" className="text-xs sm:text-sm font-medium">Due Date *</Label>
                      <Input 
                        id="dueDate"
                        type="date" 
                        value={watch("dueDate") ? formatDateForInput(watch("dueDate") ?? today) : formatDateForInput(defaultDueDate)}
                        onChange={(e) => setValue("dueDate", new Date(e.target.value), { shouldValidate: true })}
                        className="h-8 sm:h-10"
                      />
                      {errors.dueDate && (
                        <p className="text-red-500 text-xs">{errors.dueDate.message}</p>
                      )}
                    </div>

                    {/* Status */}
                    <div className="space-y-1 sm:space-y-2 md:col-span-2">
                      <Label htmlFor="status" className="text-xs sm:text-sm font-medium">Status</Label>
                      <div className="flex items-center gap-2">
                        <Select 
                          onValueChange={(value: "draft" | "sent" | "paid") => setValue("status", value, { shouldValidate: true })} 
                          defaultValue="draft"
                          value={watch("status")}
                        >
                          <SelectTrigger id="status" className="h-8 sm:h-10 w-auto min-w-32">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="sent">Sent</SelectItem>
                            <SelectItem value="paid">Paid</SelectItem>
                          </SelectContent>
                        </Select>
                        <Badge className={`${getStatusBadgeColor(watch("status") || "draft")} text-xs px-2 py-1`}>
                          {watch("status") || "draft"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Show available credit if any */}
              {clientCredit > 0 && (
                <Card className="mb-4 border-green-400">
                  <CardHeader>
                    <CardTitle className="text-green-700 text-base">Available Client Credit</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col gap-2">
                      <span className="text-green-700 font-semibold">
                        ${clientCredit.toFixed(2)} available
                      </span>
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        disabled={appliedCredit > 0}
                        onClick={() => setAppliedCredit(clientCredit > total ? total : clientCredit)}
                      >
                        Apply Credit
                      </Button>
                      <Button
                        type="button"
                        size="sm"
                        variant="destructive"
                        onClick={handleRefundCredit}
                        className="ml-2"
                      >
                        Refund Credit
                      </Button>
                      {appliedCredit > 0 && (
                        <span className="text-green-600 text-sm">Applied: ${appliedCredit.toFixed(2)}</span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Line Items Card */}
              <Card className="shadow-sm">
                <CardHeader className="pb-3 sm:pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                      <CardTitle className="text-base sm:text-lg">Line Items</CardTitle>
                    </div>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => append({ description: "", quantity: 1, unitPrice: 0.01, totalPrice: 0.01 })}
                      className="h-7 sm:h-8 px-2 sm:px-3"
                    >
                      <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      <span className="text-xs sm:text-sm">Add Item</span>
                    </Button>
                  </div>
                  <CardDescription className="text-xs sm:text-sm">
                    Add items and services for this invoice
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Line Items Header - Desktop Only */}
                  <div className="hidden md:grid md:grid-cols-11 gap-2 text-xs font-medium text-gray-500 dark:text-gray-400 pb-2 border-b">
                    <div className="col-span-5">Description</div>
                    <div className="col-span-2 text-center">Quantity</div>
                    <div className="col-span-2 text-center">Unit Price</div>
                    <div className="col-span-1 text-right">Total</div>
                    <div className="col-span-1"></div>
                  </div>

                  {/* Line Items */}
                  <div className="space-y-3">
                    {fields.map((field, index) => (
                      <div key={field.id} className="grid grid-cols-1 md:grid-cols-11 gap-2 sm:gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border">
                        {/* Description */}
                        <div className="md:col-span-5 space-y-1">
                          <Label htmlFor={`description-${index}`} className="text-xs text-gray-600 md:hidden">Description</Label>
                          <Input 
                            id={`description-${index}`}
                            {...register(`lineItems.${index}.description`)} 
                            placeholder="Item description"
                            className="h-8 sm:h-9"
                          />
                        </div>

                        {/* Quantity */}
                        <div className="md:col-span-2 space-y-1">
                          <Label htmlFor={`quantity-${index}`} className="text-xs text-gray-600 md:hidden">Quantity</Label>
                          <Input 
                            id={`quantity-${index}`}
                            type="number" 
                            {...register(`lineItems.${index}.quantity`, { valueAsNumber: true })} 
                            placeholder="Qty"
                            className="h-8 sm:h-9"
                            min="1"
                            step="1"
                          />
                        </div>

                        {/* Unit Price */}
                        <div className="md:col-span-2 space-y-1">
                          <Label htmlFor={`unitPrice-${index}`} className="text-xs text-gray-600 md:hidden">Unit Price</Label>
                          <Input 
                            id={`unitPrice-${index}`}
                            type="number" 
                            {...register(`lineItems.${index}.unitPrice`, { valueAsNumber: true })} 
                            placeholder="0.01"
                            className="h-8 sm:h-9"
                            min="0.01"
                            step="0.01"
                          />
                        </div>

                        {/* Total */}
                        <div className="md:col-span-1 space-y-1">
                          <Label className="text-xs text-gray-600 md:hidden">Total</Label>
                          <div className="h-8 sm:h-9 flex items-center justify-end text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100">
                            ${((lineItems[index]?.quantity || 0) * (lineItems[index]?.unitPrice || 0)).toFixed(2)}
                          </div>
                        </div>

                        {/* Delete Button */}
                        <div className="md:col-span-1 flex justify-end md:justify-center">
                          <Button 
                            type="button" 
                            variant="ghost" 
                            size="sm"
                            onClick={() => remove(index)} 
                            disabled={fields.length === 1}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                          >
                            <Trash className="h-3 w-3 sm:h-4 sm:w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Total Summary */}
                  <Separator />
                  <div className="flex justify-between items-center pt-2">
                    <span className="text-sm sm:text-base font-medium text-gray-600 dark:text-gray-400">
                      Total Amount:
                    </span>
                    <span className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100">
                      ${adjustedTotal.toFixed(2)}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Notes Card */}
              <Card className="shadow-sm">
                <CardHeader className="pb-3 sm:pb-4">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                    <CardTitle className="text-base sm:text-lg">Additional Notes</CardTitle>
                  </div>
                  <CardDescription className="text-xs sm:text-sm">
                    Add any additional information or terms for this invoice
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Label htmlFor="notes" className="sr-only">Additional Notes</Label>
                  <Textarea 
                    id="notes"
                    {...register("notes")} 
                    placeholder="Enter any additional notes or terms..."
                    className="min-h-20 resize-none"
                  />
                  {errors.notes && (
                    <p className="text-red-500 text-xs mt-1">{errors.notes.message}</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="border-t bg-gray-50 dark:bg-gray-900/50 px-4 py-3 sm:px-6 sm:py-4">
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 sm:justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isPending}
                  className="flex-1 sm:flex-none px-4 py-2 text-sm"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isPending}
                  className="flex-1 sm:flex-none px-4 py-2 text-sm"
                >
                  {isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      {isEdit ? "Update Invoice" : "Create Invoice"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </SheetContent>
    </Sheet>
  );
} 