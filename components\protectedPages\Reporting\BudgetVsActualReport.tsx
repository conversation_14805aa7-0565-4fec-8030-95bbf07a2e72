import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface BudgetLineItem {
  accountId: string;
  accountName: string;
  accountType: string;
  budgetedAmount: number;
  actualAmount: number;
  variance: number;
  variancePercent: number;
  period: string;
}

interface BudgetSummary {
  totalBudgetedRevenue: number;
  totalActualRevenue: number;
  revenueVariance: number;
  revenueVariancePercent: number;
  totalBudgetedExpenses: number;
  totalActualExpenses: number;
  expenseVariance: number;
  expenseVariancePercent: number;
  budgetedNetIncome: number;
  actualNetIncome: number;
  netIncomeVariance: number;
  netIncomeVariancePercent: number;
}

interface BudgetVsActualData {
  lineItems: BudgetLineItem[];
  summary: BudgetSummary;
  period: string;
}

interface Props {
  data: BudgetVsActualData;
}

export default function BudgetVsActualReport({ data }: Props) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(1)}%`;
  };

  const getVarianceColor = (variance: number, isExpense = false) => {
    // For expenses, positive variance (over budget) is bad, negative is good
    // For revenue, positive variance (over budget) is good, negative is bad
    if (isExpense) {
      return variance > 0 ? "text-red-600" : variance < 0 ? "text-green-600" : "text-gray-600";
    } else {
      return variance > 0 ? "text-green-600" : variance < 0 ? "text-red-600" : "text-gray-600";
    }
  };

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <TrendingUp className="w-4 h-4" />;
    if (variance < 0) return <TrendingDown className="w-4 h-4" />;
    return <Minus className="w-4 h-4" />;
  };

  const getVarianceBadge = (variancePercent: number, isExpense = false) => {
    const absPercent = Math.abs(variancePercent);
    let variant = "secondary";
    let label = "On Track";

    if (absPercent > 15) {
      variant = "destructive";
      label = "High Variance";
    } else if (absPercent > 5) {
      variant = "outline";
      label = "Moderate Variance";
    }

    return <Badge variant={variant as any}>{label}</Badge>;
  };

  const getBudgetUtilization = (actual: number, budget: number) => {
    if (budget === 0) return 0;
    return Math.min((actual / budget) * 100, 100);
  };

  // Group line items by account type
  const groupedItems = data.lineItems.reduce((acc, item) => {
    if (!acc[item.accountType]) {
      acc[item.accountType] = [];
    }
    acc[item.accountType].push(item);
    return acc;
  }, {} as Record<string, BudgetLineItem[]>);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Revenue Summary */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-green-600">Revenue Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Budgeted</span>
                <span className="font-medium">{formatCurrency(data.summary.totalBudgetedRevenue)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Actual</span>
                <span className="font-medium">{formatCurrency(data.summary.totalActualRevenue)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Variance</span>
                <span className={`font-medium ${getVarianceColor(data.summary.revenueVariance)}`}>
                  {formatCurrency(data.summary.revenueVariance)} ({formatPercentage(data.summary.revenueVariancePercent)})
                </span>
              </div>
              <Progress value={getBudgetUtilization(data.summary.totalActualRevenue, data.summary.totalBudgetedRevenue)} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Expense Summary */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-orange-600">Expense Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Budgeted</span>
                <span className="font-medium">{formatCurrency(data.summary.totalBudgetedExpenses)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Actual</span>
                <span className="font-medium">{formatCurrency(data.summary.totalActualExpenses)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Variance</span>
                <span className={`font-medium ${getVarianceColor(data.summary.expenseVariance, true)}`}>
                  {formatCurrency(data.summary.expenseVariance)} ({formatPercentage(data.summary.expenseVariancePercent)})
                </span>
              </div>
              <Progress value={getBudgetUtilization(data.summary.totalActualExpenses, data.summary.totalBudgetedExpenses)} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Net Income Summary */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-blue-600">Net Income Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Budgeted</span>
                <span className="font-medium">{formatCurrency(data.summary.budgetedNetIncome)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Actual</span>
                <span className="font-medium">{formatCurrency(data.summary.actualNetIncome)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Variance</span>
                <span className={`font-medium ${getVarianceColor(data.summary.netIncomeVariance)}`}>
                  {formatCurrency(data.summary.netIncomeVariance)} ({formatPercentage(data.summary.netIncomeVariancePercent)})
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Budget vs Actual Table */}
      <Card>
        <CardHeader>
          <CardTitle>Budget vs Actual Analysis</CardTitle>
          <CardDescription>
            Detailed comparison by account for {data.period}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {Object.entries(groupedItems).map(([accountType, items]) => (
            <div key={accountType} className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-100 bg-gray-800 px-4 py-2 rounded-md">{accountType}</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account</TableHead>
                    <TableHead className="text-right">Budgeted</TableHead>
                    <TableHead className="text-right">Actual</TableHead>
                    <TableHead className="text-right">Variance</TableHead>
                    <TableHead className="text-right">% Variance</TableHead>
                    <TableHead className="text-center">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item) => (
                    <TableRow key={item.accountId}>
                      <TableCell className="font-medium">{item.accountName}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.budgetedAmount)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.actualAmount)}</TableCell>
                      <TableCell className={`text-right ${getVarianceColor(item.variance, accountType === "Expense" || accountType === "COGS")}`}>
                        <div className="flex items-center justify-end space-x-1">
                          {getVarianceIcon(item.variance)}
                          <span>{formatCurrency(item.variance)}</span>
                        </div>
                      </TableCell>
                      <TableCell className={`text-right ${getVarianceColor(item.variance, accountType === "Expense" || accountType === "COGS")}`}>
                        {formatPercentage(item.variancePercent)}
                      </TableCell>
                      <TableCell className="text-center">
                        {getVarianceBadge(item.variancePercent, accountType === "Expense" || accountType === "COGS")}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
} 