"use client";

import { useEffect, useState } from "react";
import { getProductOptimizationSuggestions } from "@/lib/actions/products/get-product-optimization-suggestions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Lightbulb, AlertTriangle } from "lucide-react";

interface OptimizationSuggestionsProps {
  productId: string;
}

export function OptimizationSuggestions({
  productId,
}: OptimizationSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSuggestions = async () => {
      setIsLoading(true);
      try {
        const result = await getProductOptimizationSuggestions(productId);
        if (result.success) {
          setSuggestions(result.suggestions);
        } else {
          setError(result.message || "Failed to load suggestions.");
        }
      } catch (err) {
        setError("An unexpected error occurred.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuggestions();
  }, [productId]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Error</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (suggestions.length === 0) {
    return null; // Don't show the card if there are no suggestions
  }

  return (
    <Card className="bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800 dark:text-blue-300">
          <Lightbulb className="h-5 w-5" />
          <span>Optimization Suggestions</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2 list-disc pl-5">
          {suggestions.map((suggestion, index) => (
            <li key={index} className="text-sm text-blue-700 dark:text-blue-200">
              {suggestion}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
} 