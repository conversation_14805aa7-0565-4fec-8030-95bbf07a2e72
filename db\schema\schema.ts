import {
	pgTable,
	pgPolicy,
	text,
	timestamp,
	primaryKey,
	pgEnum,
	boolean,
	varchar,
	decimal,
	integer,
	jsonb,
	unique,
	index,
  } from "drizzle-orm/pg-core";
  import { sql, relations } from "drizzle-orm";
  import { authUid } from "drizzle-orm/neon"
  // Core Schema settings
  
  // -- AUTHENTICATION (from better-auth) --
	
  export const user = pgTable("user", {
	id: text('id').primaryKey(),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	emailVerified: boolean('email_verified').$defaultFn(() => false).notNull(),
	image: text('image'),
	onboarded: boolean('onboarded').$defaultFn(() => false).notNull(),
	twoFactorEnabled: boolean('two_factor_enabled').$defaultFn(() => false).notNull(),
	createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
	updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
  }, (table) => ({
	rlsPolicy: pgPolicy('user_rls_policy', {
	  for: 'all',
	  using: authUid(table.id)
	})
  }));
  
  export const session = pgTable("session", {
	id: text('id').primaryKey(),
	expiresAt: timestamp('expires_at').notNull(),
	token: text('token').notNull().unique(),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull(),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),
	activeOrganizationId: text('active_organization_id')
  }, (table) => ({
	rlsPolicy: pgPolicy('session_rls_policy', {
	  for: 'all',
	  using: sql`auth.uid() = ${table.userId}`
	})
  }));
  
  export const account = pgTable("account", {
	id: text('id').primaryKey(),
	accountId: text('account_id').notNull(),
	providerId: text('provider_id').notNull(),
	userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),
	accessToken: text('access_token'),
	refreshToken: text('refresh_token'),
	idToken: text('id_token'),
	accessTokenExpiresAt: timestamp('access_token_expires_at'),
	refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
	scope: text('scope'),
	password: text('password'),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull(),
  }, (table) => ({
	rlsPolicy: pgPolicy('account_rls_policy', {
	  for: 'all',
	  using: sql`auth.uid() = ${table.userId}`
	})
  }));
  
  export const verification = pgTable("verification", {
	id: text('id').primaryKey(),
	identifier: text('identifier').notNull(),
	value: text('value').notNull(),
	expiresAt: timestamp('expires_at').notNull(),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull()
  }, (table) => ({
	rlsPolicy: pgPolicy('verification_rls_policy', {
	  for: 'all',
	  using: sql`true` // Verification tokens are public for auth purposes
	})
  }));

  export const twoFactor = pgTable("two_factor", {
	id: text('id').primaryKey(),
	secret: text('secret').notNull(),
	backupCodes: text('backup_codes').notNull(),
	userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  }, (table) => ({
	rlsPolicy: pgPolicy('two_factor_rls_policy', {
	  for: 'all',
	  using: sql`auth.uid() = ${table.userId}`
	})
  }));

    export const businessTypeEnum = pgEnum("business_type", [
	"physical",  // Physical products, inventory, shipping
	"digital",   // Digital products/services, no physical inventory
	"hybrid"     // Mix of physical and digital
    ]);

	export const organization = pgTable("organization", {
	  id: text("id").primaryKey(),
	  name: text("name").notNull(),
	  slug: text('slug').unique(),
	  logo: text("logo"),
	  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	  metadata: text('metadata'),
	  businessType: businessTypeEnum("business_type"), 
	  email: text("email"),
	  industry: text("industry"),
	  website: text("website"),
	  phone: text("phone"),
	  taxId: text("tax_id"),
	  taxIdType: text("tax_id_type"),
	  currency: text("currency"),
	  legalEntity: text("legal_entity"),
	  howHeard: text("how_heard"),
	  subscriptionId: text("subscription_id"),
	  planType: varchar("plan_type", { length: 50 }).default("trial"),
	  planStatus: varchar("plan_status", { length: 50 }).default("trial"),
	  trialEndsAt: timestamp("trial_ends_at", { withTimezone: true }),
	  subscriptionCurrentPeriodStart: timestamp("subscription_current_period_start", { withTimezone: true }),
	  subscriptionCurrentPeriodEnd: timestamp("subscription_current_period_end", { withTimezone: true }),
	  monthlyTransactionLimit: integer("monthly_transaction_limit").default(50),
	  monthlyTransactionCount: integer("monthly_transaction_count").default(0),
	  lastTransactionResetDate: timestamp("last_transaction_reset_date", { withTimezone: true }),
	  isActive: boolean("is_active").default(true),
	  deactivatedAt: timestamp("deactivated_at", { withTimezone: true }),
	  deactivationReason: varchar("deactivation_reason", { length: 100 }), // 'subscription_cancelled', 'user_requested', 'violation'
	  gracePeriodEndsAt: timestamp("grace_period_ends_at", { withTimezone: true }),
	  requireTwoFactor: boolean("require_two_factor").default(false).notNull(),
	  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
	}, (table) => ({
	  rlsPolicy: pgPolicy('organization_rls_policy', {
		for: 'all',
		using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.id} AND "member"."user_id" = auth.uid())`
	  })
	}));

	export type Organization = typeof organization.$inferSelect;
	
    export const organizationAddresses = pgTable("organization_addresses", {
		id: text("id").primaryKey(),
		organizationId: text("organization_id").notNull().references(() => organization.id, { onDelete: "cascade" }),
		type: text("type").notNull(), // e.g., 'primary', 'mailing', 'billing', 'shipping'
		streetAddress: text("street_address"),
		address2: text("address2"),
		city: text("city"),
		zipCode: text("zip_code"),
		country: text("country"),
		timeZone: text("time_zone"),
		createdAt: timestamp("created_at").defaultNow(),
		updatedAt: timestamp("updated_at").defaultNow(),
	  }, (table) => ({
		rlsPolicy: pgPolicy('organization_addresses_rls_policy', {
		  for: 'all',
		  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
		})
	  }));

	export const member = pgTable(
	  "member",
	  {
		id: text("id").primaryKey(),
		organizationId: text('organization_id').notNull().references(()=> organization.id, { onDelete: 'cascade' }),
		userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),
		role: text('role').default("member").notNull(),
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	  },
	  (table) => ({
		unique_org_user: unique("unique_org_user_idx").on(table.organizationId, table.userId),
		rlsPolicy: pgPolicy('member_rls_policy', {
		  for: 'all',
		  using: sql`EXISTS (SELECT 1 FROM "member" AS om_check WHERE om_check."organization_id" = ${table.organizationId} AND om_check."user_id" = auth.uid())`
		})
	  })
	);
	
	export const invitation = pgTable(
	  "invitation",
	  {
		id: text("id").primaryKey(),
		organizationId: text('organization_id').notNull().references(()=> organization.id, { onDelete: 'cascade' }),
		email: text("email").notNull(),
		role: text('role'),
		status: text('status').default("pending").notNull(),
		expiresAt: timestamp('expires_at').notNull(),
		inviterId: text('inviter_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),
	  },
	  (table) => ({
		rlsPolicy: pgPolicy('invitation_rls_policy', {
		  for: 'all',
		  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
		})
	  })
	);

  export const onboarding = pgTable("onboarding", {
	id: text("id").primaryKey(),
	userId: text("user_id").references(() => user.id, { onDelete: "cascade" }).notNull(),
	// For multi-org onboarding: nullable at first, set after org creation
	organizationId: text("organization_id").references(() => organization.id, { onDelete: "cascade" }),
	step: integer("step").default(1).notNull(),
	totalSteps: integer("total_steps").default(7).notNull(), // Updated from 6 to 7
	// Step 1: Organization Basics
	orgBasics: jsonb("org_basics"), // { name, slug, logo }
	// Step 2: Organization Details
	orgDetails: jsonb("org_details"), // { address, address2, city, zipCode, country, timeZone, phone, website }
	// Step 3: Business Details
	businessDetails: jsonb("business_details"), // { businessType, industry, chartTemplate }
	// Step 4: Subscription
	subscription: jsonb("subscription"), // { planType, paymentInfo }
	// Step 5: Legal Consent & Review
	legalConsent: boolean("legal_consent").default(false),
	legalConsentAcceptedAt: timestamp("legal_consent_accepted_at"),
	// Step 6: Success/Completion screen (sets isCompleted to true)
	// No additional fields needed; handled by isCompleted and completedAt
	isCompleted: boolean("is_completed").default(false),
	completedAt: timestamp("completed_at"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
  }, (table) => ({
	userOrgUnique: unique("unique_user_org_onboarding").on(table.userId, table.organizationId),
	userIdIdx: index("idx_isCompleted_user_id").on(table.userId),
	rlsPolicy: pgPolicy('onboarding_rls_policy', {
	  for: 'all',
	  using: sql`auth.uid() = ${table.userId}`
	})
  }));
  
  export const businessTypeTemplates = pgTable("business_type_templates", {
	id: text("id").primaryKey(),
	businessType: businessTypeEnum("business_type").notNull(),
	templateName: text("template_name").notNull(), // e.g., "Standard Physical Business", "SaaS Company"
	description: text("description"),
	isDefault: boolean("is_default").default(false),
	// Chart of accounts template as JSON
	accountsTemplate: jsonb("accounts_template").notNull(), // Array of account objects
	// Metadata
	version: text("version").default("1.0"),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	businessTypeIdx: index("idx_business_type_templates_type").on(table.businessType),
	defaultTemplateIdx: index("idx_business_type_templates_default").on(table.isDefault),
	rlsPolicy: pgPolicy('business_type_templates_rls_policy', {
	  for: 'all',
	  using: sql`true` // Templates are public for all users
	})
  }));
  
  // -- ACCOUNTING --
  
  export const accountTypeEnum = pgEnum("account_type", [
	"asset",
	"liability",
	"equity",
	"revenue",
	"expense",
  ]);
  
  export const normalBalanceEnum = pgEnum("normal_balance", ["debit", "credit"]);
  
  // Enhanced classification enums for professional accounting standards
  export const accountClassificationEnum = pgEnum("account_classification", [
	"current",
	"non_current",
	"operating",
	"non_operating",
	"n_a", // Not applicable (for equity accounts, etc.)
  ]);
  
  export const financialStatementSectionEnum = pgEnum("financial_statement_section", [
	"current_assets",
	"non_current_assets", 
	"current_liabilities",
	"non_current_liabilities",
	"equity",
	"operating_revenue",
	"other_revenue",
	"cost_of_goods_sold",
	"operating_expenses",
	"other_expenses",
  ]);
  
  export const cashFlowCategoryEnum = pgEnum("cash_flow_category", [
	"operating",
	"investing", 
	"financing",
	"n_a", // Not applicable
  ]);
  
  export const accountGroupEnum = pgEnum("account_group", [
	// Asset Groups
	"cash_and_equivalents",
	"accounts_receivable",
	"inventory",
	"prepaid_expenses",
	"short_term_investments",
	"property_plant_equipment",
	"accumulated_depreciation",
	"intangible_assets",
	"long_term_investments",
	"other_assets",
	// Liability Groups
	"accounts_payable",
	"accrued_liabilities",
	"short_term_debt",
	"unearned_revenue",
	"long_term_debt",
	"other_liabilities",
	// Equity Groups
	"capital_stock",
	"retained_earnings",
	"other_equity",
	// Revenue Groups
	"product_sales",
	"service_revenue",
	"other_revenue",
	// Expense Groups
	"cost_of_sales",
	"selling_expenses",
	"administrative_expenses",
	"depreciation_amortization",
	"interest_expense",
	"other_expenses",
	// General
	"other",
  ]);
  
  /**
   * The Chart of Accounts for an organization.
   * Enhanced with professional accounting standards and regulatory compliance support.
   */
  export const accounts = pgTable("accounts", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	name: text("name").notNull(),
	accountNumber: varchar("account_number", { length: 20 }),
	type: accountTypeEnum("type").notNull(),
	normalBalance: normalBalanceEnum("normal_balance").notNull(),
	
	// Hierarchical structure
	isHeader: boolean("is_header").default(false),
	parentId: text("parent_id").references((): any => accounts.id, {
	  onDelete: "set null",
	}),
	level: integer("level").default(0), // 0=main category, 1=subcategory, 2=account group, 3=specific account
	
	// Professional accounting classifications
	classification: accountClassificationEnum("classification").default("n_a"),
	financialStatementSection: financialStatementSectionEnum("financial_statement_section"),
	accountGroup: accountGroupEnum("account_group").default("other"),
	cashFlowCategory: cashFlowCategoryEnum("cash_flow_category").default("n_a"),
	
	// Display and ordering
	displayOrder: integer("display_order").default(0),
	subcategory: text("subcategory"), // e.g., "Current Assets", "Property Plant Equipment"
	
	// Compliance and regulatory
	gaapCategory: text("gaap_category"), // GAAP-specific categorization
	ifrsCategory: text("ifrs_category"), // IFRS-specific categorization
	
	// Status and metadata
	isActive: boolean("is_active").default(true),
	description: text("description"),
	notes: text("notes"), // Internal notes for accountants
	
	// Timestamps
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	unique_org_account_number: unique("unique_org_account_number_idx").on(
	  table.organizationId,
	  table.accountNumber
	),
	rlsPolicy: pgPolicy('accounts_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  /**
   * The individual debit/credit entries for a given transaction.
   * This table enforces the double-entry bookkeeping system.
   */
  export const journalEntries = pgTable("journal_entries", {
	id: text("id").primaryKey(),
	transactionId: text("transaction_id")
	  .references(() => transactions.id, { onDelete: "cascade" })
	  .notNull(),
	accountId: text("account_id")
	  .references(() => accounts.id, { onDelete: "restrict" })
	  .notNull(),
	debitAmount: decimal("debit_amount", { precision: 12, scale: 2 })
	  .default("0.00")
	  .notNull(),
	creditAmount: decimal("credit_amount", { precision: 12, scale: 2 })
	  .default("0.00")
	  .notNull(),
	description: text("description"),
  }, (table) => ({
	rlsPolicy: pgPolicy('journal_entries_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM transactions t JOIN member om ON t.organization_id = om.organization_id WHERE t.id = ${table.transactionId} AND om.user_id = auth.uid())`
	})
  }));
  
  // -- BUDGETS --
  
  export const budgetStatusEnum = pgEnum("budget_status", [
	"draft",
	"active", 
	"closed",
	"archived",
  ]);
  
  export const budgetPeriods = pgTable("budget_periods", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	name: text("name").notNull(), // e.g., "2024 Annual Budget", "Q1 2024"
	description: text("description"),
	startDate: timestamp("start_date", { withTimezone: true }).notNull(),
	endDate: timestamp("end_date", { withTimezone: true }).notNull(),
	status: budgetStatusEnum("status").default("draft"),
	isDefault: boolean("is_default").default(false), // Only one default per organization
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('budget_periods_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  /**
   * Individual budget line items linking budget periods to accounts with budgeted amounts
   */
  export const budgetLineItems = pgTable("budget_line_items", {
	id: text("id").primaryKey(),
	budgetPeriodId: text("budget_period_id")
	  .references(() => budgetPeriods.id, { onDelete: "cascade" })
	  .notNull(),
	accountId: text("account_id")
	  .references(() => accounts.id, { onDelete: "restrict" })
	  .notNull(),
	budgetedAmount: decimal("budgeted_amount", { precision: 12, scale: 2 })
	  .notNull(),
	notes: text("notes"), // Optional notes for this budget line item
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	// Unique constraint to prevent duplicate accounts per budget period
	unique_budget_account: unique("unique_budget_account_idx").on(table.budgetPeriodId, table.accountId),
	rlsPolicy: pgPolicy('budget_line_items_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM budget_periods bp JOIN member om ON bp.organization_id = om.organization_id WHERE bp.id = ${table.budgetPeriodId} AND om.user_id = auth.uid())`
	})
  }));
  
  /**
   * Optional budget categories for grouping and organizing budget line items
   */
  export const budgetCategories = pgTable("budget_categories", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	name: text("name").notNull(), // e.g., "Operating Expenses", "Capital Expenditures"
	description: text("description"),
	color: text("color"), // Hex color for UI representation
	displayOrder: integer("display_order").default(0),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('budget_categories_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  // -- BILLS --
  
  export const billStatusEnum = pgEnum("bill_status", [
	"draft",
	"submitted",
	"approved",
	"paid",
	"void",
  ]);
  
  /**
   * Represents bills received from vendors.
   */
  export const bills = pgTable("bills", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	currency: text("currency").notNull(),
	vendorId: text("vendor_id")
	  .references(() => vendors.id, { onDelete: "restrict" })
	  .notNull(),
	transactionId: text("transaction_id").references((): any => transactions.id, {
	  onDelete: "set null",
	}),
	status: billStatusEnum("status").default("draft"),
	billNumber: varchar("bill_number", { length: 50 }).notNull(),
	date: timestamp("date", { withTimezone: true }).defaultNow().notNull(),
	dueDate: timestamp("due_date", { withTimezone: true }),
	subtotal: decimal("subtotal", { precision: 12, scale: 2 }).notNull(),
	tax: decimal("tax", { precision: 12, scale: 2 }).default("0.00"),
	total: decimal("total", { precision: 12, scale: 2 }).notNull(),
	amountPaid: decimal("amount_paid", { precision: 12, scale: 2 }).default("0.00").notNull(),
	amountRemaining: decimal("amount_remaining", { precision: 12, scale: 2 }).default("0.00").notNull(),
	overpaid: decimal("overpaid", { precision: 12, scale: 2 }).default("0.00").notNull(),
	notes: text("notes"),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('bills_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  /**
   * Line items on a bill.
   */
  export const billItems = pgTable("bill_items", {
	id: text("id").primaryKey(),
	billId: text("bill_id")
	  .references(() => bills.id, { onDelete: "cascade" })
	  .notNull(),
	productId: text("product_id").references(() => products.id, {
	  onDelete: "set null",
	}),
	description: text("description").notNull(),
	quantity: integer("quantity").notNull(),
	unitPrice: decimal("unit_price", { precision: 12, scale: 2 }).notNull(),
	total: decimal("total", { precision: 12, scale: 2 }).notNull(),
  }, (table) => ({
	rlsPolicy: pgPolicy('bill_items_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM bills b JOIN member om ON b.organization_id = om.organization_id WHERE b.id = ${table.billId} AND om.user_id = auth.uid())`
	})
  }));
  
  // -- CLIENTS --
  
  /**
	 * Customers of the organization.
	 */
  export const clients = pgTable("clients", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	name: text("name").notNull(),
	email: text("email"),
	phone: text("phone"),
	address: jsonb("address"), // Note for migration: Combine address, city, zip_code, and country fields from the old schema into this JSON object.
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('clients_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  // -- VENDORS --
	
	/**
	 * Represents vendors or suppliers for the organization.
	 */
	export const vendors = pgTable("vendors", {
	  id: text("id").primaryKey(),
	  organizationId: text("organization_id")
		.references(() => organization.id, { onDelete: "cascade" })
		.notNull(),
	  name: text("name").notNull(),
	  email: text("email"),
	  phone: text("phone"),
	  address: jsonb("address"), // Store address as a structured object
	  category: text("category"),
	  taxId: varchar("tax_id", { length: 50 }),
	  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
	}, (table) => ({
	  		rlsPolicy: pgPolicy('vendors_rls_policy', {
		for: 'all',
		using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	  })
	}));
  
  // -- TRANSACTIONS --
  
	export const transactionStatusEnum = pgEnum("transaction_status", [
	  "draft",
	  "posted",
	  "voided",
	]);
	export const transactionTypeEnum = pgEnum("transaction_type", [
	  "invoice",
	  "bill",
	  "simple_income",
	  "simple_expense",
	  "journal_entry",
	]);
	
	/**
	 * A master record for any financial event (e.g., an invoice, a bill).
	 * This contains the high-level details, and the double-entry accounting
	 * is handled by the associated journal entries.
	 */
	export const transactions = pgTable("transactions", {
	  id: text("id").primaryKey(),
	  organizationId: text("organization_id")
		.references(() => organization.id, { onDelete: "cascade" })
		.notNull(),
	  currency: text("currency").notNull(),
	  date: timestamp("date", { withTimezone: true }).notNull(),
	  description: text("description").notNull(),
	  totalAmount: decimal("total_amount", { precision: 12, scale: 2 }).notNull(),
	  status: transactionStatusEnum("status").default("draft"),
	  type: transactionTypeEnum("type").notNull(),
	  referenceNumber: varchar("reference_number", { length: 50 }),
	  invoiceId: text("invoice_id").references((): any => invoices.id, { onDelete: "set null" }),
	  clientId: text("client_id").references(() => clients.id, {
		onDelete: "set null",
	  }),
	  vendorId: text("vendor_id").references(() => vendors.id, {
		onDelete: "set null",
	  }),
	  projectId: text("project_id").references(() => projects.id, {
		onDelete: "set null",
	  }),
	  billId: text("bill_id").references((): any => bills.id, { onDelete: "set null" }),
	  postedAt: timestamp("posted_at", { withTimezone: true }),
	  voidedAt: timestamp("voided_at", { withTimezone: true }),
	  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
	}, (table) => ({
	  		rlsPolicy: pgPolicy('transactions_rls_policy', {
		for: 'all',
		using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	  })
	}));
  
  // -- INVENTORY --
  
  export const inventoryMovementTypeEnum = pgEnum("inventory_movement_type", [
	"purchase",
	"sale",
	"adjustment",
  ]);
  
  /**
   * Tracks every change in inventory for a product.
   * This table is the source of truth for inventory levels and valuation.
   */
  export const inventoryMovements = pgTable("inventory_movements", {
	id: text("id").primaryKey(),
	productId: text("product_id")
	  .references(() => products.id, { onDelete: "cascade" })
	  .notNull(),
	type: inventoryMovementTypeEnum("type").notNull(),
	quantity: integer("quantity").notNull(), // Can be negative for sales/decreases
	unitCost: decimal("unit_cost", { precision: 12, scale: 2 }).notNull(),
	date: timestamp("date", { withTimezone: true }).defaultNow().notNull(),
	notes: text("notes"),
	referenceId: text("reference_id"), // e.g., invoiceId, billId
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('inventory_movements_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM products p JOIN member om ON p.organization_id = om.organization_id WHERE p.id = ${table.productId} AND om.user_id = auth.uid())`
	})
  }));
  
  // -- INVOICES --
  export const invoiceStatusEnum = pgEnum("invoice_status", [
	"draft",
	"sent",
	"paid",
	"overdue",
	"overpaid",
	"void",
  ]);
  
  /**
   * Invoices issued to clients.
   * Each invoice is linked to a financial transaction.
   */
  export const invoices = pgTable("invoices", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	currency: text("currency").notNull(),
	clientId: text("client_id")
	  .references(() => clients.id, { onDelete: "restrict" })
	  .notNull(),
	transactionId: text("transaction_id").references((): any => transactions.id, {
	  onDelete: "set null",
	}),
	status: invoiceStatusEnum("status").default("draft"),
	invoiceNumber: varchar("invoice_number", { length: 50 }).notNull(),
	date: timestamp("date", { withTimezone: true }).defaultNow().notNull(),
	dueDate: timestamp("due_date", { withTimezone: true }),
	subtotal: decimal("subtotal", { precision: 12, scale: 2 }).notNull(),
	tax: decimal("tax", { precision: 12, scale: 2 }).default("0.00"),
	total: decimal("total", { precision: 12, scale: 2 }).notNull(),
	amountPaid: decimal("amount_paid", { precision: 12, scale: 2 }).default("0.00").notNull(),
	amountRemaining: decimal("amount_remaining", { precision: 12, scale: 2 }).default("0.00").notNull(),
	overpaid: decimal("overpaid", { precision: 12, scale: 2 }).default("0.00").notNull(),
	notes: text("notes"),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('invoices_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  /**
   * Line items on an invoice.
   */
  export const invoiceItems = pgTable("invoice_items", {
	id: text("id").primaryKey(),
	invoiceId: text("invoice_id")
	  .references(() => invoices.id, { onDelete: "cascade" })
	  .notNull(),
	productId: text("product_id").references(() => products.id, {
	  onDelete: "set null",
	}),
	description: text("description").notNull(),
	quantity: integer("quantity").notNull(),
	unitPrice: decimal("unit_price", { precision: 12, scale: 2 }).notNull(),
	total: decimal("total", { precision: 12, scale: 2 }).notNull(),
  }, (table) => ({
	rlsPolicy: pgPolicy('invoice_items_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM invoices i JOIN member om ON i.organization_id = om.organization_id WHERE i.id = ${table.invoiceId} AND om.user_id = auth.uid())`
	})
  }));
  
  // -- PRODUCTS --
  export const productTypeEnum = pgEnum("product_type", [
	"service",           // Labor-based services
	"digital_service",   // Software, digital products
	"physical_product",  // Inventory items
	"digital_product",   // Downloads, licenses
	"subscription",      // Recurring services
	"bundle",           // Package deals
  ]);
  
  export const productCategoryEnum = pgEnum("product_category", [
	"consulting",
	"software_services", 
	"physical_goods",
	"digital_products",
	"subscriptions",
	"training",
	"maintenance",
	"other"
  ]);
  
  /**
   * Product categories for organizing products by business type
   */
  export const productCategories = pgTable("product_categories", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	name: text("name").notNull(),
	description: text("description"),
	defaultType: productTypeEnum("default_type").notNull(),
	defaultRevenueAccountId: text("default_revenue_account_id"),
	defaultCogsAccountId: text("default_cogs_account_id"),
	isSystemCategory: boolean("is_system_category").default(false),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('product_categories_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  /**
   * Products or services offered by the organization.
   * Enhanced with mandatory account mappings and cost tracking.
   */
  export const products = pgTable("products", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	categoryId: text("category_id").references(() => productCategories.id),
	
	// Basic Information
	name: text("name").notNull(),
	description: text("description"),
	sku: text("sku"),
	type: productTypeEnum("type").default("service").notNull(),
	category: productCategoryEnum("category").default("other").notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	
	// Enhanced Pricing and Cost Structure
	price: decimal("price", { precision: 12, scale: 2 }).notNull(),
	costBasis: decimal("cost_basis", { precision: 12, scale: 2 }), // For inventory/materials
	laborCostPerHour: decimal("labor_cost_per_hour", { precision: 12, scale: 2 }), // For services
	overheadRate: decimal("overhead_rate", { precision: 12, scale: 2 }), // Overhead percentage
	
	// Mandatory Account Mappings (conditional based on type)
	revenueAccountId: text("revenue_account_id").notNull(), // Always required
	inventoryAccountId: text("inventory_account_id"), // Required for inventory only
	
	// Profitability Tracking
	totalSold: integer("total_sold").default(0).notNull(),
	totalRevenue: decimal("total_revenue", { precision: 12, scale: 2 }).default("0.00").notNull(),
	totalCosts: decimal("total_costs", { precision: 12, scale: 2 }).default("0.00").notNull(),
	lastSaleDate: timestamp("last_sale_date", { withTimezone: true }),
	
	// Business Intelligence
	businessType: text("business_type"), // consulting, retail, saas, etc.
	marginTarget: decimal("margin_target", { precision: 5, scale: 2 }), // Target profit margin %
	
	// Timestamps
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('products_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  // -- PROJECTS --
  export const projectStatusEnum = pgEnum("project_status", [
	"not_started",
	"in_progress",
	"completed",
	"on_hold",
	"canceled",
  ]);
  
  /**
   * Projects are used to group transactions and track profitability for specific jobs.
   */
  export const projects = pgTable("projects", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	clientId: text("client_id").references(() => clients.id, {
	  onDelete: "set null",
	}),
	name: text("name").notNull(),
	description: text("description"),
	status: projectStatusEnum("status").default("not_started").notNull(),
	startDate: timestamp("start_date", { withTimezone: true }),
	endDate: timestamp("end_date", { withTimezone: true }),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('projects_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  // -- RELATIONS --
  // -- CORE RELATIONS --
  
  export const userRelations = relations(user, ({ many }) => ({
	sessions: many(session),
	accounts: many(account),
	member: many(member),
	ownedOrganization: many(organization),
	onboardingProgress: many(onboarding),
	twoFactor: many(twoFactor),
  }));
  
  export const sessionRelations = relations(session, ({ one }) => ({
	user: one(user, {
	  fields: [session.userId],
	  references: [user.id],
	}),
  }));
  
  export const accountRelations = relations(account, ({ one }) => ({
	user: one(user, {
	  fields: [account.userId],
	  references: [user.id],
	}),
  }));

  export const verificationRelations = relations(verification, ({}) => ({
	// Verification table is standalone for auth purposes
  }));

  export const twoFactorRelations = relations(twoFactor, ({ one }) => ({
	user: one(user, {
	  fields: [twoFactor.userId],
	  references: [user.id],
	}),
  }));

  export const organizationRelations = relations(
	organization,
	({ one, many }) => ({
	  members: many(member),
	  accounts: many(accounts),
	  transactions: many(transactions),
	  clients: many(clients),
	  products: many(products),
	  invoices: many(invoices),
	  vendors: many(vendors),
	  bills: many(bills),
	  inventoryMovements: many(inventoryMovements),
	  projects: many(projects),
	  budgetPeriods: many(budgetPeriods),
	  budgetCategories: many(budgetCategories),
	  organizationSessions: many(organizationSessions),
	  addresses: many(organizationAddresses),
	})
  );

  export const organizationAddressesRelations = relations(organizationAddresses, ({ one }) => ({
	organization: one(organization, {
	  fields: [organizationAddresses.organizationId],
	  references: [organization.id],
	}),
  }));
  
  export const memberRelations = relations(
	member,
	({ one }) => ({
	  user: one(user, {
		fields: [member.userId],
		references: [user.id],
	  }),
	  organization: one(organization, {
		fields: [member.organizationId],
		references: [organization.id],
	  }),
	})
  );

  // -- ONBOARDING RELATIONS --
  
  export const onboardingRelations = relations(
	onboarding,
	({ one }) => ({
	  user: one(user, {
		fields: [onboarding.userId],
		references: [user.id],
	  }),
	})
  );
  
  export const businessTypeTemplatesRelations = relations(
	businessTypeTemplates,
	({ many }) => ({
	  // No direct relations but used by organizations via businessType
	})
  );
  
  // -- ACCOUNTING RELATIONS --
  
  export const accountsRelations = relations(accounts, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [accounts.organizationId],
	  references: [organization.id],
	}),
	parent: one(accounts, {
	  fields: [accounts.parentId],
	  references: [accounts.id],
	  relationName: "account_hierarchy",
	}),
	children: many(accounts, {
	  relationName: "account_hierarchy",
	}),
	journalEntries: many(journalEntries),
	budgetLineItems: many(budgetLineItems),
	productCogs: many(productCogsAccounts),
  }));
  
  export const journalEntriesRelations = relations(
	journalEntries,
	({ one }) => ({
	  transaction: one(transactions, {
		fields: [journalEntries.transactionId],
		references: [transactions.id],
	  }),
	  account: one(accounts, {
		fields: [journalEntries.accountId],
		references: [accounts.id],
	  }),
	})
  );
  
  // -- TRANSACTION RELATIONS --
  
  export const transactionsRelations = relations(
	transactions,
	({ one, many }) => ({
	  organization: one(organization, {
		fields: [transactions.organizationId],
		references: [organization.id],
	  }),
	  journalEntries: many(journalEntries),
	  invoice: one(invoices),
	  bill: one(bills),
	  client: one(clients, {
		fields: [transactions.clientId],
		references: [clients.id],
	  }),
	  vendor: one(vendors, {
		fields: [transactions.vendorId],
		references: [vendors.id],
	  }),
	})
  );
  
  // -- SALES RELATIONS --
  
  export const clientsRelations = relations(clients, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [clients.organizationId],
	  references: [organization.id],
	}),
	invoices: many(invoices),
	transactions: many(transactions),
	projects: many(projects),
  }));
  
  export const productCategoriesRelations = relations(productCategories, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [productCategories.organizationId],
	  references: [organization.id],
	}),
	products: many(products),
	defaultRevenueAccount: one(accounts, {
	  fields: [productCategories.defaultRevenueAccountId],
	  references: [accounts.id],
	}),
	defaultCogsAccount: one(accounts, {
	  fields: [productCategories.defaultCogsAccountId],
	  references: [accounts.id],
	}),
  }));
  
  export const productsRelations = relations(products, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [products.organizationId],
	  references: [organization.id],
	}),
	category: one(productCategories, {
	  fields: [products.categoryId],
	  references: [productCategories.id],
	}),
	revenueAccount: one(accounts, {
	  fields: [products.revenueAccountId],
	  references: [accounts.id],
	}),
	inventoryAccount: one(accounts, {
	  fields: [products.inventoryAccountId],
	  references: [accounts.id],
	}),
	invoiceItems: many(invoiceItems),
	billItems: many(billItems),
	inventoryMovements: many(inventoryMovements),
	cogsAccounts: many(productCogsAccounts),
  }));
  
  export const invoicesRelations = relations(invoices, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [invoices.organizationId],
	  references: [organization.id],
	}),
	client: one(clients, {
	  fields: [invoices.clientId],
	  references: [clients.id],
	}),
	transaction: one(transactions, {
	  fields: [invoices.transactionId],
	  references: [transactions.id],
	}),
	items: many(invoiceItems),
  }));
  
  export const invoiceItemsRelations = relations(
	invoiceItems,
	({ one }) => ({
	  invoice: one(invoices, {
		fields: [invoiceItems.invoiceId],
		references: [invoices.id],
	  }),
	  product: one(products, {
		fields: [invoiceItems.productId],
		references: [products.id],
	  }),
	})
  );
  
  // -- VENDOR RELATIONS --
  
  export const vendorsRelations = relations(vendors, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [vendors.organizationId],
	  references: [organization.id],
	}),
	bills: many(bills),
	transactions: many(transactions),
  }));
  
  export const billsRelations = relations(bills, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [bills.organizationId],
	  references: [organization.id],
	}),
	vendor: one(vendors, {
	  fields: [bills.vendorId],
	  references: [vendors.id],
	}),
	transaction: one(transactions, {
	  fields: [bills.transactionId],
	  references: [transactions.id],
	}),
	items: many(billItems),
  }));
  
  export const billItemsRelations = relations(billItems, ({ one }) => ({
	bill: one(bills, {
	  fields: [billItems.billId],
	  references: [bills.id],
	}),
	product: one(products, {
	  fields: [billItems.productId],
	  references: [products.id],
	}),
  }));
  
  // -- PROJECT RELATIONS --
  
  export const projectsRelations = relations(projects, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [projects.organizationId],
	  references: [organization.id],
	}),
	client: one(clients, {
	  fields: [projects.clientId],
	  references: [clients.id],
	}),
	transactions: many(transactions),
  }));
  
  // -- BUDGET RELATIONS --
  
  export const budgetPeriodsRelations = relations(budgetPeriods, ({ one, many }) => ({
	organization: one(organization, {
	  fields: [budgetPeriods.organizationId],
	  references: [organization.id],
	}),
	lineItems: many(budgetLineItems),
  }));
  
  export const budgetLineItemsRelations = relations(budgetLineItems, ({ one }) => ({
	budgetPeriod: one(budgetPeriods, {
	  fields: [budgetLineItems.budgetPeriodId],
	  references: [budgetPeriods.id],
	}),
	account: one(accounts, {
	  fields: [budgetLineItems.accountId],
	  references: [accounts.id],
	}),
  }));
  
  export const budgetCategoriesRelations = relations(budgetCategories, ({ one }) => ({
	organization: one(organization, {
	  fields: [budgetCategories.organizationId],
	  references: [organization.id],
	}),
  }));
  
  // -- INVENTORY RELATIONS --
  
  export const inventoryMovementsRelations = relations(
	inventoryMovements,
	({ one }) => ({
	  product: one(products, {
		fields: [inventoryMovements.productId],
		references: [products.id],
	  }),
	})
  );
  
  
  
  // ================================
  // PHASE 0.5: ENHANCED ORGANIZATION SYSTEM
  // ================================
  
  /**
   * Enhanced organization settings for complex configurations
   * Supports versioned settings with audit trail
   */
  export const organizationSettings = pgTable("organization_settings", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	category: varchar("category", { length: 50 }).notNull(), // 'security', 'billing', 'features', 'integrations'
	key: varchar("key", { length: 100 }).notNull(),
	value: jsonb("value").notNull(),
	description: text("description"), // Human-readable description
	isSystem: boolean("is_system").default(false), // System vs user-defined settings
	updatedBy: text("updated_by").references(() => user.id, { onDelete: "set null" }),
	validFrom: timestamp("valid_from", { withTimezone: true }).defaultNow(),
	validTo: timestamp("valid_to", { withTimezone: true }),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	unique_org_category_key: unique("unique_org_category_key_idx").on(
	  table.organizationId, 
	  table.category, 
	  table.key
	),
	rlsPolicy: pgPolicy('organization_settings_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  /**
   * Comprehensive audit logging for all organization activities
   * Supports compliance requirements and security monitoring
   */
  export const auditLogs = pgTable("audit_logs", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	userId: text("user_id").references(() => user.id, { onDelete: "set null" }),
	sessionId: text("session_id"), // Link to session for tracking
	// Event grouping
	correlationId: text("correlation_id"), // For grouping related events (bulk ops, workflows, etc.)
	// Partitioning support
	partitionKey: text("partition_key"), // For future partitioning (e.g., by date, org, etc.)
	
	// Action details
	action: varchar("action", { length: 100 }).notNull(), // 'create', 'update', 'delete', 'login', 'invite', etc.
	resourceType: varchar("resource_type", { length: 50 }).notNull(), // 'transaction', 'user', 'organization', etc.
	resourceId: varchar("resource_id", { length: 100 }), // ID of the affected resource
	
	// Change tracking
	oldValues: jsonb("old_values"), // Previous state
	newValues: jsonb("new_values"), // New state
	changedFields: jsonb("changed_fields"), // Array of field names that changed
	
	// Context
	description: text("description"), // Human-readable description
	metadata: jsonb("metadata"), // Additional context (e.g., bulk operation info)
	
	// Security context
	ipAddress: varchar("ip_address", { length: 45 }), // IPv4 or IPv6
	userAgent: text("user_agent"),
	location: jsonb("location"), // Geolocation data if available
	
	// Risk assessment
	riskLevel: varchar("risk_level", { length: 20 }).default("low"), // 'low', 'medium', 'high', 'critical'
	flags: jsonb("flags"), // Security flags or alerts
	
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	// Indexes for performance and common queries
	idx_audit_org_date: index("idx_audit_logs_org_date").on(table.organizationId, table.createdAt),
	idx_audit_user_date: index("idx_audit_logs_user_date").on(table.userId, table.createdAt),
	idx_audit_resource: index("idx_audit_logs_resource").on(table.resourceType, table.resourceId),
	idx_audit_action: index("idx_audit_logs_action").on(table.action, table.createdAt),
	idx_audit_correlation: index("idx_audit_logs_correlation").on(table.correlationId),
	rlsPolicy: pgPolicy('audit_logs_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  /**
   * Organization access sessions for detailed security monitoring
   * Tracks all organization access patterns
   */
  export const organizationSessions = pgTable("organization_sessions", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull(),
	userId: text("user_id")
	  .references(() => user.id, { onDelete: "cascade" })
	  .notNull(),
	sessionId: text("session_id").notNull(),
	
	// Session details
	startedAt: timestamp("started_at", { withTimezone: true }).defaultNow(),
	lastActivity: timestamp("last_activity", { withTimezone: true }).defaultNow(),
	endedAt: timestamp("ended_at", { withTimezone: true }),
	
	// Security context
	ipAddress: varchar("ip_address", { length: 45 }),
	userAgent: text("user_agent"),
	location: jsonb("location"),
	
	// Activity tracking
	pageViews: integer("page_views").default(0),
	actionsPerformed: integer("actions_performed").default(0),
	
	// Security flags
	isActive: boolean("is_active").default(true),
	isSuspicious: boolean("is_suspicious").default(false),
	riskScore: integer("risk_score").default(0), // 0-100 risk assessment
	
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('organization_sessions_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  export const organizationSessionsRelations = relations(
	organizationSessions,
	({ one }) => ({
	  organization: one(organization, {
		fields: [organizationSessions.organizationId],
		references: [organization.id],
	  }),
	  user: one(user, {
		fields: [organizationSessions.userId],
		references: [user.id],
	  }),
	})
  );
  
  // ================================
  // ENHANCED ORGANIZATION MANAGEMENT
  // ================================
  
  /**
   * Organization lifecycle and status management
   */
  export const organizationStatusEnum = pgEnum("organization_status", [
	"active",
	"suspended",
	"deactivated", 
	"deleted",
	"pending_verification",
  ]);
  
  export const suspensionReasonEnum = pgEnum("suspension_reason", [
	"payment_failed",
	"policy_violation",
	"security_breach",
	"user_requested",
	"compliance_issue",
	"other",
  ]);
  
  /**
   * Organization compliance and regulatory tracking
   */
  export const organizationCompliance = pgTable("organization_compliance", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
	  .references(() => organization.id, { onDelete: "cascade" })
	  .notNull()
	  .unique(),
	
	// Regulatory compliance
	gdprApplicable: boolean("gdpr_applicable").default(false),
	ccpaApplicable: boolean("ccpa_applicable").default(false),
	soxCompliant: boolean("sox_compliant").default(false),
	hipaaRequired: boolean("hipaa_required").default(false),
	
	// Data handling preferences
	dataRetentionDays: integer("data_retention_days").default(2555), // 7 years default
	allowDataExport: boolean("allow_data_export").default(true),
	requireDataDeletion: boolean("require_data_deletion").default(false),
	
	// Security requirements
	requireMfa: boolean("require_mfa").default(false),
	requireSso: boolean("require_sso").default(false),
	allowedIpRanges: jsonb("allowed_ip_ranges"), // Array of IP ranges
	sessionTimeoutMinutes: integer("session_timeout_minutes").default(480), // 8 hours
	
	// Audit requirements
	auditLogRetentionDays: integer("audit_log_retention_days").default(2555),
	requireAuditApproval: boolean("require_audit_approval").default(false),
	
	lastReviewedAt: timestamp("last_reviewed_at", { withTimezone: true }),
	reviewedBy: text("reviewed_by").references(() => user.id, { onDelete: "set null" }),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
	rlsPolicy: pgPolicy('organization_compliance_rls_policy', {
	  for: 'all',
	  using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
	})
  }));
  
  export const productCogsAccounts = pgTable("product_cogs_accounts", {
	id: text("id").primaryKey(),
	productId: text("product_id").references(() => products.id, { onDelete: "cascade" }).notNull(),
	accountId: text("account_id").references(() => accounts.id, { onDelete: "cascade" }).notNull(),
	allocationPercent: decimal("allocation_percent", { precision: 5, scale: 2 }), // e.g., 50.00 for 50%
	allocationAmount: decimal("allocation_amount", { precision: 12, scale: 2 }), // Optional, for fixed allocation
	notes: text("notes"),
}, (table) => ({
  unique_product_account: unique("unique_product_account_idx").on(table.productId, table.accountId),
  rlsPolicy: pgPolicy('product_cogs_accounts_rls_policy', {
	for: 'all',
	using: sql`EXISTS (SELECT 1 FROM products p JOIN member om ON p.organization_id = om.organization_id WHERE p.id = ${table.productId} AND om.user_id = auth.uid())`
  })
}));

export const productCogsAccountsRelations = relations(productCogsAccounts, ({ one }) => ({
  product: one(products, {
    fields: [productCogsAccounts.productId],
    references: [products.id],
  }),
  account: one(accounts, {
    fields: [productCogsAccounts.accountId],
    references: [accounts.id],
  }),
}));
  
  // --- Attachments table ---
export const attachments = pgTable("attachments", {
  id: text("id").primaryKey(),
  organizationId: text("organization_id").references(() => organization.id, { onDelete: "cascade" }).notNull(),
  entityType: text("entity_type").notNull(), // e.g., 'bill', 'invoice', 'client', 'transaction'
  entityId: text("entity_id").notNull(),
  fileUrl: text("file_url").notNull(),
  fileName: text("file_name"),
  fileType: text("file_type"),
  uploadedBy: text("uploaded_by").references(() => user.id, { onDelete: "set null" }),
  uploadedAt: timestamp("uploaded_at").defaultNow().notNull(),
  description: text("description"),
}, (table) => ({
  rlsPolicy: pgPolicy('attachments_rls_policy', {
	for: 'all',
	using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
  })
}));

// --- Exchange Rates table ---
export const exchangeRates = pgTable("exchange_rates", {
  id: text("id").primaryKey(),
  baseCurrency: text("base_currency").notNull(),
  targetCurrency: text("target_currency").notNull(),
  rate: decimal("rate", { precision: 12, scale: 6 }).notNull(),
  date: timestamp("date").notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('exchange_rates_rls_policy', {
	for: 'all',
	using: sql`true` // Exchange rates are public for all users
  })
}));

// --- Tax Rates table ---
export const taxRates = pgTable("tax_rates", {
  id: text("id").primaryKey(),
  organizationId: text("organization_id").references(() => organization.id, { onDelete: "cascade" }).notNull(),
  name: text("name").notNull(), // e.g., 'VAT', 'Sales Tax'
  rate: decimal("rate", { precision: 5, scale: 2 }).notNull(), // e.g., 7.50 for 7.5%
  country: text("country"),
  region: text("region"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('tax_rates_rls_policy', {
	for: 'all',
	using: sql`EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid())`
  })
}));

// --- Translations table for localization ---
export const translations = pgTable("translations", {
  id: text("id").primaryKey(),
  entityType: text("entity_type").notNull(), // e.g., 'ui', 'product', 'category', etc.
  entityId: text("entity_id"), // Optional: for record-specific translations
  locale: text("locale").notNull(), // e.g., 'en', 'de', 'bs'
  key: text("key").notNull(), // e.g., 'dashboard.title', 'product.name'
  value: text("value").notNull(),
  context: text("context"), // Optional: for disambiguation
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('translations_rls_policy', {
	for: 'all',
	using: sql`true` // Translations are public for all users
  })
}));

// --- Data Retention Requests table for compliance ---
export const dataRetentionRequests = pgTable("data_retention_requests", {
  id: text("id").primaryKey(),
  userId: text("user_id").references(() => user.id, { onDelete: "set null" }),
  organizationId: text("organization_id").references(() => organization.id, { onDelete: "set null" }),
  type: text("type").notNull(), // 'export', 'deletion'
  status: text("status").default('pending').notNull(), // 'pending', 'in_progress', 'completed', 'failed', 'cancelled'
  requestedAt: timestamp("requested_at").defaultNow().notNull(),
  processedAt: timestamp("processed_at"),
  processedBy: text("processed_by").references(() => user.id, { onDelete: "set null" }),
  resultUrl: text("result_url"), // For export files, etc.
  error: text("error"),
  notes: text("notes"),
}, (table) => ({
  rlsPolicy: pgPolicy('data_retention_requests_rls_policy', {
	for: 'all',
	using: sql`(auth.uid() = ${table.userId}) OR (${table.organizationId} IS NOT NULL AND EXISTS (SELECT 1 FROM "member" WHERE "member"."organization_id" = ${table.organizationId} AND "member"."user_id" = auth.uid()))`
  })
}));

export const schema = {
	user,
	session,
	account,
	verification,
	twoFactor,
	organization,
	organizationAddresses,
	member,
	invitation,
	onboarding,
	businessTypeTemplates,
	accounts,
	journalEntries,
	transactions,
	clients,
	vendors,
	bills,
	billItems,
	invoices,
	invoiceItems,
	productCategories,
	products,
	projects,
	budgetPeriods,
	budgetLineItems,
	budgetCategories,
	productCogsAccounts,
	auditLogs,
	organizationSessions,
	organizationCompliance,
	attachments,
	exchangeRates,
	taxRates,
	translations,
	dataRetentionRequests,
  };