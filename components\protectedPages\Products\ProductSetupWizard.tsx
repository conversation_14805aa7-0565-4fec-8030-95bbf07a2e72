"use client";

import { useState, useEffect } from 'react';
import { useForm, useFormContext, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Lightbulb, Plus, Trash2, Check, ChevronsUpDown } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { fetchProductAccountSuggestions } from '@/lib/actions/products/fetch-product-account-suggestions';
import type { AccountSuggestion } from '@/lib/actions/products/get-account-suggestions';

const productSchema = z.object({
  name: z.string().min(3, 'Product name must be at least 3 characters'),
  description: z.string().optional(),
  productType: z.enum([
    "service", 
    "digital_service", 
    "physical_product", 
    "digital_product", 
    "subscription", 
    "bundle"
  ]),
  revenueAccountId: z.string().min(1, "Revenue account is required"),
  cogsAccountId: z.string().optional(),
  inventoryAccountId: z.string().optional(),
  useMultipleCostAccounts: z.boolean().optional().default(false),
  costAccounts: z.array(z.object({
    accountId: z.string().min(1, "Account is required"),
    percentage: z.number().min(0).max(100, "Percentage must be between 0 and 100"),
  })).optional(),
  price: z.number().positive("Price must be a positive number").optional(),
  costBasis: z.number().nonnegative("Cost must be a positive number").optional(),
  accountsToCreate: z.array(z.object({
    accountName: z.string(),
    accountCode: z.string(),
    accountType: z.enum(['revenue', 'expense', 'asset']),
  })).optional(),
});

type ProductFormData = z.infer<typeof productSchema>;

const steps = [
  {
    id: 'step1',
    title: 'Product Information',
    description: 'Provide basic details about your product or service.',
    fields: ['name', 'description', 'productType'],
  },
  {
    id: 'step2',
    title: 'Account Mapping',
    description: 'Map this product to your chart of accounts.',
    fields: ['revenueAccountId', 'cogsAccountId', 'inventoryAccountId'],
  },
  {
    id: 'step3',
    title: 'Pricing & Cost',
    description: 'Set the pricing and cost details for profitability tracking.',
    fields: ['price', 'costBasis'],
  },
  {
    id: 'step4',
    title: 'Review & Finish',
    description: 'Review the details before creating the product.',
    fields: [],
  },
];

type Account = {
  id: string;
  name: string;
  accountNumber: string | null;
  type: string;
}

interface ProductSetupWizardProps {
  organizationId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ProductFormData) => void;
  isPending: boolean;
  editingProduct: ProductFormData | null;
}

export function ProductSetupWizard({
  organizationId,
  open,
  onOpenChange,
  onSubmit,
  isPending,
  editingProduct,
}: ProductSetupWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<any>(null);
  const [existingAccounts, setExistingAccounts] = useState<Account[]>([]);
  const [missingAccounts, setMissingAccounts] = useState<AccountSuggestion[]>([]);
  
  const defaultProductValues: ProductFormData = {
    name: "",
    description: "",
    productType: "service",
    revenueAccountId: "",
    cogsAccountId: "",
    inventoryAccountId: "",
    useMultipleCostAccounts: false,
    costAccounts: [],
    price: 0,
    costBasis: 0,
    accountsToCreate: [],
  };

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema) as any,
    defaultValues: {
      ...defaultProductValues,
      ...(editingProduct || {})
    },
  });

  useEffect(() => {
    if (editingProduct) {
      form.reset({ ...defaultProductValues, ...editingProduct });
    } else {
      form.reset(defaultProductValues);
    }
  }, [editingProduct, form]);

  const processForm = (data: any) => {
    onSubmit(data as ProductFormData);
  };
  
  const next = async () => {
    const fields = steps[currentStep].fields;
    const output = await form.trigger(fields as (keyof ProductFormData)[], { shouldFocus: true });

    if (!output) return;

    if (currentStep < steps.length - 1) {
      if (currentStep === 0) {
        setIsLoading(true);
        try {
          const productType = form.getValues('productType');
          const { suggestions: fetchedSuggestions, existingAccounts: fetchedAccounts, missingAccounts: fetchedMissing } = await fetchProductAccountSuggestions(organizationId, productType);

          setSuggestions(fetchedSuggestions);
          setExistingAccounts(fetchedAccounts as Account[]);
          setMissingAccounts(fetchedMissing);
          
          if (fetchedSuggestions.revenueAccount?.[0]) {
            form.setValue('revenueAccountId', fetchedSuggestions.revenueAccount[0].accountId);
          }
          if (fetchedSuggestions.cogsAccount?.[0]) {
            form.setValue('cogsAccountId', fetchedSuggestions.cogsAccount[0].accountId);
          }
          if (fetchedSuggestions.inventoryAccount?.[0]) {
            form.setValue('inventoryAccountId', fetchedSuggestions.inventoryAccount[0].accountId);
          }
        } catch (error) {
          console.error("Failed to fetch suggestions:", error);
        } finally {
          setIsLoading(false);
        }
      }
      setCurrentStep(step => step + 1);
    } else {
      await form.handleSubmit(processForm)();
    }
  };

  const prev = () => {
    if (currentStep > 0) {
      setCurrentStep(step => step - 1);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(processForm)}>
            <DialogHeader>
              <DialogTitle>{editingProduct ? "Edit Product" : "Create a New Product"} ({steps[currentStep].title})</DialogTitle>
              <DialogDescription>{steps[currentStep].description}</DialogDescription>
            </DialogHeader>
            <div className="py-4">
              {currentStep === 0 && <Step1 />}
              {currentStep === 1 && (
                <Step2 
                  isLoading={isLoading} 
                  suggestions={suggestions} 
                  existingAccounts={existingAccounts} 
                  missingAccounts={missingAccounts}
                />
              )}
              {currentStep === 2 && <Step3 />}
              {currentStep === 3 && <Step4 existingAccounts={existingAccounts} />}
            </div>
            <DialogFooter>
              <Button onClick={prev} type="button" variant="outline" disabled={currentStep === 0}>
                Back
              </Button>
              <Button onClick={next} type="button" disabled={isPending}>
                {isPending ? "Saving..." : (currentStep === steps.length - 1 ? 'Finish' : 'Next')}
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}

const Step1 = () => {
  const { control } = useFormContext<ProductFormData>();

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Product Name</FormLabel>
            <FormControl>
              <Input placeholder="e.g., Website Design Service" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="productType"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Product Type</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a product type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="service">Service</SelectItem>
                <SelectItem value="digital_service">Digital Service</SelectItem>
                <SelectItem value="physical_product">Physical Product</SelectItem>
                <SelectItem value="digital_product">Digital Product</SelectItem>
                <SelectItem value="subscription">Subscription</SelectItem>
                <SelectItem value="bundle">Bundle</SelectItem>
              </SelectContent>
            </Select>
            <FormDescription>
              This helps categorize the product for accounting purposes.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Description (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe the product or service..."
                className="resize-none"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

const Step2 = ({ isLoading, suggestions, existingAccounts, missingAccounts }: { 
  isLoading: boolean, 
  suggestions: any, 
  existingAccounts: Account[],
  missingAccounts: AccountSuggestion[]
}) => {
  const { control, setValue, watch } = useFormContext<ProductFormData>();
  
  // Auto-calculate percentages when cost accounts change
  useEffect(() => {
    const costAccounts = watch('costAccounts');
    if (costAccounts && costAccounts.length > 0) {
      const totalAccounts = costAccounts.length;
      const equalPercentage = Math.round(100 / totalAccounts);
      
      const updatedAccounts = costAccounts.map((account, index) => ({
        ...account,
        percentage: equalPercentage
      }));
      
      setValue('costAccounts', updatedAccounts);
    }
  }, [watch('costAccounts')?.length, setValue]);

  // Ensure useMultipleCostAccounts is properly initialized
  useEffect(() => {
    const currentValue = watch('useMultipleCostAccounts');
    console.log('Current useMultipleCostAccounts value:', currentValue);
    if (currentValue === undefined) {
      setValue('useMultipleCostAccounts', false);
    }
  }, [watch('useMultipleCostAccounts'), setValue]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="ml-4 text-muted-foreground">Fetching account suggestions...</p>
      </div>
    );
  }

  const renderSearchableAccountSelect = (
    fieldName: "revenueAccountId" | "cogsAccountId" | "inventoryAccountId",
    label: string,
    accountType: 'revenue' | 'expense' | 'asset' | 'equity' | 'liability',
    suggested: AccountSuggestion[] = [],
  ) => {
    const allAccounts = existingAccounts.filter(acc => acc.type === accountType);
    const hasSuggestions = suggested && suggested.length > 0;
    
    // Create unique sets to avoid duplicates
    const suggestedIds = new Set(suggested.map(s => s.accountId));
    const existingIds = new Set(allAccounts.map(acc => acc.id));
    
    // Filter out suggested accounts that already exist in the accounts list
    const uniqueSuggested = suggested.filter(s => !existingIds.has(s.accountId));

    const getAccountDisplay = (accountId: string) => {
      const account = allAccounts.find(acc => acc.id === accountId);
      if (account) {
        return `${account.name} (${account.accountNumber || 'No number'})`;
      }
      const suggestedAccount = uniqueSuggested.find(s => s.accountId === accountId);
      if (suggestedAccount) {
        return `${suggestedAccount.accountName} (${suggestedAccount.accountCode || 'No code'})`;
      }
      return accountId;
    };

    return (
      <FormField
        control={control}
        name={fieldName}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              {label} {hasSuggestions && <Lightbulb className="h-4 w-4 text-yellow-500" />}
            </FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "w-full justify-between",
                      !field.value && "text-muted-foreground"
                    )}
                  >
                    {field.value ? getAccountDisplay(field.value) : `Select a ${label.toLowerCase()}`}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput placeholder={`Search ${accountType} accounts...`} />
                  <CommandList>
                    <CommandEmpty>No account found.</CommandEmpty>
                    {uniqueSuggested.length > 0 && (
                      <CommandGroup heading="Suggested">
                        {uniqueSuggested.map((s) => (
                          <CommandItem
                            key={`suggested-${s.accountId}`}
                            value={`${s.accountName} ${s.accountCode || ''}`}
                            onSelect={() => field.onChange(s.accountId)}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                field.value === s.accountId ? "opacity-100" : "opacity-0"
                              )}
                            />
                            <div className="flex justify-between w-full">
                              <span>{s.accountName}</span>
                              <Badge variant="secondary">{s.accountCode || 'No code'}</Badge>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    )}
                    <CommandGroup heading={`All ${accountType} accounts`}>
                      {allAccounts.map((acc) => (
                        <CommandItem
                          key={`existing-${acc.id}`}
                          value={`${acc.name} ${acc.accountNumber || ''}`}
                          onSelect={() => field.onChange(acc.id)}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              field.value === acc.id ? "opacity-100" : "opacity-0"
                            )}
                          />
                          <div className="flex justify-between w-full">
                            <span>{acc.name}</span>
                            <Badge variant="outline">{acc.accountNumber || 'No number'}</Badge>
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  }

  return (
    <div className="space-y-6">
      {renderSearchableAccountSelect("revenueAccountId", "Revenue Account", 'revenue', suggestions?.revenueAccount)}

      {(
        <div className="space-y-4">
          <FormField
            control={control}
            name="useMultipleCostAccounts"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value || false}
                    onCheckedChange={(checked) => {
                      console.log('Checkbox changed:', checked);
                      field.onChange(checked);
                      if (checked) {
                        // Get the best recommended cost account
                        const bestCogsAccount = suggestions?.cogsAccount?.[0];
                        const recommendedAccountId = bestCogsAccount?.accountId || '';
                        
                        // Initialize with one cost account when enabled, using the best recommendation
                        setValue('costAccounts', [{ 
                          accountId: recommendedAccountId, 
                          percentage: 100 
                        }]);
                      } else {
                        // Clear cost accounts when disabled
                        setValue('costAccounts', []);
                      }
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Multiple Cost Accounts</FormLabel>
                  <FormDescription>
                    Enable to allocate costs across multiple accounts with percentages
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />


          {watch('useMultipleCostAccounts') ? (
            <div className="space-y-4">
              <FormField
                control={control}
                name="costAccounts"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost Accounts</FormLabel>
                    <div className="space-y-3">
                      {field.value?.map((account, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                          <div className="flex-1">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  className={cn(
                                    "w-full justify-between",
                                    !account.accountId && "text-muted-foreground"
                                  )}
                                >
                                  {account.accountId ? 
                                    (() => {
                                      const acc = existingAccounts.find(a => a.id === account.accountId);
                                      return acc ? `${acc.name} (${acc.accountNumber || 'No number'})` : account.accountId;
                                    })() 
                                    : "Select account"
                                  }
                                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput placeholder="Search expense accounts..." />
                                  <CommandList>
                                    <CommandEmpty>No account found.</CommandEmpty>
                                    <CommandGroup heading="Expense accounts">
                                      {existingAccounts.filter(acc => acc.type === 'expense').map((acc) => (
                                        <CommandItem
                                          key={`cost-${acc.id}`}
                                          value={`${acc.name} ${acc.accountNumber || ''}`}
                                          onSelect={() => {
                                            const newAccounts = [...(field.value || [])];
                                            newAccounts[index] = { ...account, accountId: acc.id };
                                            field.onChange(newAccounts);
                                          }}
                                        >
                                          <Check
                                            className={cn(
                                              "mr-2 h-4 w-4",
                                              account.accountId === acc.id ? "opacity-100" : "opacity-0"
                                            )}
                                          />
                                          <div className="flex justify-between w-full">
                                            <span>{acc.name}</span>
                                            <Badge variant="outline">{acc.accountNumber || 'No number'}</Badge>
                                          </div>
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </div>
                          <div className="w-24">
                            <div className="flex items-center gap-1">
                              <Input
                                type="number"
                                placeholder="0"
                                value={account.percentage}
                                onChange={(e) => {
                                  const newAccounts = [...(field.value || [])];
                                  newAccounts[index] = { ...account, percentage: Number(e.target.value) };
                                  field.onChange(newAccounts);
                                }}
                              />
                              <span className="text-sm font-medium text-muted-foreground">%</span>
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newAccounts = field.value?.filter((_, i) => i !== index) || [];
                              field.onChange(newAccounts);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          const currentAccounts = field.value || [];
                          
                          // Find the next best recommended account that's not already selected
                          const usedAccountIds = new Set(currentAccounts.map(acc => acc.accountId));
                          const nextBestAccount = suggestions?.cogsAccount?.find((s: any) => !usedAccountIds.has(s.accountId)) ||
                                                existingAccounts.filter(acc => acc.type === 'expense')
                                                  .find(acc => !usedAccountIds.has(acc.id));
                          
                          const newAccounts = [...currentAccounts, { 
                            accountId: nextBestAccount?.accountId || nextBestAccount?.id || '', 
                            percentage: 0 
                          }];
                          field.onChange(newAccounts);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Cost Account
                      </Button>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          ) : (
            renderSearchableAccountSelect("cogsAccountId", "Cost of Goods Sold Account", 'expense', suggestions?.cogsAccount)
          )}
        </div>
      )}

      {watch('productType') === 'physical_product' &&
        renderSearchableAccountSelect("inventoryAccountId", "Inventory Account", 'asset', suggestions?.inventoryAccount)
      }
      
      {missingAccounts.length > 0 && (
        <div className="space-y-4 rounded-md border p-4">
           <h3 className="text-sm font-medium flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-yellow-500" />
            Missing Accounts Detected
          </h3>
          <p className="text-sm text-muted-foreground">
            We suggest creating these standard accounts for your business type to improve tracking. Select the ones you want to add.
          </p>
          <FormField
            control={control}
            name="accountsToCreate"
            render={() => (
              <FormItem className="space-y-3">
                {missingAccounts.map((acc) => (
                  <FormField
                    key={`missing-${acc.accountId}`}
                    control={control}
                    name="accountsToCreate"
                    render={({ field }) => {
                      return (
                        <FormItem
                          key={`missing-item-${acc.accountId}`}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.some(v => v.accountCode === acc.accountCode)}
                              onCheckedChange={(checked) => {
                                const { accountType } = acc;
                                if (!accountType) return; // Should not happen for system suggestions

                                const normalBalance = (accountType === 'asset' || accountType === 'expense') ? 'debit' : 'credit';
                                
                                const accountData = {
                                  accountId: acc.accountId,
                                  accountName: acc.accountName,
                                  accountCode: acc.accountCode,
                                  confidence: acc.confidence,
                                  reason: acc.reason,
                                  isSystemGenerated: acc.isSystemGenerated,
                                  accountType: accountType,
                                  normalBalance: normalBalance,
                                };

                                return checked
                                  ? field.onChange([...(field.value || []), accountData])
                                  : field.onChange(
                                      field.value?.filter(
                                        (value) => value.accountCode !== acc.accountCode
                                      )
                                    );
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Create <Badge variant="secondary">{acc.accountName}</Badge> ({acc.accountCode})
                          </FormLabel>
                        </FormItem>
                      );
                    }}
                  />
                ))}
              </FormItem>
            )}
          />
        </div>
      )}
    </div>
  );
}

const Step3 = () => {
  const { control, watch, setValue } = useFormContext<ProductFormData>();
  const useMultipleCostAccounts = watch('useMultipleCostAccounts');
  const costAccounts = watch('costAccounts');

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name="price"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Price</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                placeholder="0.00" 
                {...field}
                onChange={event => field.onChange(+event.target.value)}
              />
            </FormControl>
            <FormDescription>
              The price you will charge your customers for this product.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
      
      {useMultipleCostAccounts ? (
        <div className="space-y-4">
          <FormField
            control={control}
            name="costBasis"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Total Cost</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="0.00" 
                    {...field}
                    onChange={event => {
                      const totalCost = +event.target.value;
                      field.onChange(totalCost);
                      
                                             // Distribute cost across multiple accounts based on percentages
                       if (costAccounts && costAccounts.length > 0) {
                         const updatedCostAccounts = costAccounts.map(account => ({
                           ...account,
                           distributedCost: (totalCost * (account.percentage || 0)) / 100
                         }));
                         setValue('costAccounts', updatedCostAccounts);
                       }
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Total cost will be distributed across your cost accounts based on the percentages set.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {costAccounts && costAccounts.length > 0 && (watch('costBasis') || 0) > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Cost Distribution</Label>
              <div className="space-y-2">
                                 {costAccounts?.map((account, index) => (
                   <div key={index} className="flex justify-between items-center p-2 bg-muted rounded">
                     <span className="text-sm">{account?.accountId ? `Account ${index + 1}` : 'Select account'}</span>
                     <span className="text-sm font-medium">
                       ${(((watch('costBasis') || 0) * (account?.percentage || 0)) / 100).toFixed(2)} ({account?.percentage || 0}%)
                     </span>
                   </div>
                 )) || []}
              </div>
            </div>
          )}
        </div>
      ) : (
        <FormField
          control={control}
          name="costBasis"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cost of Goods (Optional)</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="0.00" 
                  {...field}
                  onChange={event => field.onChange(+event.target.value)}
                />
              </FormControl>
              <FormDescription>
                The direct cost to you for providing this product or service.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
};

const Step4 = ({ existingAccounts }: { existingAccounts?: Account[] }) => {
  const { getValues } = useFormContext<ProductFormData>();
  const values = getValues();

  const getAccountDisplay = (accountId: string | undefined) => {
    if (!accountId) return 'Not selected';
    if (!existingAccounts) return accountId;
    const account = existingAccounts.find(acc => acc.id === accountId);
    if (!account) return 'Account not found';
    return `${account.name} (${account.accountNumber || 'No number'})`;
  };

  return (
    <div className="space-y-6">
       <div className="text-center">
        <h3 className="text-lg font-semibold">Review Your Product</h3>
        <p className="text-muted-foreground">
          Review the details below and click Finish to create the product.
        </p>
      </div>
      <Card>
        <CardContent className="p-4 space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Product Details</h4>
            <div className="flex justify-between"><span>Name:</span> <span className="font-medium">{values.name}</span></div>
            <div className="flex justify-between"><span>Type:</span> <Badge variant="outline">{values.productType.replace(/_/g, ' ')}</Badge></div>
            <p className="text-sm text-muted-foreground">{values.description}</p>
          </div>
          <Separator />
          <div className="space-y-2">
            <h4 className="font-medium">Financials</h4>
            <div className="flex justify-between"><span>Price:</span> <span className="font-medium">${values.price?.toFixed(2) || '0.00'}</span></div>
            <div className="flex justify-between"><span>Cost:</span> <span className="font-medium">${values.costBasis?.toFixed(2) || '0.00'}</span></div>
          </div>
          <Separator />
           <div className="space-y-2">
            <h4 className="font-medium">Account Mapping</h4>
            <div className="flex justify-between text-sm"><span>Revenue Account:</span> <span className="font-medium">{getAccountDisplay(values.revenueAccountId)}</span></div>
            {values.cogsAccountId && <div className="flex justify-between text-sm"><span>COGS Account:</span> <span className="font-medium">{getAccountDisplay(values.cogsAccountId)}</span></div>}
            {values.inventoryAccountId && <div className="flex justify-between text-sm"><span>Inventory Account:</span> <span className="font-medium">{getAccountDisplay(values.inventoryAccountId)}</span></div>}
            {values.useMultipleCostAccounts && values.costAccounts && values.costAccounts.length > 0 && (
              <>
                <div className="flex justify-between text-sm"><span>Multiple Cost Accounts:</span></div>
                {values.costAccounts.map((account, index) => (
                  <div key={index} className="flex justify-between text-sm ml-4">
                    <span>• {getAccountDisplay(account.accountId)}</span>
                    <span className="text-muted-foreground">{account.percentage}%</span>
                  </div>
                ))}
              </>
            )}
          </div>
          {values.accountsToCreate && values.accountsToCreate.length > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <h4 className="font-medium">New Accounts to Create</h4>
                {values.accountsToCreate.map(acc => (
                  <div key={acc.accountCode} className="flex justify-between text-sm">
                    <span>{acc.accountName}</span>
                    <Badge variant="secondary">{acc.accountCode}</Badge>
                  </div>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}; 