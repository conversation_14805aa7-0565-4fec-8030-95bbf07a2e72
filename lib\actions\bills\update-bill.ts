"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { bills, billItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { billFormSchema, type NewBill } from "@/lib/actions/bills/bill.schema";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function updateBill(billId: string, billData: NewBill): ActionResponse {
    try {
        const { organization } = await getServerUserContext();
        const orgId = organization.id;
        const validatedFields = billFormSchema.safeParse(billData);

        if (!validatedFields.success) {
            return { success: false, message: "Invalid data provided." };
        }

        type LineItem = { quantity: number; unitPrice: number; [key: string]: any };
        const { lineItems, ...updatedBillData } = validatedFields.data as { lineItems: LineItem[] } & typeof validatedFields.data;
        const total = lineItems.reduce((acc: number, item: LineItem) => acc + item.quantity * item.unitPrice, 0);

        await db.transaction(async (tx) => {
            await tx
                .update(bills)
                .set({
                    ...updatedBillData,
                    subtotal: String(total),
                    total: String(total),
                })
                .where(and(eq(bills.id, billId), eq(bills.organizationId, orgId)));
            
            await tx.delete(billItems).where(eq(billItems.billId, billId));

            const itemsToInsert = lineItems.map((item: LineItem) => ({
                ...item,
                billId: billId,
                unitPrice: String(item.unitPrice),
                total: String(item.quantity * item.unitPrice),
            }));

            await tx.insert(billItems).values(itemsToInsert);
        });

        revalidatePath("/bills");
        return { success: true, message: "Bill updated successfully." };
    } catch (error) {
        return { success: false, message: error instanceof Error ? error.message : "Failed to update bill." };
    }
} 