import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Text,
  Tailwind,
  Font,
} from '@react-email/components';

interface ForgotPasswordEmailProps {
    userName: string;
    userEmail: string;
    resetUrl: string;

}

const ForgotPasswordEmail = (props: ForgotPasswordEmailProps) => {
  const { userName, userEmail, resetUrl } = props;

  return (
    <Html lang="en" dir="ltr">
      <Head>
        <Font
          fontFamily="Poppins"
          fallbackFontFamily="Arial"
          webFont={{
            url: 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap',
            format: 'woff2',
          }}
        />
      </Head>
      <Tailwind>
        <Preview>Reset your password - Action required</Preview>
        <Body className="bg-[#222121] font-sans py-[40px]">
          <Container className="bg-white rounded-[12px] max-w-[600px] mx-auto px-[48px] py-[40px]">
            {/* Header */}
            <Section className="text-center mb-[32px]">
              <Heading className="text-[28px] font-bold text-[#222121] m-0 mb-[8px]">
                Password Reset Request
              </Heading>
              <Text className="text-[16px] text-[#666666] m-0">
                We received a request to reset your password
              </Text>
            </Section>

            {/* Main Content */}
            <Section className="mb-[32px]">
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[16px]">
                Hello {userName},
              </Text>
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[16px]">
                We received a request to reset the password for your account associated with{' '}
                <strong>{userEmail}</strong>.
              </Text>
              <Text className="text-[16px] text-[#222121] leading-[24px] mb-[24px]">
                If you made this request, click the button below to reset your password. This link will expire in 24 hours for security reasons.
              </Text>
            </Section>

            {/* Reset Button */}
            <Section className="text-center mb-[32px]">
              <Button
                href={resetUrl}
                className="bg-[#bc00fe] text-white px-[32px] py-[16px] rounded-[8px] text-[16px] font-semibold no-underline box-border inline-block"
              >
                Reset My Password
              </Button>
            </Section>

            {/* Alternative Link */}
            <Section className="mb-[32px]">
              <Text className="text-[14px] text-[#666666] leading-[20px] mb-[8px]">
                If the button doesn't work, copy and paste this link into your browser:
              </Text>
              <Link
                href={resetUrl}
                className="text-[#bc00fe] text-[14px] break-all underline"
              >
                {resetUrl}
              </Link>
            </Section>

            {/* Security Notice */}
            <Section className="bg-[#f1f1f1] rounded-[8px] p-[20px] mb-[32px]">
              <Text className="text-[14px] text-[#222121] leading-[20px] m-0 mb-[8px] font-semibold">
                🔒 Security Notice
              </Text>
              <Text className="text-[14px] text-[#666666] leading-[20px] m-0">
                If you didn't request a password reset, please ignore this email. Your password will remain unchanged. For security, this reset link will expire in 24 hours.
              </Text>
            </Section>

            {/* Footer */}
            <Section className="border-t border-solid border-[#e5e5e5] pt-[24px]">
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0 mb-[8px]">
                This email was sent to {userEmail}
              </Text>
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0 mb-[8px]">
                NextGenBusiness
              </Text>
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0">
                <Link href="#" className="text-[#bc00fe] underline">Unsubscribe</Link> | 
                <Link href="#" className="text-[#bc00fe] underline ml-[4px]">Privacy Policy</Link> | 
                <Link href="#" className="text-[#bc00fe] underline ml-[4px]">Contact Support</Link>
              </Text>
              <Text className="text-[12px] text-[#999999] leading-[16px] text-center m-0 mt-[16px]">
                © 2024 Alvecomm. All rights reserved.
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};


export default ForgotPasswordEmail;