"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";

export async function getProducts() {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const productList = await db
      .select({
        id: products.id,
        organizationId: products.organizationId,
        categoryId: products.categoryId,
        name: products.name,
        description: products.description,
        sku: products.sku,
        type: products.type,
        category: products.category,
        isActive: products.isActive,
        price: products.price,
        costBasis: products.costBasis,
        laborCostPerHour: products.laborCostPerHour,
        overheadRate: products.overheadRate,
        revenueAccountId: products.revenueAccountId,
        inventoryAccountId: products.inventoryAccountId,
        totalSold: products.totalSold,
        totalRevenue: products.totalRevenue,
        totalCosts: products.totalCosts,
        lastSaleDate: products.lastSaleDate,
        businessType: products.businessType,
        marginTarget: products.marginTarget,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      })
      .from(products)
      .where(eq(products.organizationId, orgId))
      .orderBy(desc(products.createdAt));
    return productList;
  } catch (error) {
    console.error("Failed to fetch products:", error);
    return [];
  }
} 