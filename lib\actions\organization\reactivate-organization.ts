"use server";

import { db } from "@/db/drizzle";
import { organization } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";

export async function reactivateOrganization() {
  const { organization } = await getServerUserContext();
  if (!organization.id) {
    return { success: false, message: "No organization found." };
  }
  await db.update(organization)
    .set({
      isActive: true,
      deactivatedAt: null,
      deactivationReason: null,
    })
    .where(eq(organization.id, organization.id));
  return { success: true, message: "Organization reactivated." };
} 