import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { AvatarDropdown } from "@/components/general/AvatarDropdown";
import { Bell, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Breadcrumbs } from "./Breadcrumbs";
import React from "react";
import Link from "next/link";
import { OrganizationSwitcher } from "@/components/general/OrganizationSwitcher";

export async function ProtectedHeader({ children }: { children?: React.ReactNode }) {
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;
  const organizations = user?.organizations || [];
  const currentOrg = organizations.find((org: any) => org.organizationId === user?.currentOrganizationId);

  return (
    <header className="flex items-center justify-between h-16 px-6 bg-card border-b">
      <div className="flex items-center gap-6">
        <Breadcrumbs />
        {children}
      </div>

      {/* Right side: Switch Org, Notifications, Avatar */}
      <div className="flex items-center space-x-4">
        {/* Switch Organization button */}
        <OrganizationSwitcher
        organizations={organizations}
        currentOrganizationId={user?.currentOrganizationId}
        />
        {/* Notifications (Placeholder) */}
        <Button variant="ghost" size="icon">
          <Bell className="w-5 h-5" />
          <span className="sr-only">Notifications</span>
        </Button>
        {/* Avatar Dropdown */}
        {user ? (
          <AvatarDropdown
            name={user.name || "User"}
            email={user.email || ""}
            image={user.image}
          />
        ) : (
          <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
        )}
      </div>
    </header>
  );
} 