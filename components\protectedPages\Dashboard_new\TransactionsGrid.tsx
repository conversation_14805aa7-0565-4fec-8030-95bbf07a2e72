import TransactionCard, { Transaction } from "./TransactionCard";

import { Skeleton } from "@/components/ui/skeleton";

interface TransactionsGridProps {
  transactions?: Transaction[];
  isLoading?: boolean;
}

const TransactionsGrid = ({
  transactions = [
    {
      id: "1",
      type: "income",
      amount: 2500,
      description: "Website Development",
      date: "2024-04-15",
      category: "Freelance Work",
    },
    {
      id: "2",
      type: "expense",
      amount: 800,
      description: "Software Licenses",
      date: "2024-04-10",
      category: "Tools",
    },
    {
      id: "3",
      type: "income",
      amount: 1500,
      description: "Logo Design",
      date: "2024-04-08",
      category: "Design Work",
    },
    {
      id: "4",
      type: "expense",
      amount: 200,
      description: "Cloud Services",
      date: "2024-04-05",
      category: "Infrastructure",
    },
  ],
  isLoading = false,
}: TransactionsGridProps) => {
  const totalIncome = transactions
    .filter((t) => t.type === "income")
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactions
    .filter((t) => t.type === "expense")
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <div className="w-full bg-black/50 p-4 rounded-lg border border-white/10">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-6">
        <h2 className="text-2xl font-semibold">Recent Transactions</h2>
        <div className="flex flex-wrap gap-4">
          <div className="text-right">
            <p className="text-sm text-gray-500">Total Income</p>
            <p className="text-lg font-semibold text-green-600">
              +${totalIncome.toLocaleString()}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Total Expenses</p>
            <p className="text-lg font-semibold text-red-600">
              -${totalExpenses.toLocaleString()}
            </p>
          </div>
        </div>
      </div>
      <div className="grid gap-4">
        {isLoading
          ? Array(4)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-16 w-full" />
                </div>
              ))
          : transactions.map((transaction) => (
              <TransactionCard key={transaction.id} transaction={transaction} />
            ))}
      </div>
    </div>
  );
};

export default TransactionsGrid;