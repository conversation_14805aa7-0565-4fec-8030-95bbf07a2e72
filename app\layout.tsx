import type { Metada<PERSON> } from "next";
import "./globals.css";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import QueryProvider from '@/components/providers/query-provider';

const montserrat = Montserrat({
  weight: ["100", "200", "400", "700"],
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "400", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "NextGen Business",
  description: "The next generation of business management.",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`min-h-screen bg-background font-sans antialiased ${montserrat.className} ${poppins.className}`}>
        <ThemeProvider 
        attribute="class" 
        defaultTheme="dark" 
        enableSystem 
        disableTransitionOnChange>
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
