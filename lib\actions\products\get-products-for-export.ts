"use server";

import { db } from "@/db/drizzle";
import { getServerUserContext } from "@/lib/server-auth";

export async function getProductsForExport() {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;

  try {
    const productsToExport = await db.query.products.findMany({
      where: (products, { eq }) => eq(products.organizationId, orgId),
      with: {
        category: {
          columns: {
            name: true,
          },
        },
        revenueAccount: {
          columns: {
            name: true,
          },
        },
        inventoryAccount: {
          columns: {
            name: true,
          },
        },
      },
      orderBy: (products, { asc }) => [asc(products.name)],
    });

    return { success: true, data: productsToExport };
  } catch (error) {
    console.error("Failed to fetch products for export:", error);
    return { success: false, message: "Could not fetch products for export." };
  }
} 