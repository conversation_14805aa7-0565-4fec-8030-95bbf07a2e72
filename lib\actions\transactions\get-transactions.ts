"use server";

import { db } from "@/db/drizzle";
import { transactions } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq } from "drizzle-orm";

async function getUserOrganizationId(userId: string) {
  const { member } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: member.organizationId })
    .from(member)
.where(eq(member.userId, userId));
  return orgs[0]?.organizationId || null;
}

export async function getTransactions() {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;
  if (!userId) return [];
  const organizationId = await getUserOrganizationId(userId);
  if (!organizationId) return [];

  const transactionList = await db
    .select({
      id: transactions.id,
      date: transactions.date,
      description: transactions.description,
      totalAmount: transactions.totalAmount,
      status: transactions.status,
      type: transactions.type,
    })
    .from(transactions)
    .where(eq(transactions.organizationId, organizationId))
    .orderBy(transactions.date);

  return transactionList.map(tx => ({
    ...tx,
    date: tx.date.toLocaleDateString(),
  }));
} 