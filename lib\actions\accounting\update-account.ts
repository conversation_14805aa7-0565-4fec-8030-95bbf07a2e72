"use server";

import { accountFormSchema, FormState } from "@/lib/actions/accounting/account.schema";
import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { Account } from "@/components/protectedPages/Accounting/columns";

export async function updateAccount(
  accountId: string,
  accountData: Omit<Account, "id" | "createdAt" | "updatedAt" | "organizationId">
): Promise<FormState> {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;

  const validatedFields = accountFormSchema.safeParse(accountData);
  if (!validatedFields.success) {
    return { success: false, message: "Error: Invalid fields provided." };
  }

  try {
    await db
      .update(accounts)
      .set({
        ...validatedFields.data,
        isHeader: validatedFields.data.isHeader ?? false,
        isActive: validatedFields.data.isActive ?? true,
      })
      .where(and(eq(accounts.id, accountId), eq(accounts.organizationId, orgId)));
    revalidatePath("/accounting/chart-of-accounts");
    return { success: true, message: "Account updated successfully." };
  } catch (error) {
    return { success: false, message: "Error: Failed to update account." };
  }
} 