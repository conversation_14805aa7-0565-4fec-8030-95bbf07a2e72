"use server";

import { db } from "@/db/drizzle";
import { eq } from "drizzle-orm";
import { onboarding, member, organizationAddresses } from "@/db/schema/schema";
import { ONBOARDING_STEPS, OnboardingProgress } from "@/lib/onboarding-constants";
import { mapDbRowToOnboardingProgress } from "@/lib/actions/onboarding/mapDbRowToOnboardingProgress";

/**
 * Update onboarding step and data
 */
export async function updateOnboardingStep(
  userId: string, 
  step: number,
  stepData?: Record<string, any>
): Promise<OnboardingProgress> {
  try {
    // First, retrieve the existing onboarding progress
    const [existingProgress] = await db
      .select()
      .from(onboarding)
      .where(eq(onboarding.userId, userId));

    if (!existingProgress) {
      console.error(`No onboarding row found for userId: ${userId} when updating step ${step}`);
      throw new Error(`No onboarding row found for userId: ${userId}. Onboarding must be initialized before updating steps.`);
    }

    // Prepare update values
    const updateValues: any = {
      step,
      lastActiveStep: step,
      updatedAt: new Date(),
    };

    // Preserve existing data when moving between steps
    if (existingProgress) {
      updateValues.orgBasics = existingProgress.orgBasics;
      updateValues.orgDetails = existingProgress.orgDetails;
      updateValues.businessDetails = existingProgress.businessDetails;
      updateValues.subscription = existingProgress.subscription;
      updateValues.legalConsent = existingProgress.legalConsent;
      updateValues.legalConsentAcceptedAt = existingProgress.legalConsentAcceptedAt;
      updateValues.isCompleted = existingProgress.isCompleted;
      updateValues.completedAt = existingProgress.completedAt;
    }

    // Add step-specific data updates, giving priority to new data
    if (stepData) {
      switch (step) {
        case ONBOARDING_STEPS.ORGANIZATION_BASICS:
          updateValues.orgBasics = stepData;
          break;
        case ONBOARDING_STEPS.ORGANIZATION_DETAILS:
          updateValues.orgDetails = stepData;
          // Upsert multiple addresses in organizationAddresses if addresses array is present
          if (stepData && Array.isArray(stepData.addresses)) {
            // Find the user's organization
            const orgMembership = await db
              .select()
              .from(member)
.where(eq(member.userId, userId));
            if (orgMembership.length > 0) {
              const orgId = orgMembership[0].organizationId;
              // Fetch all existing addresses for the org
              const existingAddresses = await db
                .select()
                .from(organizationAddresses)
                .where(eq(organizationAddresses.organizationId, orgId));
              for (const addr of stepData.addresses) {
                // Try to find an existing address of this type
                const existing = existingAddresses.find(a => a.type === addr.type);
                if (existing) {
                  await db.update(organizationAddresses)
                    .set({
                      streetAddress: addr.street,
                      address2: addr.address2,
                      city: addr.city,
                      zipCode: addr.zip,
                      country: addr.country,
                      timeZone: addr.timeZone,
                      updatedAt: new Date(),
                    })
                    .where(eq(organizationAddresses.id, existing.id));
                } else {
                  await db.insert(organizationAddresses).values({
                    id: crypto.randomUUID(),
                    organizationId: orgId,
                    type: addr.type,
                    streetAddress: addr.street,
                    address2: addr.address2,
                    city: addr.city,
                    zipCode: addr.zip,
                    country: addr.country,
                    timeZone: addr.timeZone,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  });
                }
              }
            }
          }
          break;
        case ONBOARDING_STEPS.BUSINESS_TYPE:
          updateValues.businessDetails = stepData;
          break;
        case ONBOARDING_STEPS.SUBSCRIPTION:
          updateValues.subscription = stepData;
          break;
        // Do NOT set isCompleted or completedAt here for COMPLETION step
        case ONBOARDING_STEPS.COMPLETION:
          // Only update step, do not mark as completed here
          break;
      }
    }

    const result = await db
      .update(onboarding)
      .set(updateValues)
      .where(eq(onboarding.userId, userId))
      .returning();

    if (!result || result.length === 0) {
      throw new Error('Failed to update onboarding progress');
    }

    return mapDbRowToOnboardingProgress(result[0]);
  } catch (error) {
    console.error('Error updating onboarding step:', error);
    throw error;
  }
}

/**
 * Reset onboarding progress for a user
 */
export async function resetOnboardingProgress(userId: string): Promise<void> {
  try {
    await db.delete(onboarding)
      .where(eq(onboarding.userId, userId));
  } catch (error) {
    console.error('Error resetting onboarding progress:', error);
    throw error;
  }
} 