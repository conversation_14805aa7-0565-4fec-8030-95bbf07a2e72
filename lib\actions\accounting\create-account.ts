"use server";

import { db } from "@/db/drizzle";
import { accountFormSchema, FormState } from "@/lib/actions/accounting/account.schema";
import { accounts } from "@/db/schema/schema";
import { revalidatePath } from "next/cache";
import { getServerUserContext } from "@/lib/server-auth";
import { randomUUID } from 'crypto';
import { eq } from "drizzle-orm";

// Server Action to create a new account
export async function createAccount(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  const context = await getServerUserContext();
  const organizationId = context.organization.id;

  const validatedFields = accountFormSchema.safeParse(
    Object.fromEntries(formData.entries())
  );

  if (!validatedFields.success) {
    return { success: false, message: "Error: Invalid fields provided." };
  }

  try {
    await db.insert(accounts).values({
      id: randomUUID(),
      ...validatedFields.data,
      isHeader: validatedFields.data.isHeader ?? false,
      isActive: validatedFields.data.isActive ?? true,
      organizationId,
    });
    revalidatePath("/accounting/chart-of-accounts");
    return { success: true, message: "Account created successfully." };
  } catch (error) {
    return { success: false, message: "Error: Failed to create account." };
  }
} 