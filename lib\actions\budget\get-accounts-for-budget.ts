"use server";

import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, asc } from "drizzle-orm";

export async function getAccountsForBudget() {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    const revenueAccounts = await db.select({ id: accounts.id, name: accounts.name, accountNumber: accounts.accountNumber }).from(accounts).where(and(eq(accounts.organizationId, organizationId), eq(accounts.type, "revenue"), eq(accounts.isHeader, false), eq(accounts.isActive, true))).orderBy(asc(accounts.accountNumber));
    const expenseAccounts = await db.select({ id: accounts.id, name: accounts.name, accountNumber: accounts.accountNumber }).from(accounts).where(and(eq(accounts.organizationId, organizationId), eq(accounts.type, "expense"), eq(accounts.isHeader, false), eq(accounts.isActive, true))).orderBy(asc(accounts.accountNumber));
    return { revenue: revenueAccounts, expenses: expenseAccounts };
  } catch (error) {
    console.error("Error fetching accounts for budget:", error);
    return { revenue: [], expenses: [] };
  }
} 