import { useState, useEffect } from 'react';

export type Address = {
  type: 'primary' | 'billing' | 'shipping' | 'mailing' | string;
  street: string;
  address2?: string;
  city: string;
  zip: string;
  country: string;
  timeZone?: string;
};

export interface OrganizationData {
  businessType?: string;
  welcomeData?: Record<string, any>;
  businessTypeData?: Record<string, any>;
  organization?: {
    name?: string;
    slug?: string;
    email?: string;
    industry?: string;
    website?: string;
    legalEntity?: string;
    logo?: File | string | null;
  };
  orgDetails?: {
    timeZone?: string;
    addresses: Address[];
    phone?: string;
    website?: string;
    taxId?: string;
    taxIdType?: string;
    currency?: string;
  };
  chartOfAccounts?: {
    selectedTemplateId?: string;
  };
  subscription?: Record<string, any>;
  completionData?: Record<string, any>;
  businessDetails?: {
    businessType: string;
    selectedAt?: string;
    chartTemplate?: string;
  };
  legalConsentAcceptedAt?: string;
  userId?: string;
}

const STORAGE_KEY = 'onboarding_data';

export function useOnboardingStore() {
  const [data, setData] = useState<OrganizationData>(() => {
    // Only attempt to use localStorage on the client side
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsed = JSON.parse(savedData);
        // Ensure slug is present in organization
        if (parsed.organization && typeof parsed.organization === 'object' && !('slug' in parsed.organization)) {
          parsed.organization.slug = '';
        }
        return parsed;
      }
      return { organization: { slug: '' } };
    }
    return { organization: { slug: '' } };
  });

  // Persist data to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    }
  }, [data]);

  // Method to update specific parts of the onboarding data
  const updateData = (newData: Partial<OrganizationData>) => {
    setData(prevData => ({
      ...prevData,
      ...newData
    }));
  };

  // Method to reset/clear onboarding data
  const resetData = () => {
    setData({});
    localStorage.removeItem(STORAGE_KEY);
  };

  return {
    data,
    updateData,
    resetData
  };
} 