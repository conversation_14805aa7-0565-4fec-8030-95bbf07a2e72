"use server";

import { db } from "@/db/drizzle";
import { productCategories } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";

export async function getProductCategories() {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const categories = await db
      .select({
        id: productCategories.id,
        name: productCategories.name,
      })
      .from(productCategories)
      .where(eq(productCategories.organizationId, orgId))
      .orderBy(productCategories.name);

    return categories;
  } catch (error) {
    console.error("Failed to fetch product categories:", error);
    return [];
  }
} 