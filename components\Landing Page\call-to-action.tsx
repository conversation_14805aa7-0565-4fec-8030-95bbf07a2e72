'use client';

import { But<PERSON> } from '@/components/ui/button'
import { authClient } from '@/lib/auth-client'
import { toast } from 'sonner'
import Link from 'next/link'

export default function CallToAction() {
    const handleGetStarted = async () => {
        try {
            // Start with the most popular Professional plan
            await authClient.checkout({
                products: ['professional-monthly'],
                slug: 'professional-monthly',
            });
        } catch (error) {
            console.error('Checkout error:', error);
            toast.error('Something went wrong. Please try again.');
        }
    };

    return (
        <section className="py-16 md:py-32">
            <div className="mx-auto max-w-5xl px-6">
                <div className="text-center">
                    <h2 className="text-balance text-4xl font-semibold lg:text-5xl">Ready to Transform Your Business?</h2>
                    <p className="mt-4">Join thousands of businesses already using NextGen Business to streamline their accounting and boost profitability.</p>

                    <div className="mt-12 flex flex-wrap justify-center gap-4">
                        <Button size="lg" onClick={handleGetStarted}>
                            <span>Start 7-Day Free Trial</span>
                        </Button>

                        <Button asChild size="lg" variant="outline">
                            <Link href="/#pricing">
                                <span>View Pricing</span>
                            </Link>
                        </Button>
                    </div>
                    
                              <p className="mt-4 text-sm text-muted-foreground">
            7-day free trial • Cancel anytime • Setup in 5 minutes
          </p>
                </div>
            </div>
        </section>
    )
}
