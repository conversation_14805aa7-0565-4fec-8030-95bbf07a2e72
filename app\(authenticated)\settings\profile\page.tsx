import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { getCurrentUserInfo } from "@/lib/actions/user/get-current-user";
import { CircleUserRound, AlertTriangle } from "lucide-react";
import { TwoFactorAuthSwitch } from "@/components/protectedPages/organization/TwoFactorAuthSwitch";

export default async function ProfileSettingsPage({ searchParams }: { searchParams: { error?: string }}) {
  const user = await getCurrentUserInfo();

  if (!user) {
    return <div>User not found.</div>;
  }

  return (
    <div className="grid gap-6">
      {searchParams.error === '2fa_required' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Two-Factor Authentication Required</AlertTitle>
          <AlertDescription>
            Your organization requires you to enable Two-Factor Authentication to continue.
          </AlertDescription>
        </Alert>
      )}
      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
          <CardDescription>Update your personal information.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={user.image ?? undefined} />
              <AvatarFallback>
                <CircleUserRound className="h-10 w-10" />
              </AvatarFallback>
            </Avatar>
            <div className="grid gap-1">
              <h2 className="text-lg font-semibold">{user.name}</h2>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Security</CardTitle>
          <CardDescription>Manage your account security settings.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label htmlFor="2fa-switch" className="text-base">
                  Two-Factor Authentication
                </Label>
                <p className="text-sm text-muted-foreground">
                  Require a code from your email for every login.
                </p>
              </div>
              <TwoFactorAuthSwitch isEnabled={user.twoFactorEnabled} />
            </div>
            <div>
              <Button variant="outline">Change Password</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 