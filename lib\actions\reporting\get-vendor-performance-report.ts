"use server";

import { db } from "@/db/drizzle";
import { bills, vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, gte, lte, desc, sql, sum } from "drizzle-orm";

export async function getVendorPerformanceReport(fromDate?: string, toDate?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;

    let billFilter = [eq(bills.organizationId, organizationId)];
    if (fromDate) billFilter.push(gte(bills.date, new Date(fromDate)));
    if (toDate) billFilter.push(lte(bills.date, new Date(toDate)));

    const vendorStats = await db.select({
        vendorId: vendors.id,
        vendorName: vendors.name,
        totalBilled: sum(bills.total),
        billCount: sql<number>`count(${bills.id})`
    })
    .from(vendors)
    .leftJoin(bills, eq(vendors.id, bills.vendorId))
    .where(and(...billFilter))
    .groupBy(vendors.id, vendors.name)
    .orderBy(desc(sum(bills.total)));

    // Calculate payment performance metrics
    const report = vendorStats.map(v => {
        const totalBilled = Number(v.totalBilled) || 0;
        const billCount = Number(v.billCount) || 0;
        
        return {
            vendorId: v.vendorId,
            vendorName: v.vendorName,
            totalBilled,
            billCount,
            averageBillAmount: billCount > 0 ? totalBilled / billCount : 0,
            onTimePaymentRate: "N/A", // Would require payment tracking
            averagePaymentTime: "N/A"  // Would require payment tracking
        };
    });
    
    return report;
} 