import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { getAllFeatureFlags, setFeatureFlag } from "@/lib/feature-flags";

/**
 * GET /api/admin/feature-flags
 * Get all feature flags (for admin dashboard)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication (in dev, you might want to skip this or use a simple check)
    const session = await auth.api.getSession({ headers: await headers() });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // In dev environment, allow any authenticated user
    // In production, you'd check for admin role
    if (process.env.NODE_ENV === 'production') {
      // TODO: Add admin role check
      // if (!isAdmin(session.user)) {
      //   return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      // }
    }

    const flags = await getAllFeatureFlags();

    return NextResponse.json({
      flags,
      environment: process.env.NODE_ENV,
      message: "Feature flags retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching feature flags:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/feature-flags
 * Update a feature flag
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth.api.getSession({ headers: await headers() });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // In dev environment, allow any authenticated user
    if (process.env.NODE_ENV === 'production') {
      // TODO: Add admin role check
    }

    const body = await request.json();
    const { flagKey, enabled, userPercentage, userIds, description } = body;

    if (!flagKey || typeof enabled !== 'boolean') {
      return NextResponse.json(
        { error: "flagKey and enabled are required" },
        { status: 400 }
      );
    }

    await setFeatureFlag(flagKey, enabled, {
      userPercentage,
      userIds,
      description,
    });

    // Get updated flags to return
    const flags = await getAllFeatureFlags();

    return NextResponse.json({
      success: true,
      message: `Feature flag '${flagKey}' updated successfully`,
      flags,
    });
  } catch (error) {
    console.error("Error updating feature flag:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 