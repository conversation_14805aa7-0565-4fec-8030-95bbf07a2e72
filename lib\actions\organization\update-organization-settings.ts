'use server';

import { z } from 'zod';
import { db } from '@/db/drizzle';
import { organization, organizationAddresses } from "@/db/schema/schema";
import { eq, and } from 'drizzle-orm';
import { getServerUserContext } from '@/lib/server-auth';
import { revalidatePath } from 'next/cache';
import { createAuditLog } from '../audit/create-audit-log';
import { auth } from "@/lib/auth";

const addressSchema = z.object({
  streetAddress: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
  timeZone: z.string().optional(),
});

const updateOrganizationSchema = z.object({
  name: z.string().min(2, 'Organization name must be at least 2 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  industry: z.string().optional(),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  phone: z.string().optional(),
  taxId: z.string().optional(),
  currency: z.string().optional(),
  address: addressSchema.optional(),
  requireTwoFactor: z.boolean().optional(),
});

function getChangedFields(oldData: any, newData: any, oldAddress: any, newAddress: any): string[] {
    const changedFields: string[] = [];
    for (const key in newData) {
        if (Object.prototype.hasOwnProperty.call(newData, key) && key !== 'address') {
            if (newData[key] !== oldData[key]) {
                changedFields.push(key);
            }
        }
    }
    // Compare address fields
    if (newAddress && oldAddress) {
      for (const key in newAddress) {
        if (newAddress[key] !== oldAddress[key]) {
          changedFields.push(`address.${key}`);
        }
      }
    } else if (newAddress || oldAddress) {
      changedFields.push('address');
    }
    return changedFields;
}

export async function updateOrganizationSettingsAction(
  values: z.infer<typeof updateOrganizationSchema>
) {
  let orgCtx;
  try {
    const context = await getServerUserContext();
    orgCtx = context.organization;
  } catch (error: any) {
    if (error && error.message && error.message.includes('NEXT_REDIRECT')) {
      return {
        success: false,
        error: 'User context redirect triggered. User may not be authorized or onboarding is incomplete.'
      };
    }
    throw error;
  }
  // No manual membership/role check needed; plugin enforces permissions
  const validation = updateOrganizationSchema.safeParse(values);

  if (!validation.success) {
    return {
      success: false,
      error: 'Invalid data provided.',
      errors: validation.error.flatten().fieldErrors,
    };
  }
  
  const data = validation.data;

  try {
    const originalOrganization = orgCtx;

    // Fetch old address for audit
    const [oldAddress] = await db
      .select()
      .from(organizationAddresses)
      .where(
        and(
          eq(organizationAddresses.organizationId, orgCtx.id),
          eq(organizationAddresses.type, "primary")
        )
      );

    // Use Better Auth plugin for supported fields
    console.log("Updating organization with data:", data);
    await (auth.api as any).organization.update({
      organizationId: orgCtx.id,
      data: {
        name: data.name,
        email: data.email,
        industry: data.industry,
        website: data.website,
        phone: data.phone,
        taxId: data.taxId,
        currency: data.currency,
        metadata: {
          requireTwoFactor: data.requireTwoFactor,
        }
      }
    });

    // Upsert primary address if provided
    let newAddress = null;
    if (data.address) {
      const [existing] = await db
        .select()
        .from(organizationAddresses)
        .where(
          and(
            eq(organizationAddresses.organizationId, orgCtx.id),
            eq(organizationAddresses.type, "primary")
          )
        );
      if (existing) {
        await db.update(organizationAddresses)
          .set({
            streetAddress: data.address.streetAddress,
            address2: data.address.address2,
            city: data.address.city,
            zipCode: data.address.zipCode,
            country: data.address.country,
            timeZone: data.address.timeZone,
            updatedAt: new Date(),
          })
          .where(eq(organizationAddresses.id, existing.id));
        newAddress = { ...existing, ...data.address };
      } else {
        const addressObj = {
          id: crypto.randomUUID(),
          organizationId: orgCtx.id,
          type: "primary",
          streetAddress: data.address.streetAddress,
          address2: data.address.address2,
          city: data.address.city,
          zipCode: data.address.zipCode,
          country: data.address.country,
          timeZone: data.address.timeZone,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        await db.insert(organizationAddresses).values(addressObj);
        newAddress = addressObj;
      }
    }

    revalidatePath('/settings/organization');
    
    await createAuditLog({
      action: 'update',
      resourceType: 'organization',
      resourceId: orgCtx.id,
      description: `Updated general settings for organization "${orgCtx.name}"`,
      oldValues: {
          name: originalOrganization.name,
          email: originalOrganization.email,
          industry: originalOrganization.industry,
          website: originalOrganization.website,
          phone: originalOrganization.phone,
          taxId: originalOrganization.taxId,
          currency: originalOrganization.currency,
          requireTwoFactor: originalOrganization.requireTwoFactor,
          address: oldAddress || null,
      },
      newValues: {
          ...data,
          address: newAddress || null,
      },
      changedFields: getChangedFields(originalOrganization, data, oldAddress, newAddress),
    });

    return {
      success: true,
      message: 'Organization settings updated successfully.',
    };
  } catch (error) {
    console.error('Error updating organization settings:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
} 