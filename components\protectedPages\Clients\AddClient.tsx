import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { clientFormSchema, NewClient } from "@/lib/actions/clients/client.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import { useQuery } from "@tanstack/react-query";
import { getClientById } from "@/lib/actions/clients/get-client-by-id";
import { PhoneNumberInput } from "@/components/general/PhoneNumberInput";
import { CountrySelector } from "@/components/general/CountrySelector";


interface AddClientProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (client: NewClient) => void;
  clientId?: string | null;
  loading: boolean;
}

export default function AddClient({
  open,
  onOpenChange,
  onSubmit,
  clientId,
  loading,
}: AddClientProps) {
  const isEdit = !!clientId;
  const { data: client, isLoading: isLoadingClient } = useQuery({
    queryKey: ["client", clientId],
    queryFn: () => (clientId ? getClientById(clientId) : null),
    enabled: !!clientId && open,
  });

  const form = useForm<NewClient>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: { street: "", city: "", zip: "", country: "" },
    },
  });

  React.useEffect(() => {
    if (open && isEdit && client) {
      form.reset({
        ...client,
        email: client.email ?? "",
        phone: client.phone ?? "",
        address: client.address
          ? (client.address as any)
          : { street: "", city: "", zip: "", country: "" },
      });
    } else if (open && !isEdit) {
      form.reset({
        name: "",
        email: "",
        phone: "",
        address: { street: "", city: "", zip: "", country: "" },
      });
    }
  }, [open, isEdit, client, form]);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="!w-full sm:!w-[400px] md:!w-[500px] lg:!w-[600px] xl:!w-[700px] !max-w-[50vw] !p-0 !gap-0 flex flex-col h-screen overflow-hidden">
        {/* Fixed Header */}
        <SheetHeader className="border-b bg-background flex-shrink-0 !p-0 !gap-0">
          <div className="px-4 sm:px-6 py-4">
            <SheetTitle className="text-lg sm:text-xl font-semibold">{isEdit ? "Edit Client" : "Add New Client"}</SheetTitle>
            <SheetDescription className="text-sm text-muted-foreground mt-1">
              {isEdit
                ? "Update the details for this client."
                : "Add a new client to your records. Fill out the form below."}
            </SheetDescription>
          </div>
        </SheetHeader>

        {isLoadingClient ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-muted-foreground">Loading client details...</div>
          </div>
        ) : (
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex flex-col flex-1 min-h-0"
            >
              {/* Scrollable Content Area */}
              <div className="flex-1 overflow-y-auto min-h-0">
                <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 pb-8">
                  {/* Basic Information Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 pb-2">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium">Basic Information</h3>
                    </div>
                    
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-foreground">Client Name *</FormLabel>
                            <FormControl>
                              <Input 
                                {...field} 
                                className="h-10"
                                placeholder="Enter client name"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-foreground">Email Address</FormLabel>
                              <FormControl>
                                <Input 
                                  type="email" 
                                  {...field} 
                                  className="h-10"
                                  placeholder="<EMAIL>"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-foreground">Phone Number</FormLabel>
                              <FormControl>
                                <PhoneNumberInput {...field} value={field.value ?? ""} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Address Information Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 pb-2">
                      <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium">Address Information</h3>
                    </div>
                    
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="address.street"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-foreground">Street Address</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="123 Main Street, Suite 100" 
                                {...field} 
                                className="h-10"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="address.city"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-foreground">City</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="New York" 
                                  {...field} 
                                  className="h-10"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="address.zip"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-foreground">Zip Code</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="10001" 
                                  {...field} 
                                  className="h-10"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="address.country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-foreground">Country</FormLabel>
                            <FormControl>
                              <CountrySelector
                                value={field.value ?? ""}
                                onChange={field.onChange}
                                className="h-10"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Fixed Footer */}
              <div className="border-t bg-background flex-shrink-0 mt-auto">
                <div className="px-4 sm:px-6 py-4 flex flex-col sm:flex-row gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    className="flex-1 h-10"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={loading}
                    className="flex-1 h-10"
                  >
                    {loading
                      ? "Saving..."
                      : isEdit
                      ? "Update Client"
                      : "Save"}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        )}
      </SheetContent>
    </Sheet>
  );
} 