"use server";

import { db } from "@/db/drizzle";
import { products, productCategories, accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";
import { createProduct } from "./create-product";
import { updateProduct } from "./update-product";

export async function importProducts(data: any[]) {
  const { organization } = await getServerUserContext();
  if (!organization) {
    return { success: false, message: "Not authenticated" };
  }
  const orgId = organization.id;

  const results = {
    success: true,
    created: 0,
    updated: 0,
    errors: [] as { row: number; errors: string[] }[],
  };

  const categoryNames = [...new Set(data.map(row => row.category).filter(Boolean))];
  const existingCategories = categoryNames.length > 0 ? await db.query.productCategories.findMany({
    where: and(
        eq(productCategories.organizationId, orgId),
        inArray(productCategories.name, categoryNames)
    )
  }) : [];
  const categoryMap = new Map(existingCategories.map(c => [c.name, c.id]));

  const accountNames = [...new Set([
      ...data.map(row => row.revenueAccount).filter(Boolean),
      ...data.map(row => row.cogsAccount).filter(Boolean),
      ...data.map(row => row.inventoryAccount).filter(Boolean),
  ])];

  const existingAccounts = accountNames.length > 0 ? await db.query.accounts.findMany({
      where: and(
          eq(accounts.organizationId, orgId),
          inArray(accounts.name, accountNames)
      )
  }) : [];
  const accountMap = new Map(existingAccounts.map(a => [a.name, a.id]));


  for (const [index, row] of data.entries()) {
    const rowErrors: string[] = [];
    
    // Basic validation
    if (!row.name) rowErrors.push("Product name is required.");
    if (!row.type) rowErrors.push("Product type is required.");
    
    // Lookups
    let categoryId = row.category ? categoryMap.get(row.category) : undefined;
    if (row.category && !categoryId) rowErrors.push(`Category "${row.category}" not found.`);

    let revenueAccountId = row.revenueAccount ? accountMap.get(row.revenueAccount) : undefined;
    if (row.revenueAccount && !revenueAccountId) rowErrors.push(`Revenue account "${row.revenueAccount}" not found.`);
    if (!revenueAccountId) rowErrors.push("Revenue account is required.");
    
    let cogsAccountId = row.cogsAccount ? accountMap.get(row.cogsAccount) : undefined;
    if (row.cogsAccount && !cogsAccountId) rowErrors.push(`COGS account "${row.cogsAccount}" not found.`);

    let inventoryAccountId = row.inventoryAccount ? accountMap.get(row.inventoryAccount) : undefined;
    if (row.inventoryAccount && !inventoryAccountId) rowErrors.push(`Inventory account "${row.inventoryAccount}" not found.`);


    if (rowErrors.length > 0) {
      results.errors.push({ row: index + 1, errors: rowErrors });
      continue;
    }
    
    const productData = {
        name: row.name as string,
        description: row.description,
        productType: row.type,
        price: row.price ? parseFloat(row.price) : undefined,
        costBasis: row.costBasis ? parseFloat(row.costBasis) : undefined,
        sku: row.sku,
        isActive: row.isActive === 'true',
        categoryId,
        revenueAccountId: revenueAccountId as string,
        cogsAccountId,
        inventoryAccountId,
    };

    try {
        if(row.id) {
            const result = await updateProduct(row.id, productData);
            if(result.success) results.updated++;
            else results.errors.push({ row: index + 1, errors: [result.message] });
        } else {
            const result = await createProduct(productData);
            if(result.success) results.created++;
            else results.errors.push({ row: index + 1, errors: [result.message] });
        }
    } catch (e: any) {
        results.errors.push({ row: index + 1, errors: [e.message] });
    }
  }
  
  return results;
} 