import countriesData from './data/countries.json';

export type Country = {
    name: string;
    code: string; // ISO 3166-1 alpha-2
    phone: string;
    continent: string;
  };
  
  export type Continent = {
    name: string;
    code: string;
    countries: Country[];
  };

  export const countries: Country[] = countriesData;

  const continentNames: Record<string, string> = {
    AF: 'Africa',
    AN: 'Antarctica',
    AS: 'Asia',
    EU: 'Europe',
    NA: 'North America',
    OC: 'Oceania',
    SA: 'South America',
  };
  
  const groupedByContinent = countries.reduce((acc, country) => {
    if (!acc[country.continent]) {
      acc[country.continent] = [];
    }
    acc[country.continent].push(country);
    return acc;
  }, {} as Record<string, Country[]>);
  
  export const continents: Continent[] = Object.keys(groupedByContinent)
    .map(continentCode => ({
      name: continentNames[continentCode],
      code: continentCode,
      countries: groupedByContinent[continentCode].sort((a, b) => a.name.localeCompare(b.name)),
    }))
    .sort((a, b) => a.name.localeCompare(b.name)); 