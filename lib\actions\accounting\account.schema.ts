import { z } from "zod";

export const accountFormSchema = z.object({
  accountNumber: z.string().min(1, "Account number is required."),
  name: z.string().min(2, "Name must be at least 2 characters."),
  type: z.enum([
    "asset", "liability", "equity", "revenue", "expense"
  ]),
  normalBalance: z.enum(["debit", "credit"]),
  isHeader: z.boolean().optional().default(false),
  isActive: z.boolean().optional().default(true),
  description: z.string().optional(),
  parentId: z.string().nullable().optional(),
  
  // Enhanced professional accounting fields
  level: z.number().optional().default(0),
  classification: z.enum([
    "current", "non_current", "operating", "non_operating", "n_a"
  ]).nullable().optional(),
  financialStatementSection: z.enum([
    "current_assets", "non_current_assets", "current_liabilities", 
    "non_current_liabilities", "equity", "operating_revenue", "other_revenue",
    "cost_of_goods_sold", "operating_expenses", "other_expenses"
  ]).nullable().optional(),
  accountGroup: z.enum([
    "cash_and_equivalents", "accounts_receivable", "inventory", "prepaid_expenses",
    "short_term_investments", "property_plant_equipment", "accumulated_depreciation",
    "intangible_assets", "long_term_investments", "other_assets", "accounts_payable",
    "accrued_liabilities", "short_term_debt", "unearned_revenue", "long_term_debt",
    "other_liabilities", "capital_stock", "retained_earnings", "other_equity",
    "product_sales", "service_revenue", "other_revenue", "cost_of_sales",
    "selling_expenses", "administrative_expenses", "depreciation_amortization",
    "interest_expense", "other_expenses", "other"
  ]).nullable().optional(),
  cashFlowCategory: z.enum([
    "operating", "investing", "financing", "n_a"
  ]).nullable().optional(),
  displayOrder: z.number().optional().default(0),
  subcategory: z.string().nullable().optional(),
  gaapCategory: z.string().nullable().optional(),
  ifrsCategory: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
});

export type NewAccount = z.infer<typeof accountFormSchema>;

export type FormState = {
  success: boolean;
  message: string;
}; 