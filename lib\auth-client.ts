import { createAuthClient } from "better-auth/react"
import { polarClient } from "@polar-sh/better-auth";
import { twoFactorClient } from "better-auth/client/plugins";
import { organizationClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
    /** the base url of the server (optional if you're using the same domain) */
    baseURL: process.env.NODE_ENV === "production" 
        ? "https://your-domain.com" // Replace with your actual domain
        : "http://localhost:3000",
    emailAndPassword: {
        enabled: true,
        resetPassword: true
    },
    email: {
        enabled: true
    },
    plugins: [
        polarClient(),
        twoFactorClient({
            onTwoFactorRedirect() {
                // This will automatically redirect the user to the verification page
                // after a successful sign-in if they have 2FA enabled.
                window.location.href = "/verify-2fa";
            }
        }),
        organizationClient(),
    ],
})

export const { useSession, signIn, signOut, signUp } = authClient;
