"use server";

import { db } from "@/db/drizzle";
import { organization } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";

export async function softDeleteOrganization(reason?: string, defaultReason: string = "deleted") {
  const { organization } = await getServerUserContext();
  if (!organization.id) {
    return { success: false, message: "No organization found." };
  }
  await db.update(organization)
    .set({
      isActive: false,
      deactivatedAt: new Date(),
      deactivationReason: reason || defaultReason,
    })
    .where(eq(organization.id, organization.id));
  return { success: true, message: "Organization deactivated." };
} 