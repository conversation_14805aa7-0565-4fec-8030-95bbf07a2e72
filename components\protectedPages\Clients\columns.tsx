"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { clients } from "@/db/schema/schema";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import * as React from "react";
import { Checkbox } from "@/components/ui/checkbox";

// This type is manually created based on the Drizzle schema.
export type Client = typeof clients.$inferSelect;

function formatAddress(address: any) {
  if (!address) return "-";
  // Example: { street, city, zip, country }
  const { street, city, zip, country } = address;
  return [street, city, zip, country].filter(Boolean).join(", ");
}

interface Actions {
  onEdit: (client: Client) => void;
  onDelete: (clientId: string) => void;
}

export function getClientColumns({ onEdit, onDelete }: Actions): ColumnDef<Client>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "address",
      header: "Address",
      cell: ({ row }) => formatAddress(row.original.address),
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const client = row.original;
        const [dropdownOpen, setDropdownOpen] = React.useState(false);

        return (
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem 
                onSelect={(event) => {
                  event.preventDefault();
                  setDropdownOpen(false);
                  onEdit(client);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(client.id)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}