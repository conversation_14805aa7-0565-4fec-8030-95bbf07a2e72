"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Fragment } from "react";
import { ChevronRight } from "lucide-react";

const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1);

export function Breadcrumbs() {
  const pathname = usePathname();
  
  if (pathname === '/dashboard') {
    return (
      <nav className="flex items-center text-sm font-medium text-gray-800 dark:text-gray-200">
        Dashboard
      </nav>
    )
  }

  const pathSegments = pathname.split('/').filter(Boolean);

  // Don't show breadcrumbs for root of authenticated section
  if (pathSegments.length === 0) {
    return null;
  }

  return (
    <nav className="flex items-center text-sm font-medium text-gray-500">
      <Link href="/dashboard" className="hover:text-gray-700 dark:hover:text-gray-300">
        Dashboard
      </Link>
      {pathSegments.map((segment, index) => {
        const href = `/${pathSegments.slice(0, index + 1).join('/')}`;
        const isLast = index === pathSegments.length - 1;
        
        // Replace dynamic segments like IDs with a more generic name if needed in the future
        const title = capitalize(segment.replace(/-/g, ' '));

        return (
          <Fragment key={href}>
            <ChevronRight className="w-4 h-4 mx-1" />
            <Link
              href={href}
              className={
                isLast
                  ? "text-gray-800 dark:text-gray-200"
                  : "hover:text-gray-700 dark:hover:text-gray-300"
              }
            >
              {title}
            </Link>
          </Fragment>
        );
      })}
    </nav>
  );
} 