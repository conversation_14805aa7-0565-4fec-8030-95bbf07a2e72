"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function deleteVendors(ids: string[]): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    if (ids.length === 0) {
      return { success: false, message: "No vendor IDs provided." };
    }

    await db
      .delete(vendors)
      .where(
        and(inArray(vendors.id, ids), eq(vendors.organizationId, orgId))
      );

    revalidatePath("/vendors");
    return { success: true, message: `${ids.length} vendor(s) deleted.` };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to delete vendors.",
    };
  }
} 