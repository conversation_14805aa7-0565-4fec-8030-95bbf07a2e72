import {
  pgTable,
  pgPolicy,
  uuid,
  text,
  timestamp,
  primaryKey,
  pgEnum,
  boolean,
  varchar,
  decimal,
  integer,
  jsonb,
  unique,
  index,
} from "drizzle-orm/pg-core";
import { sql, relations } from "drizzle-orm";
import { authenticatedRole, authUid } from "drizzle-orm/neon"
// Core Schema settings

// -- AUTHENTICATION (from better-auth) --
  
export const user = pgTable("user", {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  emailVerified: boolean('email_verified').$defaultFn(() => false).notNull(),
  image: text('image'),
  onboarded: boolean('onboarded').$defaultFn(() => false).notNull(),
  jobTitle: text('job_title'),
  phone: text('phone'),
  // 2FA field from better-auth plugin
  twoFactorEnabled: boolean('two_factor_enabled').$defaultFn(() => false).notNull(),
  // Multi-organization support (references added later to avoid circular dependency)
  lastActiveOrganizationId: uuid('last_active_organization_id'),
  defaultOrganizationId: uuid('default_organization_id'),
  createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('user_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: authUid(table.id)
  })
}));

export const session = pgTable("session", {
  id: text('id').primaryKey(),
  expiresAt: timestamp('expires_at').notNull(),
  token: text('token').notNull().unique(),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),
}, (table) => ({
  rlsPolicy: pgPolicy('session_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`auth.uid() = ${table.userId}`
  })
}));

export const account = pgTable("account", {
  id: text('id').primaryKey(),
  accountId: text('account_id').notNull(),
  providerId: text('provider_id').notNull(),
  userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  idToken: text('id_token'),
  accessTokenExpiresAt: timestamp('access_token_expires_at'),
  refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
  scope: text('scope'),
  password: text('password'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('account_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`auth.uid() = ${table.userId}`
  })
}));

export const verification = pgTable("verification", {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(),
  value: text('value').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull()
});

/**
 * Stores 2FA secrets and backup codes for users.
 * Required by the better-auth twoFactor plugin.
 */
export const twoFactor = pgTable("two_factor", {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  secret: text('secret'),
  backupCodes: text('backup_codes'),
});

// -- MULTI-TENANCY --
  
  /**
   * Organizations are the top-level tenant for data isolation.
   * All business-related data belongs to an organization.
   */
  export const invitationStatusEnum = pgEnum("invitation_status", [
    "pending",
    "accepted",
    "expired",
  ]);
  
  // Business type for onboarding flow
  export const businessTypeEnum = pgEnum("business_type", [
    "physical",  // Physical products, inventory, shipping
    "digital",   // Digital products/services, no physical inventory
    "hybrid"     // Mix of physical and digital
  ]);

  export const organizations = pgTable("organizations", {
    id: uuid("id").defaultRandom().primaryKey(),
    name: text("name").notNull(),
    ownerId: text("owner_id")
      .references(() => user.id, { onDelete: "cascade" })
      .notNull(),
    // Onboarding Security Fields (from migration 0002)
    createdBy: text("created_by").references(() => user.id),
    onboardingComplete: boolean("onboarding_complete").default(false),
    onboardingCompletedAt: timestamp("onboarding_completed_at", { withTimezone: true }),
    businessType: businessTypeEnum("business_type"),
    // New Flow Enhancement (migration 0003)
    onboardingFlowVersion: integer("onboarding_flow_version").default(1),
    email: text("email"),
    industry: text("industry"),
    website: text("website"),
    logoUrl: text("logo_url"),
    address: jsonb("address"), // { street, city, zip, country, timeZone }
    phone: text("phone"),
    taxId: text("tax_id"),
    taxIdType: text("tax_id_type"),
    currency: text("currency"),
    legalEntity: text("legal_entity"),
    howHeard: text("how_heard"),
    // Billing & Subscription fields
    subscriptionId: text("subscription_id"),
    planType: varchar("plan_type", { length: 50 }).default("trial"),
    planStatus: varchar("plan_status", { length: 50 }).default("trial"),
    trialEndsAt: timestamp("trial_ends_at", { withTimezone: true }),
    subscriptionCurrentPeriodStart: timestamp("subscription_current_period_start", { withTimezone: true }),
    subscriptionCurrentPeriodEnd: timestamp("subscription_current_period_end", { withTimezone: true }),
    monthlyTransactionLimit: integer("monthly_transaction_limit").default(50),
    monthlyTransactionCount: integer("monthly_transaction_count").default(0),
    lastTransactionResetDate: timestamp("last_transaction_reset_date", { withTimezone: true }),
    // Soft delete and account status
    isActive: boolean("is_active").default(true),
    deactivatedAt: timestamp("deactivated_at", { withTimezone: true }),
    deactivationReason: varchar("deactivation_reason", { length: 100 }), // 'subscription_cancelled', 'user_requested', 'violation'
    gracePeriodEndsAt: timestamp("grace_period_ends_at", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
    // Indexes for onboarding security lookups
    createdByIdx: index("idx_organizations_created_by").on(table.createdBy),
    onboardingCompleteIdx: index("idx_organizations_onboarding_complete").on(table.onboardingComplete),
    rlsPolicy: pgPolicy('organizations_rls_policty', {
      for: 'all',
      to: authenticatedRole,
      using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.id} AND "organization_members"."user_id" = auth.uid())`
    })
  }));
  
  // Enhanced role system for multi-organization architecture
  // Includes legacy roles (manager, editor, viewer) for backward compatibility
  export const memberRoleEnum = pgEnum("member_role", [
    "owner",   // Complete control + billing + organization settings
    "admin",   // All operational features + team management (no billing/org settings)
    "manager", // Legacy role - maps to admin
    "editor",  // Legacy role - maps to admin  
    "viewer",  // Legacy role - maps to member
    "member",  // Create invoices/transactions + view financials (no team management)
    "auditor"  // Reports and dashboards only (read-only access)
  ]);
  
  export const memberStatusEnum = pgEnum("member_status", [
    "pending",   // For an invitation
    "accepted",  // For an invitation that has been used
    "declined",  // For an invitation
    "expired",   // For an invitation
    "active",    // For a member
    "removed",   // For a member (soft delete)
  ]);
  
  /**
   * Junction table for user-organization membership and roles.
   * Determines which users have access to which organizations.
   */
  export const organizationMembers = pgTable(
    "organization_members",
    {
      userId: text("user_id")
        .references(() => user.id, { onDelete: "cascade" })
        .notNull(),
      organizationId: uuid("organization_id")
        .references(() => organizations.id, { onDelete: "cascade" })
        .notNull(),
      role: memberRoleEnum("role").default("member").notNull(),
      status: memberStatusEnum("status").default("active").notNull(),
      invitedBy: text("invited_by").references(() => user.id, { onDelete: "set null" }),
      // New Flow Enhancement (migration 0003)
      permissions: jsonb("permissions").default('[]'),
    },
    (table) => ({
      pk: primaryKey({ columns: [table.organizationId, table.userId] }),
      rlsPolicy: pgPolicy('organization_members_rls_policy', {
        for: 'all',
        to: authenticatedRole,
        using: sql`EXISTS (SELECT 1 FROM "organization_members" AS om_check WHERE om_check."organization_id" = ${table.organizationId} AND om_check."user_id" = auth.uid())`
      })
    })
  );
  
  /**
   * Stores pending invitations for users to join organizations.
   */
  export const organizationInvitations = pgTable(
    "organization_invitations",
    {
      id: uuid("id").defaultRandom().primaryKey(),
      organizationId: uuid("organization_id")
        .references(() => organizations.id, { onDelete: "cascade" })
        .notNull(),
      email: text("email").notNull(),
      role: memberRoleEnum("role").default("member").notNull(),
      invitedBy: text("invited_by").references(() => user.id, { onDelete: "set null" }),
      status: memberStatusEnum("status").default("pending").notNull(),
      expiresAt: timestamp("expires_at", { withTimezone: true }),
      createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
    },
    (table) => ({
      // Unique constraint to prevent sending multiple pending invites to the same email for the same org
      unique_org_email: unique("unique_org_email_idx").on(table.organizationId, table.email),
      rlsPolicy: pgPolicy('organization_invitations_rls_policty', {
        for: 'all',
        to: authenticatedRole,
        using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
      })
    })
  );

// -- ONBOARDING FLOW TABLES --

/**
 * Tracks onboarding progress for new organizations.
 * Enables resuming onboarding where user left off.
 */
export const onboardingProgress = pgTable("onboarding_progress", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: text("user_id")
    .references(() => user.id, { onDelete: "cascade" })
    .notNull(),
  step: integer("step").default(1).notNull(), // Current step (1-6)
  totalSteps: integer("total_steps").default(6).notNull(),
  // Step data stored as JSON for flexibility
  welcomeData: jsonb("welcome_data"), // Step 1
  businessTypeData: jsonb("business_type_data"), // Step 2
  organizationData: jsonb("organization_data"), // Step 3
  accountsData: jsonb("accounts_data"), // Step 4
  subscriptionData: jsonb("subscription_data"), // Step 5
  completionData: jsonb("completion_data"), // Step 6
  // Progress metadata
  lastActiveStep: integer("last_active_step").default(1),
  isCompleted: boolean("is_completed").default(false),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  userIdIdx: index("idx_onboarding_progress_user_id").on(table.userId),
  rlsPolicy: pgPolicy('onboarding_progress_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`auth.uid() = ${table.userId}`
  })
}));

/**
 * Pre-defined chart of accounts templates based on business type.
 * Provides intelligent defaults for different business models.
 */
export const businessTypeTemplates = pgTable("business_type_templates", {
  id: uuid("id").defaultRandom().primaryKey(),
  businessType: businessTypeEnum("business_type").notNull(),
  templateName: text("template_name").notNull(), // e.g., "Standard Physical Business", "SaaS Company"
  description: text("description"),
  isDefault: boolean("is_default").default(false),
  // Chart of accounts template as JSON
  accountsTemplate: jsonb("accounts_template").notNull(), // Array of account objects
  // Metadata
  version: text("version").default("1.0"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  businessTypeIdx: index("idx_business_type_templates_type").on(table.businessType),
  defaultTemplateIdx: index("idx_business_type_templates_default").on(table.isDefault),
}));

// -- ACCOUNTING --

export const accountTypeEnum = pgEnum("account_type", [
  "asset",
  "liability",
  "equity",
  "revenue",
  "expense",
]);

export const normalBalanceEnum = pgEnum("normal_balance", ["debit", "credit"]);

// Enhanced classification enums for professional accounting standards
export const accountClassificationEnum = pgEnum("account_classification", [
  "current",
  "non_current",
  "operating",
  "non_operating",
  "n_a", // Not applicable (for equity accounts, etc.)
]);

export const financialStatementSectionEnum = pgEnum("financial_statement_section", [
  "current_assets",
  "non_current_assets", 
  "current_liabilities",
  "non_current_liabilities",
  "equity",
  "operating_revenue",
  "other_revenue",
  "cost_of_goods_sold",
  "operating_expenses",
  "other_expenses",
]);

export const cashFlowCategoryEnum = pgEnum("cash_flow_category", [
  "operating",
  "investing", 
  "financing",
  "n_a", // Not applicable
]);

export const accountGroupEnum = pgEnum("account_group", [
  // Asset Groups
  "cash_and_equivalents",
  "accounts_receivable",
  "inventory",
  "prepaid_expenses",
  "short_term_investments",
  "property_plant_equipment",
  "accumulated_depreciation",
  "intangible_assets",
  "long_term_investments",
  "other_assets",
  // Liability Groups
  "accounts_payable",
  "accrued_liabilities",
  "short_term_debt",
  "unearned_revenue",
  "long_term_debt",
  "other_liabilities",
  // Equity Groups
  "capital_stock",
  "retained_earnings",
  "other_equity",
  // Revenue Groups
  "product_sales",
  "service_revenue",
  "other_revenue",
  // Expense Groups
  "cost_of_sales",
  "selling_expenses",
  "administrative_expenses",
  "depreciation_amortization",
  "interest_expense",
  "other_expenses",
  // General
  "other",
]);

/**
 * The Chart of Accounts for an organization.
 * Enhanced with professional accounting standards and regulatory compliance support.
 */
export const accounts = pgTable("accounts", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  name: text("name").notNull(),
  accountNumber: varchar("account_number", { length: 20 }),
  type: accountTypeEnum("type").notNull(),
  normalBalance: normalBalanceEnum("normal_balance").notNull(),
  
  // Hierarchical structure
  isHeader: boolean("is_header").default(false),
  parentId: uuid("parent_id").references((): any => accounts.id, {
    onDelete: "set null",
  }),
  level: integer("level").default(0), // 0=main category, 1=subcategory, 2=account group, 3=specific account
  
  // Professional accounting classifications
  classification: accountClassificationEnum("classification").default("n_a"),
  financialStatementSection: financialStatementSectionEnum("financial_statement_section"),
  accountGroup: accountGroupEnum("account_group").default("other"),
  cashFlowCategory: cashFlowCategoryEnum("cash_flow_category").default("n_a"),
  
  // Display and ordering
  displayOrder: integer("display_order").default(0),
  subcategory: text("subcategory"), // e.g., "Current Assets", "Property Plant Equipment"
  
  // Compliance and regulatory
  gaapCategory: text("gaap_category"), // GAAP-specific categorization
  ifrsCategory: text("ifrs_category"), // IFRS-specific categorization
  
  // Status and metadata
  isActive: boolean("is_active").default(true),
  description: text("description"),
  notes: text("notes"), // Internal notes for accountants
  
  // Timestamps
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  unique_org_account_number: unique("unique_org_account_number_idx").on(
    table.organizationId,
    table.accountNumber
  ),
  rlsPolicy: pgPolicy('accounts_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

/**
 * The individual debit/credit entries for a given transaction.
 * This table enforces the double-entry bookkeeping system.
 */
export const journalEntries = pgTable("journal_entries", {
  id: uuid("id").defaultRandom().primaryKey(),
  transactionId: uuid("transaction_id")
    .references(() => transactions.id, { onDelete: "cascade" })
    .notNull(),
  accountId: uuid("account_id")
    .references(() => accounts.id, { onDelete: "restrict" })
    .notNull(),
  debitAmount: decimal("debit_amount", { precision: 12, scale: 2 })
    .default("0.00")
    .notNull(),
  creditAmount: decimal("credit_amount", { precision: 12, scale: 2 })
    .default("0.00")
    .notNull(),
  description: text("description"),
}, (table) => ({
  rlsPolicy: pgPolicy('journal_entries_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM transactions t JOIN organization_members om ON t.organization_id = om.organization_id WHERE t.id = ${table.transactionId} AND om.user_id = auth.uid())`
  })
}));

// -- BUDGETS --

export const budgetStatusEnum = pgEnum("budget_status", [
  "draft",
  "active", 
  "closed",
  "archived",
]);

/**
 * Budget periods for organizations. Allows multiple budget periods (annual, quarterly, etc.)
 */
export const budgetPeriods = pgTable("budget_periods", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  name: text("name").notNull(), // e.g., "2024 Annual Budget", "Q1 2024"
  description: text("description"),
  startDate: timestamp("start_date", { withTimezone: true }).notNull(),
  endDate: timestamp("end_date", { withTimezone: true }).notNull(),
  status: budgetStatusEnum("status").default("draft"),
  isDefault: boolean("is_default").default(false), // Only one default per organization
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('budget_periods_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

/**
 * Individual budget line items linking budget periods to accounts with budgeted amounts
 */
export const budgetLineItems = pgTable("budget_line_items", {
  id: uuid("id").defaultRandom().primaryKey(),
  budgetPeriodId: uuid("budget_period_id")
    .references(() => budgetPeriods.id, { onDelete: "cascade" })
    .notNull(),
  accountId: uuid("account_id")
    .references(() => accounts.id, { onDelete: "restrict" })
    .notNull(),
  budgetedAmount: decimal("budgeted_amount", { precision: 12, scale: 2 })
    .notNull(),
  notes: text("notes"), // Optional notes for this budget line item
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // Unique constraint to prevent duplicate accounts per budget period
  unique_budget_account: unique("unique_budget_account_idx").on(table.budgetPeriodId, table.accountId),
  rlsPolicy: pgPolicy('budget_line_items_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM budget_periods bp JOIN organization_members om ON bp.organization_id = om.organization_id WHERE bp.id = ${table.budgetPeriodId} AND om.user_id = auth.uid())`
  })
}));

/**
 * Optional budget categories for grouping and organizing budget line items
 */
export const budgetCategories = pgTable("budget_categories", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  name: text("name").notNull(), // e.g., "Operating Expenses", "Capital Expenditures"
  description: text("description"),
  color: text("color"), // Hex color for UI representation
  displayOrder: integer("display_order").default(0),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('budget_categories_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

// -- BILLS --

export const billStatusEnum = pgEnum("bill_status", [
  "draft",
  "submitted",
  "approved",
  "paid",
  "void",
]);

/**
 * Represents bills received from vendors.
 */
export const bills = pgTable("bills", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  vendorId: uuid("vendor_id")
    .references(() => vendors.id, { onDelete: "restrict" })
    .notNull(),
  transactionId: uuid("transaction_id").references(() => transactions.id, {
    onDelete: "set null",
  }),
  status: billStatusEnum("status").default("draft"),
  billNumber: varchar("bill_number", { length: 50 }).notNull(),
  date: timestamp("date", { withTimezone: true }).defaultNow().notNull(),
  dueDate: timestamp("due_date", { withTimezone: true }),
  subtotal: decimal("subtotal", { precision: 12, scale: 2 }).notNull(),
  tax: decimal("tax", { precision: 12, scale: 2 }).default("0.00"),
  total: decimal("total", { precision: 12, scale: 2 }).notNull(),
  notes: text("notes"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('bills_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

/**
 * Line items on a bill.
 */
export const billItems = pgTable("bill_items", {
  id: uuid("id").defaultRandom().primaryKey(),
  billId: uuid("bill_id")
    .references(() => bills.id, { onDelete: "cascade" })
    .notNull(),
  productId: uuid("product_id").references(() => products.id, {
    onDelete: "set null",
  }),
  description: text("description").notNull(),
  quantity: integer("quantity").notNull(),
  unitPrice: decimal("unit_price", { precision: 12, scale: 2 }).notNull(),
  total: decimal("total", { precision: 12, scale: 2 }).notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('bill_items_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM bills b JOIN organization_members om ON b.organization_id = om.organization_id WHERE b.id = ${table.billId} AND om.user_id = auth.uid())`
  })
}));

// -- CLIENTS --

/**
   * Customers of the organization.
   */
export const clients = pgTable("clients", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  name: text("name").notNull(),
  email: text("email"),
  phone: text("phone"),
  address: jsonb("address"), // Note for migration: Combine address, city, zip_code, and country fields from the old schema into this JSON object.
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('clients_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

// -- VENDORS --
  
  /**
   * Represents vendors or suppliers for the organization.
   */
  export const vendors = pgTable("vendors", {
    id: uuid("id").defaultRandom().primaryKey(),
    organizationId: uuid("organization_id")
      .references(() => organizations.id, { onDelete: "cascade" })
      .notNull(),
    name: text("name").notNull(),
    email: text("email"),
    phone: text("phone"),
    address: jsonb("address"), // Store address as a structured object
    category: text("category"),
    taxId: varchar("tax_id", { length: 50 }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
    rlsPolicy: pgPolicy('vendors_rls_policty', {
      for: 'all',
      to: authenticatedRole,
      using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
    })
  }));

// -- TRANSACTIONS --

  export const transactionStatusEnum = pgEnum("transaction_status", [
    "draft",
    "posted",
    "voided",
  ]);
  export const transactionTypeEnum = pgEnum("transaction_type", [
    "invoice",
    "bill",
    "simple_income",
    "simple_expense",
    "journal_entry",
  ]);
  
  /**
   * A master record for any financial event (e.g., an invoice, a bill).
   * This contains the high-level details, and the double-entry accounting
   * is handled by the associated journal entries.
   */
  export const transactions = pgTable("transactions", {
    id: uuid("id").defaultRandom().primaryKey(),
    organizationId: uuid("organization_id")
      .references(() => organizations.id, { onDelete: "cascade" })
      .notNull(),
    date: timestamp("date", { withTimezone: true }).notNull(),
    description: text("description").notNull(),
    totalAmount: decimal("total_amount", { precision: 12, scale: 2 }).notNull(),
    status: transactionStatusEnum("status").default("draft"),
    type: transactionTypeEnum("type").notNull(),
    referenceNumber: varchar("reference_number", { length: 50 }),
    clientId: uuid("client_id").references(() => clients.id, {
      onDelete: "set null",
    }),
    vendorId: uuid("vendor_id").references(() => vendors.id, {
      onDelete: "set null",
    }),
    projectId: uuid("project_id").references(() => projects.id, {
      onDelete: "set null",
    }),
    postedAt: timestamp("posted_at", { withTimezone: true }),
    voidedAt: timestamp("voided_at", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  }, (table) => ({
    rlsPolicy: pgPolicy('transactions_rls_policty', {
      for: 'all',
      to: authenticatedRole,
      using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
    })
  }));

// -- INVENTORY --

export const inventoryMovementTypeEnum = pgEnum("inventory_movement_type", [
  "purchase",
  "sale",
  "adjustment",
]);

/**
 * Tracks every change in inventory for a product.
 * This table is the source of truth for inventory levels and valuation.
 */
export const inventoryMovements = pgTable("inventory_movements", {
  id: uuid("id").defaultRandom().primaryKey(),
  productId: uuid("product_id")
    .references(() => products.id, { onDelete: "cascade" })
    .notNull(),
  type: inventoryMovementTypeEnum("type").notNull(),
  quantity: integer("quantity").notNull(), // Can be negative for sales/decreases
  unitCost: decimal("unit_cost", { precision: 12, scale: 2 }).notNull(),
  date: timestamp("date", { withTimezone: true }).defaultNow().notNull(),
  notes: text("notes"),
  referenceId: text("reference_id"), // e.g., invoiceId, billId
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('inventory_movements_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM products p JOIN organization_members om ON p.organization_id = om.organization_id WHERE p.id = ${table.productId} AND om.user_id = auth.uid())`
  })
}));

// -- INVOICES --
export const invoiceStatusEnum = pgEnum("invoice_status", [
  "draft",
  "sent",
  "paid",
  "overdue",
  "void",
]);

/**
 * Invoices issued to clients.
 * Each invoice is linked to a financial transaction.
 */
export const invoices = pgTable("invoices", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  clientId: uuid("client_id")
    .references(() => clients.id, { onDelete: "restrict" })
    .notNull(),
  transactionId: uuid("transaction_id").references(() => transactions.id, {
    onDelete: "set null",
  }),
  status: invoiceStatusEnum("status").default("draft"),
  invoiceNumber: varchar("invoice_number", { length: 50 }).notNull(),
  date: timestamp("date", { withTimezone: true }).defaultNow().notNull(),
  dueDate: timestamp("due_date", { withTimezone: true }),
  subtotal: decimal("subtotal", { precision: 12, scale: 2 }).notNull(),
  tax: decimal("tax", { precision: 12, scale: 2 }).default("0.00"),
  total: decimal("total", { precision: 12, scale: 2 }).notNull(),
  notes: text("notes"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('invoices_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

/**
 * Line items on an invoice.
 */
export const invoiceItems = pgTable("invoice_items", {
  id: uuid("id").defaultRandom().primaryKey(),
  invoiceId: uuid("invoice_id")
    .references(() => invoices.id, { onDelete: "cascade" })
    .notNull(),
  productId: uuid("product_id").references(() => products.id, {
    onDelete: "set null",
  }),
  description: text("description").notNull(),
  quantity: integer("quantity").notNull(),
  unitPrice: decimal("unit_price", { precision: 12, scale: 2 }).notNull(),
  total: decimal("total", { precision: 12, scale: 2 }).notNull(),
}, (table) => ({
  rlsPolicy: pgPolicy('invoice_items_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM invoices i JOIN organization_members om ON i.organization_id = om.organization_id WHERE i.id = ${table.invoiceId} AND om.user_id = auth.uid())`
  })
}));

// -- PRODUCTS --
export const productTypeEnum = pgEnum("product_type", [
  "service",           // Labor-based services
  "digital_service",   // Software, digital products
  "physical_product",  // Inventory items
  "digital_product",   // Downloads, licenses
  "subscription",      // Recurring services
  "bundle",           // Package deals
]);

export const productCategoryEnum = pgEnum("product_category", [
  "consulting",
  "software_services", 
  "physical_goods",
  "digital_products",
  "subscriptions",
  "training",
  "maintenance",
  "other"
]);

/**
 * Product categories for organizing products by business type
 */
export const productCategories = pgTable("product_categories", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  name: text("name").notNull(),
  description: text("description"),
  defaultType: productTypeEnum("default_type").notNull(),
  defaultRevenueAccountId: uuid("default_revenue_account_id"),
  defaultCogsAccountId: uuid("default_cogs_account_id"),
  isSystemCategory: boolean("is_system_category").default(false),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('product_categories_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

/**
 * Products or services offered by the organization.
 * Enhanced with mandatory account mappings and cost tracking.
 */
export const products = pgTable("products", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  categoryId: uuid("category_id").references(() => productCategories.id),
  
  // Basic Information
  name: text("name").notNull(),
  description: text("description"),
  sku: text("sku"),
  type: productTypeEnum("type").default("service").notNull(),
  category: productCategoryEnum("category").default("other").notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  
  // Enhanced Pricing and Cost Structure
  price: decimal("price", { precision: 12, scale: 2 }).notNull(),
  costBasis: decimal("cost_basis", { precision: 12, scale: 2 }), // For inventory/materials
  laborCostPerHour: decimal("labor_cost_per_hour", { precision: 12, scale: 2 }), // For services
  overheadRate: decimal("overhead_rate", { precision: 12, scale: 2 }), // Overhead percentage
  
  // Mandatory Account Mappings (conditional based on type)
  revenueAccountId: uuid("revenue_account_id").notNull(), // Always required
  cogsAccountId: uuid("cogs_account_id"), // Required for inventory/non-inventory
  inventoryAccountId: uuid("inventory_account_id"), // Required for inventory only
  
  // Profitability Tracking
  totalSold: integer("total_sold").default(0).notNull(),
  totalRevenue: decimal("total_revenue", { precision: 12, scale: 2 }).default("0.00").notNull(),
  totalCosts: decimal("total_costs", { precision: 12, scale: 2 }).default("0.00").notNull(),
  lastSaleDate: timestamp("last_sale_date", { withTimezone: true }),
  
  // Business Intelligence
  businessType: text("business_type"), // consulting, retail, saas, etc.
  marginTarget: decimal("margin_target", { precision: 5, scale: 2 }), // Target profit margin %
  
  // Timestamps
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('products_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

// -- PROJECTS --
export const projectStatusEnum = pgEnum("project_status", [
  "not_started",
  "in_progress",
  "completed",
  "on_hold",
  "canceled",
]);

/**
 * Projects are used to group transactions and track profitability for specific jobs.
 */
export const projects = pgTable("projects", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  clientId: uuid("client_id").references(() => clients.id, {
    onDelete: "set null",
  }),
  name: text("name").notNull(),
  description: text("description"),
  status: projectStatusEnum("status").default("not_started").notNull(),
  startDate: timestamp("start_date", { withTimezone: true }),
  endDate: timestamp("end_date", { withTimezone: true }),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('projects_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

// -- RELATIONS --
// -- CORE RELATIONS --

export const userRelations = relations(user, ({ many }) => ({
  sessions: many(session),
  accounts: many(account),
  organizationMembers: many(organizationMembers),
  ownedOrganizations: many(organizations),
  onboardingProgress: many(onboardingProgress),
}));

export const sessionRelations = relations(session, ({ one }) => ({
  user: one(user, {
    fields: [session.userId],
    references: [user.id],
  }),
}));

export const accountRelations = relations(account, ({ one }) => ({
  user: one(user, {
    fields: [account.userId],
    references: [user.id],
  }),
}));

export const organizationsRelations = relations(
  organizations,
  ({ one, many }) => ({
    owner: one(user, {
      fields: [organizations.ownerId],
      references: [user.id],
    }),
    createdBy: one(user, {
      fields: [organizations.createdBy],
      references: [user.id],
    }),
    members: many(organizationMembers),
    accounts: many(accounts),
    transactions: many(transactions),
    clients: many(clients),
    products: many(products),
    invoices: many(invoices),
    vendors: many(vendors),
    bills: many(bills),
    inventoryMovements: many(inventoryMovements),
    projects: many(projects),
    budgetPeriods: many(budgetPeriods),
    budgetCategories: many(budgetCategories),
    organizationSessions: many(organizationSessions),
  })
);

export const organizationMembersRelations = relations(
  organizationMembers,
  ({ one }) => ({
    user: one(user, {
      fields: [organizationMembers.userId],
      references: [user.id],
    }),
    organization: one(organizations, {
      fields: [organizationMembers.organizationId],
      references: [organizations.id],
    }),
  })
);

// -- ONBOARDING RELATIONS --

export const onboardingProgressRelations = relations(
  onboardingProgress,
  ({ one }) => ({
    user: one(user, {
      fields: [onboardingProgress.userId],
      references: [user.id],
    }),
  })
);

export const businessTypeTemplatesRelations = relations(
  businessTypeTemplates,
  ({ many }) => ({
    // No direct relations but used by organizations via businessType
  })
);

// -- ACCOUNTING RELATIONS --

export const accountsRelations = relations(accounts, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [accounts.organizationId],
    references: [organizations.id],
  }),
  parent: one(accounts, {
    fields: [accounts.parentId],
    references: [accounts.id],
    relationName: "account_hierarchy",
  }),
  children: many(accounts, {
    relationName: "account_hierarchy",
  }),
  journalEntries: many(journalEntries),
  budgetLineItems: many(budgetLineItems),
}));

export const journalEntriesRelations = relations(
  journalEntries,
  ({ one }) => ({
    transaction: one(transactions, {
      fields: [journalEntries.transactionId],
      references: [transactions.id],
    }),
    account: one(accounts, {
      fields: [journalEntries.accountId],
      references: [accounts.id],
    }),
  })
);

// -- TRANSACTION RELATIONS --

export const transactionsRelations = relations(
  transactions,
  ({ one, many }) => ({
    organization: one(organizations, {
      fields: [transactions.organizationId],
      references: [organizations.id],
    }),
    journalEntries: many(journalEntries),
    invoice: one(invoices),
    bill: one(bills),
    client: one(clients, {
      fields: [transactions.clientId],
      references: [clients.id],
    }),
    vendor: one(vendors, {
      fields: [transactions.vendorId],
      references: [vendors.id],
    }),
  })
);

// -- SALES RELATIONS --

export const clientsRelations = relations(clients, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [clients.organizationId],
    references: [organizations.id],
  }),
  invoices: many(invoices),
  transactions: many(transactions),
  projects: many(projects),
}));

export const productCategoriesRelations = relations(productCategories, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [productCategories.organizationId],
    references: [organizations.id],
  }),
  products: many(products),
  defaultRevenueAccount: one(accounts, {
    fields: [productCategories.defaultRevenueAccountId],
    references: [accounts.id],
  }),
  defaultCogsAccount: one(accounts, {
    fields: [productCategories.defaultCogsAccountId],
    references: [accounts.id],
  }),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [products.organizationId],
    references: [organizations.id],
  }),
  category: one(productCategories, {
    fields: [products.categoryId],
    references: [productCategories.id],
  }),
  revenueAccount: one(accounts, {
    fields: [products.revenueAccountId],
    references: [accounts.id],
  }),
  cogsAccount: one(accounts, {
    fields: [products.cogsAccountId],
    references: [accounts.id],
  }),
  inventoryAccount: one(accounts, {
    fields: [products.inventoryAccountId],
    references: [accounts.id],
  }),
  invoiceItems: many(invoiceItems),
  billItems: many(billItems),
  inventoryMovements: many(inventoryMovements),
}));

export const invoicesRelations = relations(invoices, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [invoices.organizationId],
    references: [organizations.id],
  }),
  client: one(clients, {
    fields: [invoices.clientId],
    references: [clients.id],
  }),
  transaction: one(transactions, {
    fields: [invoices.transactionId],
    references: [transactions.id],
  }),
  items: many(invoiceItems),
}));

export const invoiceItemsRelations = relations(
  invoiceItems,
  ({ one }) => ({
    invoice: one(invoices, {
      fields: [invoiceItems.invoiceId],
      references: [invoices.id],
    }),
    product: one(products, {
      fields: [invoiceItems.productId],
      references: [products.id],
    }),
  })
);

// -- VENDOR RELATIONS --

export const vendorsRelations = relations(vendors, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [vendors.organizationId],
    references: [organizations.id],
  }),
  bills: many(bills),
  transactions: many(transactions),
}));

export const billsRelations = relations(bills, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [bills.organizationId],
    references: [organizations.id],
  }),
  vendor: one(vendors, {
    fields: [bills.vendorId],
    references: [vendors.id],
  }),
  transaction: one(transactions, {
    fields: [bills.transactionId],
    references: [transactions.id],
  }),
  items: many(billItems),
}));

export const billItemsRelations = relations(billItems, ({ one }) => ({
  bill: one(bills, {
    fields: [billItems.billId],
    references: [bills.id],
  }),
  product: one(products, {
    fields: [billItems.productId],
    references: [products.id],
  }),
}));

// -- PROJECT RELATIONS --

export const projectsRelations = relations(projects, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [projects.organizationId],
    references: [organizations.id],
  }),
  client: one(clients, {
    fields: [projects.clientId],
    references: [clients.id],
  }),
  transactions: many(transactions),
}));

// -- BUDGET RELATIONS --

export const budgetPeriodsRelations = relations(budgetPeriods, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [budgetPeriods.organizationId],
    references: [organizations.id],
  }),
  lineItems: many(budgetLineItems),
}));

export const budgetLineItemsRelations = relations(budgetLineItems, ({ one }) => ({
  budgetPeriod: one(budgetPeriods, {
    fields: [budgetLineItems.budgetPeriodId],
    references: [budgetPeriods.id],
  }),
  account: one(accounts, {
    fields: [budgetLineItems.accountId],
    references: [accounts.id],
  }),
}));

export const budgetCategoriesRelations = relations(budgetCategories, ({ one }) => ({
  organization: one(organizations, {
    fields: [budgetCategories.organizationId],
    references: [organizations.id],
  }),
}));

// -- INVENTORY RELATIONS --

export const inventoryMovementsRelations = relations(
  inventoryMovements,
  ({ one }) => ({
    product: one(products, {
      fields: [inventoryMovements.productId],
      references: [products.id],
    }),
  })
);

export const schema = {
  user,
  session,
  account,
  verification,
  organizations,
  organizationMembers,
  accounts,
  journalEntries,
  transactions,
  clients,
  products,
  invoices,
  budgetPeriods,
  budgetLineItems,
  budgetCategories,
};

// ================================
// PHASE 0.5: ENHANCED ORGANIZATION SYSTEM
// ================================

/**
 * Enhanced organization settings for complex configurations
 * Supports versioned settings with audit trail
 */
export const organizationSettings = pgTable("organization_settings", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  category: varchar("category", { length: 50 }).notNull(), // 'security', 'billing', 'features', 'integrations'
  key: varchar("key", { length: 100 }).notNull(),
  value: jsonb("value").notNull(),
  description: text("description"), // Human-readable description
  isSystem: boolean("is_system").default(false), // System vs user-defined settings
  updatedBy: text("updated_by").references(() => user.id, { onDelete: "set null" }),
  validFrom: timestamp("valid_from", { withTimezone: true }).defaultNow(),
  validTo: timestamp("valid_to", { withTimezone: true }),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  unique_org_category_key: unique("unique_org_category_key_idx").on(
    table.organizationId, 
    table.category, 
    table.key
  ),
  rlsPolicy: pgPolicy('organization_settings_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));
/**
 * Comprehensive audit logging for all organization activities
 * Supports compliance requirements and security monitoring
 */
export const auditLogs = pgTable("audit_logs", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  userId: text("user_id").references(() => user.id, { onDelete: "set null" }),
  sessionId: text("session_id"), // Link to session for tracking
  
  // Action details
  action: varchar("action", { length: 100 }).notNull(), // 'create', 'update', 'delete', 'login', 'invite', etc.
  resourceType: varchar("resource_type", { length: 50 }).notNull(), // 'transaction', 'user', 'organization', etc.
  resourceId: varchar("resource_id", { length: 100 }), // ID of the affected resource
  
  // Change tracking
  oldValues: jsonb("old_values"), // Previous state
  newValues: jsonb("new_values"), // New state
  changedFields: jsonb("changed_fields"), // Array of field names that changed
  
  // Context
  description: text("description"), // Human-readable description
  metadata: jsonb("metadata"), // Additional context (e.g., bulk operation info)
  
  // Security context
  ipAddress: varchar("ip_address", { length: 45 }), // IPv4 or IPv6
  userAgent: text("user_agent"),
  location: jsonb("location"), // Geolocation data if available
  
  // Risk assessment
  riskLevel: varchar("risk_level", { length: 20 }).default("low"), // 'low', 'medium', 'high', 'critical'
  flags: jsonb("flags"), // Security flags or alerts
  
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // Indexes for performance and common queries
  idx_audit_org_date: index("idx_audit_logs_org_date").on(table.organizationId, table.createdAt),
  idx_audit_user_date: index("idx_audit_logs_user_date").on(table.userId, table.createdAt),
  idx_audit_resource: index("idx_audit_logs_resource").on(table.resourceType, table.resourceId),
  idx_audit_action: index("idx_audit_logs_action").on(table.action, table.createdAt),
  rlsPolicy: pgPolicy('audit_logs_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

/**
 * Organization access sessions for detailed security monitoring
 * Tracks all organization access patterns
 */
export const organizationSessions = pgTable("organization_sessions", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  userId: text("user_id")
    .references(() => user.id, { onDelete: "cascade" })
    .notNull(),
  sessionId: text("session_id").notNull(),
  
  // Session details
  startedAt: timestamp("started_at", { withTimezone: true }).defaultNow(),
  lastActivity: timestamp("last_activity", { withTimezone: true }).defaultNow(),
  endedAt: timestamp("ended_at", { withTimezone: true }),
  
  // Security context
  ipAddress: varchar("ip_address", { length: 45 }),
  userAgent: text("user_agent"),
  location: jsonb("location"),
  
  // Activity tracking
  pageViews: integer("page_views").default(0),
  actionsPerformed: integer("actions_performed").default(0),
  
  // Security flags
  isActive: boolean("is_active").default(true),
  isSuspicious: boolean("is_suspicious").default(false),
  riskScore: integer("risk_score").default(0), // 0-100 risk assessment
  
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('organization_sessions_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

export const organizationSessionsRelations = relations(
  organizationSessions,
  ({ one }) => ({
    organization: one(organizations, {
      fields: [organizationSessions.organizationId],
      references: [organizations.id],
    }),
    user: one(user, {
      fields: [organizationSessions.userId],
      references: [user.id],
    }),
  })
);

// ================================
// ENHANCED ORGANIZATION MANAGEMENT
// ================================

/**
 * Organization lifecycle and status management
 */
export const organizationStatusEnum = pgEnum("organization_status", [
  "active",
  "suspended",
  "deactivated", 
  "deleted",
  "pending_verification",
]);

export const suspensionReasonEnum = pgEnum("suspension_reason", [
  "payment_failed",
  "policy_violation",
  "security_breach",
  "user_requested",
  "compliance_issue",
  "other",
]);

/**
 * Organization compliance and regulatory tracking
 */
export const organizationCompliance = pgTable("organization_compliance", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull()
    .unique(),
  
  // Regulatory compliance
  gdprApplicable: boolean("gdpr_applicable").default(false),
  ccpaApplicable: boolean("ccpa_applicable").default(false),
  soxCompliant: boolean("sox_compliant").default(false),
  hipaaRequired: boolean("hipaa_required").default(false),
  
  // Data handling preferences
  dataRetentionDays: integer("data_retention_days").default(2555), // 7 years default
  allowDataExport: boolean("allow_data_export").default(true),
  requireDataDeletion: boolean("require_data_deletion").default(false),
  
  // Security requirements
  requireMfa: boolean("require_mfa").default(false),
  requireSso: boolean("require_sso").default(false),
  allowedIpRanges: jsonb("allowed_ip_ranges"), // Array of IP ranges
  sessionTimeoutMinutes: integer("session_timeout_minutes").default(480), // 8 hours
  
  // Audit requirements
  auditLogRetentionDays: integer("audit_log_retention_days").default(2555),
  requireAuditApproval: boolean("require_audit_approval").default(false),
  
  lastReviewedAt: timestamp("last_reviewed_at", { withTimezone: true }),
  reviewedBy: text("reviewed_by").references(() => user.id, { onDelete: "set null" }),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  rlsPolicy: pgPolicy('organization_compliance_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM "organization_members" WHERE "organization_members"."organization_id" = ${table.organizationId} AND "organization_members"."user_id" = auth.uid())`
  })
}));

// ================================
// ENHANCED TEAM MANAGEMENT
// ================================

/**
 * Team invitation tokens with enhanced security
 */
export const invitationTokens = pgTable("invitation_tokens", {
  id: uuid("id").defaultRandom().primaryKey(),
  invitationId: uuid("invitation_id")
    .references(() => organizationInvitations.id, { onDelete: "cascade" })
    .notNull()
    .unique(),
  token: varchar("token", { length: 255 }).notNull().unique(),
  hashedToken: varchar("hashed_token", { length: 255 }).notNull(),
  
  // Security measures
  maxUses: integer("max_uses").default(1),
  currentUses: integer("current_uses").default(0),
  ipRestriction: varchar("ip_restriction", { length: 45 }), // Restrict to specific IP
  
  // Tracking
  generatedAt: timestamp("generated_at", { withTimezone: true }).defaultNow(),
  firstUsedAt: timestamp("first_used_at", { withTimezone: true }),
  lastUsedAt: timestamp("last_used_at", { withTimezone: true }),
  
  isRevoked: boolean("is_revoked").default(false),
  revokedAt: timestamp("revoked_at", { withTimezone: true }),
  revokedBy: text("revoked_by").references(() => user.id, { onDelete: "set null" }),
  revokedReason: text("revoked_reason"),
}, (table) => ({
  rlsPolicy: pgPolicy('invitation_tokens_rls_policty', {
    for: 'all',
    to: authenticatedRole,
    using: sql`EXISTS (SELECT 1 FROM organization_invitations oi JOIN organization_members om ON oi.organization_id = om.organization_id WHERE oi.id = ${table.invitationId} AND om.user_id = auth.uid())`
  })
}));

