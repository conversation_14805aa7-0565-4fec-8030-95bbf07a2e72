import { NextRequest, NextResponse } from "next/server";
import { getProductSuggestions } from "@/lib/actions/products/get-product-suggestions";
import { getServerUserContext } from "@/lib/server-auth";

type ProductType = "service" | "digital_service" | "physical_product" | "digital_product" | "subscription" | "bundle";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    await getServerUserContext();

    const { searchParams } = new URL(request.url);
    const productType = searchParams.get("type") as ProductType;
    const productName = searchParams.get("name");

    if (!productType) {
      return NextResponse.json(
        { error: "Product type is required" },
        { status: 400 }
      );
    }

    const suggestions = await getProductSuggestions(
      productType,
      productName || undefined
    );

    return NextResponse.json(suggestions);
  } catch (error) {
    console.error("Error fetching product suggestions:", error);
    return NextResponse.json(
      { error: "Failed to fetch suggestions" },
      { status: 500 }
    );
  }
} 