"use server";

import { and, eq } from "drizzle-orm";
import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { ProductBusinessRules } from "./product.rules";
import { getProductsWithAnalytics } from "./get-products-with-analytics";

export async function getProductOptimizationSuggestions(productId: string) {
  try {
    const allProducts = await getProductsWithAnalytics();
    const product = allProducts.find((p) => p.id === productId);

    if (!product) {
      return { success: false, message: "Product not found.", suggestions: [] };
    }

    const businessRules = new ProductBusinessRules();
    const { suggestions } = businessRules.suggestOptimizations(product);

    return {
      success: true,
      suggestions,
    };
  } catch (error) {
    console.error("Failed to get product optimization suggestions:", error);
    return {
      success: false,
      message: "An error occurred while getting suggestions.",
      suggestions: [],
    };
  }
} 