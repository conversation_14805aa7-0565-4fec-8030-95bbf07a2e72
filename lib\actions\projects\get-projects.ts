"use server";

import { db } from "@/db/drizzle";
import { projects } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";
import { Project } from "@/components/protectedPages/Projects/columns";

export async function getProjects(): Promise<Project[]> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    // This needs to be a join to get the client name
    const projectList = await db.query.projects.findMany({
      where: eq(projects.organizationId, orgId),
      with: {
        client: {
          columns: {
            name: true,
          },
        },
      },
      orderBy: [desc(projects.createdAt)],
    });
    
    return projectList.map(p => ({
      ...p,
      clientName: p.client?.name ?? "N/A",
    }));

  } catch (error) {
    console.error("Failed to fetch projects:", error);
    return [];
  }
} 