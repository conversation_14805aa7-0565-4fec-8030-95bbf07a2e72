import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Bell, Calendar, DollarSign } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface Payment {
  id: string;
  clientName: string;
  amount: number;
  dueDate: string;
  status: "pending" | "overdue" | "upcoming";
}

interface PaymentRemindersProps {
  payments?: Payment[];
  isLoading?: boolean;
}

const PaymentReminders = ({
  payments = [
    {
      id: "1",
      clientName: "Acme Corp",
      amount: 2500,
      dueDate: "2024-07-15",
      status: "pending",
    },
    {
      id: "2",
      clientName: "Tech Solutions",
      amount: 1800,
      dueDate: "2024-06-10",
      status: "overdue",
    },
    {
      id: "3",
      clientName: "Design Studio",
      amount: 3000,
      dueDate: "2024-08-01",
      status: "upcoming",
    },
  ] as Payment[],
  isLoading = false,
}: PaymentRemindersProps) => {
  const getStatusClasses = (status: Payment["status"]) => {
    switch (status) {
      case "pending":
        return "border-yellow-500/50 bg-yellow-500/10 text-yellow-500";
      case "overdue":
        return "border-red-500/50 bg-red-500/10 text-red-500";
      case "upcoming":
        return "border-blue-500/50 bg-blue-500/10 text-blue-500";
      default:
        return "border-gray-500/50 bg-gray-500/10 text-gray-500";
    }
  };

  return (
    <Card className="w-full h-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Bell className="w-5 h-5 text-muted-foreground" />
            Payment Reminders
          </CardTitle>
          <Badge variant="secondary">
            {payments.length}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[340px] pr-4">
          <div className="space-y-3">
            {isLoading
              ? Array(3)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="p-3 rounded-lg border space-y-2">
                      <Skeleton className="h-5 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  ))
              : payments.map((payment) => (
                  <div
                    key={payment.id}
                    className="p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">
                        {payment.clientName}
                      </h4>
                      <Badge
                        variant="outline"
                        className={cn("capitalize", getStatusClasses(payment.status))}
                      >
                        {payment.status}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-4 h-4" />
                        {payment.amount.toLocaleString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {new Date(payment.dueDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default PaymentReminders; 