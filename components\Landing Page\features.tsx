'use client'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { ChartBarIncreasingIcon, Database, Fingerprint, IdCard } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { BorderBeam } from '@/components/magicui/border-beam'

export default function Features() {
    type ImageKey = 'item-1' | 'item-2' | 'item-3' | 'item-4' | 'item-5' | 'item-6'
    const [activeItem, setActiveItem] = useState<ImageKey>('item-1')

    const images = {
        'item-1': {
            image: 'https://placehold.co/400x300/png',
            alt: 'Database visualization',
        },
        'item-2': {
            image: 'https://placehold.co/400x300/png',
            alt: 'Security authentication',
        },
        'item-3': {
            image: 'https://placehold.co/400x300/png',
            alt: 'Identity management',
        },
        'item-4': {
            image: 'https://placehold.co/400x300/png',
            alt: 'Analytics dashboard',
        },
        'item-5': {
            image: 'https://placehold.co/400x300/png',
            alt: 'Placeholder',
        },
        'item-6': {
            image: 'https://placehold.co/400x300/png',
            alt: 'Placeholder',
        },
    }

    return (
        <section className="py-12 md:py-20 lg:py-32" id="features">
            <div className="bg-linear-to-b absolute inset-0 -z-10 sm:inset-6 sm:rounded-b-3xl dark:block dark:to-[color-mix(in_oklab,var(--color-zinc-900)_75%,var(--color-background))]"></div>
            <div className="mx-auto max-w-5xl space-y-8 px-6 md:space-y-16 lg:space-y-20 dark:[--color-border:color-mix(in_oklab,var(--color-white)_10%,transparent)]">
                <div className="relative z-10 mx-auto max-w-2xl space-y-6 text-center">
                    <h2 className="text-balance text-4xl font-semibold lg:text-6xl">NextGenBusiness</h2>
                    <h5>Simplify Your Finances, Amplify Your Growth</h5>
                    <p>NextGenBusiness automates your accounting, reporting, tracking, giving you a clear real-time view of your business's financial health. Our intuitive platform eliminates the complexity, allowing you to focus on what you do best – growing your business.</p>
                </div>

                <div className="grid gap-12 sm:px-12 md:grid-cols-2 lg:gap-20 lg:px-0">
                    <Accordion
                        type="single"
                        value={activeItem}
                        onValueChange={(value) => setActiveItem(value as ImageKey)}
                        className="w-full">
                        <AccordionItem value="item-1">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <Database className="size-4" />
                                    Automated Accounting
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Save time and reduce errors with automated data entry and accounting. With NextGenBusiness just drag and drop your monthly statement and watch the magic happen as your transactions get imported into the system automatically.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="item-2">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <Fingerprint className="size-4" />
                                    Real-Time Reporting
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Stop Struggling to understand financial reports. Get instant insights into your financial performance with easy-to-understand reports. Business reporting doesn't have to be a pain. Not only do you have a fully customizable dashboard to see insights which are relevant to you, in our reporting section you will find clear and simple reports that will tell you exactly what you need to know about your financial health.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="item-3">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <IdCard className="size-4" />
                                    Inventory & Service Tracking
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Optimize your resources and track performance with precision. Don't you just hate the countless amount of sheets you have for each product or service you provide? We certainly hated them, hence the birth of NextGenBusiness. You'll never have to manage spreadsheets again, as you got all the relevant data in the proftiability tracker.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="item-4">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <ChartBarIncreasingIcon className="size-4" />
                                    Simplified Financial Management
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Take control of your finances, even if you're not an accounting expert. Know your cashflow at a glance and make sound financial decisions.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="item-5">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <ChartBarIncreasingIcon className="size-4" />
                                    PlaceholderTitle
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>PlaceholderText.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="item-6">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <ChartBarIncreasingIcon className="size-4" />
                                    PlaceholderTitle
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>PlaceholderText.</AccordionContent>
                        </AccordionItem>
                    </Accordion>

                    <div className="bg-background relative flex overflow-hidden rounded-3xl border p-2">
                        <div className="w-15 absolute inset-0 right-0 ml-auto border-l bg-[repeating-linear-gradient(-45deg,var(--color-border),var(--color-border)_1px,transparent_1px,transparent_8px)]"></div>
                        <div className="aspect-76/59 bg-background relative w-[calc(3/4*100%+3rem)] rounded-2xl">
                            <AnimatePresence mode="wait">
                                <motion.div
                                    key={`${activeItem}-id`}
                                    initial={{ opacity: 0, y: 6, scale: 0.98 }}
                                    animate={{ opacity: 1, y: 0, scale: 1 }}
                                    exit={{ opacity: 0, y: 6, scale: 0.98 }}
                                    transition={{ duration: 0.2 }}
                                    className="size-full overflow-hidden rounded-2xl border bg-zinc-900 shadow-md">
                                    <Image
                                        src={images[activeItem].image}
                                        className="size-full object-cover object-left-top dark:mix-blend-lighten"
                                        alt={images[activeItem].alt}
                                        width={1207}
                                        height={929}
                                    />
                                </motion.div>
                            </AnimatePresence>
                        </div>
                        <BorderBeam
                            duration={6}
                            size={200}
                            className="from-transparent via-yellow-700 to-transparent dark:via-white/50"
                        />
                    </div>
                </div>
            </div>
        </section>
    )
}
