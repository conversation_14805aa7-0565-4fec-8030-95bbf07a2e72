"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getTeammates } from "@/lib/actions/team/get-teammates.action";
import { updateUserRole } from "@/lib/actions/team/update-user-role.action";
import { toast } from "sonner";
import { RolesDataTable } from "./data-table";
import { getColumns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { getUserPermissions } from "@/lib/permissions";

export function RolesClient() {
    const queryClient = useQueryClient();

    const { data: teammates = [], isLoading: isLoadingTeammates } = useQuery({
        queryKey: ["teammates"],
        queryFn: getTeammates,
    });

    const { data: currentUserPermissions, isLoading: isLoadingPermissions } = useQuery({
        queryKey: ["user-permissions"],
        queryFn: getUserPermissions,
    });

    const { mutate, isPending } = useMutation({
        mutationFn: ({ userId, role }: { userId: string; role: any }) => updateUserRole(userId, role),
        onSuccess: (result) => {
            if (result.success) {
                toast.success(result.message);
                queryClient.invalidateQueries({ queryKey: ["teammates"] });
            } else {
                toast.error(result.message);
            }
        },
        onError: (error) => {
            toast.error(error.message);
        }
    });

    const handleRoleChange = (userId: string, role: string) => {
        mutate({ userId, role });
    };

    const columns = getColumns({ onRoleChange: handleRoleChange, currentUserRole: currentUserPermissions?.role });

    if (isLoadingTeammates || isLoadingPermissions) {
        return <Skeleton className="h-64 w-full" />;
    }

    return <RolesDataTable columns={columns} data={teammates} />;
} 