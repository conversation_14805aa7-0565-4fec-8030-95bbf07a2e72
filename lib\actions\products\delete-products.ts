"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
  data?: any;
}>;

export async function deleteProducts(ids: string[]): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    if (ids.length === 0) {
      return { success: false, message: "No product IDs provided." };
    }

    await db
      .delete(products)
      .where(and(inArray(products.id, ids), eq(products.organizationId, orgId)));

    revalidatePath("/products");
    return { success: true, message: `${ids.length} product(s) deleted.` };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to delete products.",
    };
  }
} 