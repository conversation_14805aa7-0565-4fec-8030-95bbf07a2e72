"use client";

import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Mail, Clock } from 'lucide-react';

export function VerifyEmailClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const verified = searchParams.get("verified");
  const [checkingAuth, setCheckingAuth] = useState(true);

  useEffect(() => {
    if (verified === "true") {
      // After verification, redirect to onboarding after a short delay
      setTimeout(() => {
        router.push("/onboarding");
      }, 2000);
      return;
    }
    setCheckingAuth(false);
  }, [verified, router]);

  if (checkingAuth) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Loading...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (verified === "true") {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          <div className="space-y-2">
            <CardTitle className="text-2xl font-bold text-green-800 dark:text-green-200">
              Email Verified!
            </CardTitle>
            <CardDescription className="text-lg">
              Your account is now active. Redirecting you to signin...
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex items-center justify-center space-x-2 text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span className="text-sm">Loading your account...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show verification screen
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
          <Mail className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="space-y-2">
          <CardTitle className="text-2xl font-bold">
            Verify Your Email
          </CardTitle>
          <CardDescription className="text-lg">
            We need to verify your email address
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 dark:bg-blue-950/30 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start space-x-3">
            <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                Check Your Email
              </h3>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                We've sent you a verification email. Please check your inbox and click the verification link to activate your account.
              </p>
            </div>
          </div>
        </div>
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Didn't receive the email? Check your spam folder or{' '}
            <Button variant="link" className="p-0 h-auto">
              resend verification email
            </Button>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}