import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getAccounts } from "@/lib/actions/accounting/get-accounts";
import { TrendingUp, Shield, AlertTriangle, CheckCircle, BarChart3, <PERSON><PERSON>hart } from "lucide-react";

export function AccountAnalytics() {
  const { data: accounts = [], isLoading } = useQuery({
    queryKey: ["accounts"],
    queryFn: getAccounts,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse h-24">
            <CardContent className="p-4">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Calculate metrics
  const totalAccounts = accounts.length;
  const classifiedAccounts = accounts.filter(acc => acc.financialStatementSection && acc.financialStatementSection !== null).length;
  const accountsWithNumbers = accounts.filter(acc => acc.accountNumber && acc.accountNumber.trim() !== '').length;
  
  const compliancePercentage = totalAccounts > 0 ? Math.round((classifiedAccounts / totalAccounts) * 100) : 0;
  const numberingPercentage = totalAccounts > 0 ? Math.round((accountsWithNumbers / totalAccounts) * 100) : 0;

  // Account type distribution
  const typeDistribution = accounts.reduce((acc, account) => {
    const type = account.type;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const getStatusColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-blue-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusBadge = (percentage: number) => {
    if (percentage >= 90) return { label: 'EXCELLENT', className: 'bg-green-100 text-green-800 border-green-200' };
    if (percentage >= 70) return { label: 'GOOD', className: 'bg-blue-100 text-blue-800 border-blue-200' };
    if (percentage >= 50) return { label: 'FAIR', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' };
    return { label: 'POOR', className: 'bg-red-100 text-red-800 border-red-200' };
  };

  const complianceBadge = getStatusBadge(compliancePercentage);
  const numberingBadge = getStatusBadge(numberingPercentage);

  return (
    <div className="space-y-3">
      {/* Key Metrics Row - Much More Compact */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
        {/* Total Accounts */}
        <div className="border-l-4 border-l-blue-500 bg-card rounded-md p-3 flex items-center justify-between">
          <div>
            <p className="text-xs text-muted-foreground">Total Accounts</p>
            <p className="text-xl font-bold text-blue-600">{totalAccounts}</p>
          </div>
          <BarChart3 className="h-6 w-6 text-blue-500" />
        </div>

        {/* GAAP Compliance */}
        <div className="border-l-4 border-l-green-500 bg-card rounded-md p-3 flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-1 mb-1">
              <p className="text-xs text-muted-foreground">GAAP Compliance</p>
              {compliancePercentage < 70 && <AlertTriangle className="h-3 w-3 text-amber-500" />}
            </div>
            <div className="flex items-center gap-2">
              <p className={`text-xl font-bold ${getStatusColor(compliancePercentage)}`}>
                {compliancePercentage}%
              </p>
              <Badge variant="outline" className={`text-xs px-1 py-0 ${complianceBadge.className}`}>
                {complianceBadge.label}
              </Badge>
            </div>
          </div>
          <Shield className="h-6 w-6 text-green-500" />
        </div>

        {/* Account Numbering */}
        <div className="border-l-4 border-l-purple-500 bg-card rounded-md p-3 flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-1 mb-1">
              <p className="text-xs text-muted-foreground">Account Numbering</p>
              {numberingPercentage < 70 && <AlertTriangle className="h-3 w-3 text-amber-500" />}
            </div>
            <div className="flex items-center gap-2">
              <p className={`text-xl font-bold ${getStatusColor(numberingPercentage)}`}>
                {numberingPercentage}%
              </p>
              <Badge variant="outline" className={`text-xs px-1 py-0 ${numberingBadge.className}`}>
                {numberingBadge.label}
              </Badge>
            </div>
          </div>
          <PieChart className="h-6 w-6 text-purple-500" />
        </div>
      </div>

      {/* Account Type Distribution - Much More Compact */}
      <div className="bg-card rounded-md p-3">
        <div className="flex items-center gap-2 mb-2">
          <BarChart3 className="h-4 w-4" />
          <h4 className="text-sm font-semibold">Account Type Distribution</h4>
        </div>
        <div className="grid grid-cols-5 gap-2">
          {[
            { type: 'asset', label: 'Assets', color: 'bg-blue-100 text-blue-800', icon: '🏦' },
            { type: 'liability', label: 'Liabilities', color: 'bg-red-100 text-red-800', icon: '💳' },
            { type: 'equity', label: 'Equity', color: 'bg-purple-100 text-purple-800', icon: '👑' },
            { type: 'revenue', label: 'Revenue', color: 'bg-green-100 text-green-800', icon: '💰' },
            { type: 'expense', label: 'Expenses', color: 'bg-orange-100 text-orange-800', icon: '💸' },
          ].map(({ type, label, color, icon }) => (
            <div key={type} className="text-center">
              <div className="text-lg mb-1">{icon}</div>
              <div className="text-sm font-bold text-gray-900">{typeDistribution[type] || 0}</div>
              <Badge variant="outline" className={`text-xs px-1 py-0 ${color}`}>
                {label}
              </Badge>
            </div>
          ))}
        </div>
      </div>

      {/* Professional Insights - Compact */}
      {(compliancePercentage < 90 || numberingPercentage < 90) && (
        <div className="border-l-4 border-l-amber-500 bg-amber-50/50 rounded-md p-3">
          <div className="flex items-start gap-2">
            <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-amber-900 mb-1">Professional Recommendations</h4>
              <div className="space-y-1 text-xs text-amber-800">
                {compliancePercentage < 90 && (
                  <p>• Categorize {totalAccounts - classifiedAccounts} uncategorized accounts for better GAAP compliance</p>
                )}
                {numberingPercentage < 90 && (
                  <p>• Assign numbers to {totalAccounts - accountsWithNumbers} accounts for improved organization</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {compliancePercentage >= 90 && numberingPercentage >= 90 && (
        <div className="border-l-4 border-l-green-500 bg-green-50/50 rounded-md p-3">
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-green-900">Excellent Compliance!</h4>
              <p className="text-xs text-green-800 mt-1">
                Your chart of accounts meets professional GAAP standards.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 