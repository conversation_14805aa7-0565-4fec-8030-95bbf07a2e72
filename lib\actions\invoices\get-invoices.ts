"use server";

import { db } from "@/db/drizzle";
import { invoices } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";
import { Invoice } from "@/components/protectedPages/Invoices/columns";

export async function getInvoices(): Promise<Invoice[]> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const invoiceList = await db.query.invoices.findMany({
      where: eq(invoices.organizationId, orgId),
      with: {
        client: {
          columns: {
            name: true,
          },
        },
      },
      orderBy: [desc(invoices.date)],
    });

    return invoiceList.map((i) => ({
      ...i,
      clientName: i.client?.name ?? "N/A",
      total: i.total,
      status: i.status as "draft" | "sent" | "paid" | "overdue" | "void",
      dueDate: i.dueDate ? new Date(i.dueDate) : null,
      date: new Date(i.date),
    }));
  } catch (error) {
    console.error("Failed to fetch invoices:", error);
    return [];
  }
} 