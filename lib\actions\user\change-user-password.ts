"use server";
import { auth } from "@/lib/auth";
import { verifyPassword } from "better-auth/crypto";

export async function changeUserPassword(userId: string, currentPassword: string, newPassword: string) {
  if (!newPassword || newPassword.length < 8) {
    return { error: "New password must be at least 8 characters." };
  }
  try {
    const ctx = await auth.$context;
    // Fetch all accounts for the user, then filter for providerId 'credential'
    const accounts = await ctx.internalAdapter.findAccount(userId);
    const account = Array.isArray(accounts)
      ? accounts.find((a) => a.providerId === "credential")
      : accounts && accounts.providerId === "credential"
        ? accounts
        : null;
    if (!account || !account.password) {
      return { error: "No password account found for this user." };
    }
    // Verify current password
    const valid = await verifyPassword({ password: currentPassword, hash: account.password });
    if (!valid) {
      return { error: "Current password is incorrect." };
    }
    // Hash new password
    const hash = await ctx.password.hash(newPassword);
    // Update password
    await ctx.internalAdapter.updatePassword(userId, hash);
    return { success: true };
  } catch (error) {
    return { error: error instanceof Error ? error.message : String(error) };
  }
} 