import { db } from "@/db/drizzle";
import { auth } from "@/lib/auth";
import { eq } from "drizzle-orm";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import EnhancedTransactionsPageClient from "@/components/protectedPages/Transactions/EnhancedTransactionsPageClient";

// Helper to get user's organizationId (copied from other modules)
async function getUserOrganizationId(userId: string) {
  const { organizationMembers } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: organizationMembers.organizationId })
    .from(organizationMembers)
    .where(eq(organizationMembers.userId, userId));
  return orgs[0]?.organizationId || null;
}

// Placeholder for the client data table component
// import TransactionsPageClient from "@/components/protectedPages/Transactions/TransactionsPageClient";

export default async function TransactionsPage() {
  const session = await auth.api.getSession({ headers: await headers() });
  if (!session?.user?.id) {
    redirect("/signin");
  }
  const organizationId = await getUserOrganizationId(session.user.id);
  if (!organizationId) {
    redirect("/onboarding");
  }
  // Fetch transactions for the current organization
  // const txs = await db
  //   .select()
  //   .from(transactions)
  //   .where(eq(transactions.organizationId, organizationId));

  return (
    <div className="container mx-auto py-10">
      <EnhancedTransactionsPageClient />
    </div>
  );
}
