"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Shield, TrendingUp } from "lucide-react";

interface WelcomeStepProps {
  data?: any;
  onNext: (data?: any) => void;
  isLoading: boolean;
}

export function WelcomeStep({ data, onNext, isLoading }: WelcomeStepProps) {
  const [acknowledged, setAcknowledged] = useState(false);

  const handleNext = () => {
    onNext({ acknowledged: true, timestamp: new Date().toISOString() });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6 px-4 sm:px-6">
      {/* Hero Section - No Icon */}
      <div className="text-center space-y-3">
        <h1 className="text-2xl sm:text-3xl font-bold text-white">
          🎉 Welcome to NextGen Business!
        </h1>
        
        <p className="text-sm sm:text-base text-gray-300 max-w-2xl mx-auto">
          Say goodbye to the pain of complex accounting and managing hundreds of spreadsheets. 
          Our intuitive, easy-to-use application solves your business management problems 
          so you can focus on what you do best — running your business.
        </p>
      </div>

      {/* Problem Statement */}
      <Card className="border-2 border-red-500/30 bg-red-900/20">
        <CardContent className="p-3 sm:p-4">
          <h3 className="text-sm sm:text-base font-semibold text-red-300 mb-2">
            Tired of dealing with...
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-3">
            <div className="space-y-1">
              <div className="flex items-center text-xs sm:text-sm text-red-200">
                <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-400 rounded-full mr-2 flex-shrink-0"></div>
                Complex spreadsheets that break constantly
              </div>
              <div className="flex items-center text-xs sm:text-sm text-red-200">
                <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-400 rounded-full mr-2 flex-shrink-0"></div>
                Manual data entry and calculation errors
              </div>
              <div className="flex items-center text-xs sm:text-sm text-red-200">
                <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-400 rounded-full mr-2 flex-shrink-0"></div>
                Scattered financial information
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center text-xs sm:text-sm text-red-200">
                <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-400 rounded-full mr-2 flex-shrink-0"></div>
                Time-consuming reconciliation processes
              </div>
              <div className="flex items-center text-xs sm:text-sm text-red-200">
                <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-400 rounded-full mr-2 flex-shrink-0"></div>
                Difficulty tracking profitability
              </div>
              <div className="flex items-center text-xs sm:text-sm text-red-200">
                <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-400 rounded-full mr-2 flex-shrink-0"></div>
                Stress during tax season
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Solution */}
      <Card className="border-2 border-green-500/30 bg-green-900/20">
        <CardContent className="p-3 sm:p-4">
          <h3 className="text-sm sm:text-base font-semibold text-green-300 mb-3">
            We'll help you achieve...
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
            <div className="text-center space-y-2">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <h4 className="font-medium text-xs sm:text-sm text-green-300">Automated Workflows</h4>
              <p className="text-xs text-green-200">
                Streamlined processes that work in the background
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <h4 className="font-medium text-xs sm:text-sm text-green-300">Data Security</h4>
              <p className="text-xs text-green-200">
                Bank-level encryption and reliable backups
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <h4 className="font-medium text-xs sm:text-sm text-green-300">Clear Insights</h4>
              <p className="text-xs text-green-200">
                Real-time reports and actionable analytics
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Call to Action */}
      <Card className="border-2 border-blue-500/30 bg-blue-900/20">
        <CardContent className="p-4 sm:p-5 text-center">
          <h3 className="text-base sm:text-lg font-bold text-blue-300 mb-2 sm:mb-3">
            If you're ready to deal with this pain once and for all...
          </h3>
          <p className="text-xs sm:text-sm text-blue-200 mb-3 sm:mb-4">
            Let's get your business set up with a system that actually works for you.
          </p>
          
          <Button 
            onClick={handleNext}
            disabled={isLoading}
            size="lg"
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 sm:px-8 py-2 sm:py-2.5 text-sm sm:text-base font-semibold w-full sm:w-auto"
          >
            {isLoading ? "Starting..." : "Let's Get Started →"}
          </Button>
          
          <p className="text-xs text-gray-400 mt-2 sm:mt-3">
            This will take about 5-8 minutes to complete
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
 