"use client";
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";

interface IncomeStatementProps {
  data: {
    revenue: any[];
    expenses: any[];
    totalRevenue: number;
    totalExpenses: number;
    netIncome: number;
  };
  loading?: boolean;
}

const IncomeStatement = ({ data, loading = false }: IncomeStatementProps) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white">Income Statement</h2>
      <div className="space-y-6">
        {/* Revenue */}
        <div>
          <h3 className="text-lg font-medium text-white mb-2">Revenue</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Account</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="text-gray-300">
              {data.revenue.map((ab) => (
                <TableRow key={ab.account.id}>
                  <TableCell>{ab.account.name}</TableCell>
                  <TableCell className="text-right">
                    ${ab.balance.toFixed(2)}
                  </TableCell>
                </TableRow>
              ))}
              <TableRow className="font-bold text-white">
                <TableCell>Total Revenue</TableCell>
                <TableCell className="text-right">
                  ${data.totalRevenue.toFixed(2)}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
        {/* Expenses */}
        <div>
          <h3 className="text-lg font-medium text-white mb-2">Expenses</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Account</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="text-gray-300">
              {data.expenses.map((ab) => (
                <TableRow key={ab.account.id}>
                  <TableCell>{ab.account.name}</TableCell>
                  <TableCell className="text-right">
                    ${ab.balance.toFixed(2)}
                  </TableCell>
                </TableRow>
              ))}
              <TableRow className="font-bold text-white">
                <TableCell>Total Expenses</TableCell>
                <TableCell className="text-right">
                  ${data.totalExpenses.toFixed(2)}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
        {/* Net Income */}
        <Table>
          <TableBody>
            <TableRow
              className={`font-bold text-lg ${data.netIncome >= 0 ? "text-green-500" : "text-red-500"}`}
            >
              <TableCell>Net Income</TableCell>
              <TableCell className="text-right">
                ${data.netIncome.toFixed(2)}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default IncomeStatement; 