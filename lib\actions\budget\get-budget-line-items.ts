"use server";

import { db } from "@/db/drizzle";
import { budgetLineItems, accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, asc } from "drizzle-orm";

export async function getBudgetLineItems(budgetPeriodId: string) {
  await getServerUserContext();

  try {
    return await db
      .select({
        id: budgetLineItems.id,
        budgetPeriodId: budgetLineItems.budgetPeriodId,
        accountId: budgetLineItems.accountId,
        budgetedAmount: budgetLineItems.budgetedAmount,
        notes: budgetLineItems.notes,
        createdAt: budgetLineItems.createdAt,
        updatedAt: budgetLineItems.updatedAt,
        accountName: accounts.name,
        accountNumber: accounts.accountNumber,
        accountType: accounts.type,
      })
      .from(budgetLineItems)
      .leftJoin(accounts, eq(budgetLineItems.accountId, accounts.id))
      .where(eq(budgetLineItems.budgetPeriodId, budgetPeriodId))
      .orderBy(asc(accounts.accountNumber), asc(accounts.name));
  } catch (error) {
    console.error("Error fetching budget line items:", error);
    return [];
  }
} 