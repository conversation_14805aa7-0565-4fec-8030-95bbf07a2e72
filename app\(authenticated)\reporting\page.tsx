import React from "react";
import ReportingPageClient from "@/components/protectedPages/Reporting/ReportingPageClient";
import { getServerUserContext } from "@/lib/server-auth";

interface ReportingPageProps {
  searchParams?: Promise<{ from?: string; to?: string }>;
}

export default async function ReportingPage({ searchParams }: ReportingPageProps) {
  const params = await searchParams;
  const { organization } = await getServerUserContext();
  const initialDateRange = { 
    from: params?.from, 
    to: params?.to 
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Reporting</h1>
      <ReportingPageClient 
        initialDateRange={initialDateRange} 
        organizationName={organization.name}
      />
    </div>
  );
}
