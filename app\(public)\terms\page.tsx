"use client";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export default function TermsPage() {
  return (
    <div className="max-w-2xl mx-auto py-12 px-4">
      <Card>
        <CardContent className="p-6">
          <h1 className="text-3xl font-bold mb-4">Terms of Service</h1>
          <p className="text-sm text-muted-foreground mb-6">Last updated: May 2024</p>
          <Separator className="mb-6" />

          <h2 className="text-xl font-semibold mb-2">1. Acceptance of Terms</h2>
          <p className="mb-4">By accessing or using NextGen Business, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services.</p>

          <h2 className="text-xl font-semibold mb-2">2. Use of Service</h2>
          <p className="mb-4">You agree to use NextGen Business only for lawful purposes and in accordance with all applicable laws and regulations. You are responsible for maintaining the confidentiality of your account and password.</p>

          <h2 className="text-xl font-semibold mb-2">3. Intellectual Property</h2>
          <p className="mb-4">All content, features, and functionality on NextGen Business are the exclusive property of NextGen Business and its licensors. You may not copy, modify, or distribute any part of our service without our prior written consent.</p>

          <h2 className="text-xl font-semibold mb-2">4. Termination</h2>
          <p className="mb-4">We reserve the right to suspend or terminate your access to NextGen Business at any time, without notice, for conduct that we believe violates these Terms or is harmful to other users of the service.</p>

          <h2 className="text-xl font-semibold mb-2">5. Disclaimer & Limitation of Liability</h2>
          <p className="mb-4">NextGen Business is provided "as is" and without warranties of any kind. We are not liable for any damages arising from your use of the service.</p>

          <h2 className="text-xl font-semibold mb-2">6. Changes to Terms</h2>
          <p className="mb-4">We may update these Terms of Service from time to time. We will notify users of any changes by updating the date at the top of this page. Continued use of the service constitutes acceptance of the new terms.</p>

          <h2 className="text-xl font-semibold mb-2">7. Contact Us</h2>
          <p>If you have any questions about these Terms, please contact <NAME_EMAIL>.</p>
        </CardContent>
      </Card>
    </div>
  );
} 