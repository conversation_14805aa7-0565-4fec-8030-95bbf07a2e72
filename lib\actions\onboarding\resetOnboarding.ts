"use server";

import { db } from "@/db/drizzle";
import { onboarding } from "@/db/schema/schema";
import { ONBOARDING_STEPS, OnboardingProgress } from "@/lib/onboarding-constants";
import { eq } from "drizzle-orm";
import { randomUUID } from 'crypto';

/**
 * Reset onboarding progress for a user
 */
export async function resetOnboarding(userId: string): Promise<OnboardingProgress> {
  try {
    // Delete any existing onboarding progress for this user
    await db.delete(onboarding)
      .where(eq(onboarding.userId, userId));

    // Insert new onboarding progress
    const result = await db
      .insert(onboarding)
      .values({
        id: randomUUID(),
        userId,
        step: ONBOARDING_STEPS.WELCOME,
        totalSteps: 7, // Updated from 6 to 7
        isCompleted: false,
      })
      .returning();

    const created = result[0];
    return {
      id: created.id,
      userId: created.userId,
      step: created.step,
      totalSteps: created.totalSteps ?? 6,
      welcomeData: created.orgBasics,
      businessTypeData: created.businessDetails,
      organizationData: created.orgBasics,
      addressData: created.orgDetails,
      contactData: created.orgDetails,
      accountsData: null, // No direct mapping
      subscriptionData: created.subscription,
      completionData: { isCompleted: created.isCompleted, completedAt: created.completedAt },
      lastActiveStep: created.step ?? 1,
      isCompleted: created.isCompleted ?? false,
      createdAt: created.createdAt ?? new Date(),
      updatedAt: created.updatedAt ?? new Date(),
    };
  } catch (error) {
    console.error('Error resetting onboarding:', error);
    throw error;
  }
} 