"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and } from "drizzle-orm";
import { getBudgetLineItems } from "./get-budget-line-items";
import { getBudgetVsActualData } from "./get-budget-vs-actual-data";

export async function getBudgetPeriodDetails(budgetPeriodId: string) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    const [period] = await db
      .select()
      .from(budgetPeriods)
      .where(
        and(
          eq(budgetPeriods.id, budgetPeriodId),
          eq(budgetPeriods.organizationId, organizationId)
        )
      )
      .limit(1);

    if (!period) {
      return { error: "Budget period not found" };
    }

    const lineItems = await getBudgetLineItems(budgetPeriodId);
    const vsActual = await getBudgetVsActualData(budgetPeriodId);
    let budgetSummary = null;
    if (vsActual && vsActual.summary) {
      const s = vsActual.summary;
      budgetSummary = {
        totalBudgetedRevenue: s.totalBudgetedRevenue || 0,
        totalActualRevenue: s.totalActualRevenue || 0,
        revenueVariance: (s.totalActualRevenue || 0) - (s.totalBudgetedRevenue || 0),
        revenueVariancePercent: (s.totalBudgetedRevenue ? (((s.totalActualRevenue || 0) - (s.totalBudgetedRevenue || 0)) / s.totalBudgetedRevenue) * 100 : 0),
        totalBudgetedExpenses: s.totalBudgetedExpenses || 0,
        totalActualExpenses: s.totalActualExpenses || 0,
        expenseVariance: (s.totalBudgetedExpenses || 0) - (s.totalActualExpenses || 0),
        expenseVariancePercent: (s.totalBudgetedExpenses ? (((s.totalBudgetedExpenses || 0) - (s.totalActualExpenses || 0)) / s.totalBudgetedExpenses) * 100 : 0),
        budgetedNetIncome: s.budgetedNetIncome || 0,
        actualNetIncome: s.actualNetIncome || 0,
        netIncomeVariance: (s.actualNetIncome || 0) - (s.budgetedNetIncome || 0),
        netIncomeVariancePercent: (s.budgetedNetIncome ? (((s.actualNetIncome || 0) - (s.budgetedNetIncome || 0)) / s.budgetedNetIncome) * 100 : 0),
      };
    }

    return {
      success: true,
      data: {
        ...period,
        lineItems,
        budgetSummary,
      },
    };
  } catch (error) {
    console.error("Error fetching budget period details:", error);
    return { error: "Failed to fetch budget period details" };
  }
} 