/**
 * Onboarding constants and pure utility functions
 * This file contains no database imports and can be safely used in client components
 */

/**
 * Onboarding step numbers (matching existing schema)
 */
export const ONBOARDING_STEPS = {
  WELCOME: 1,
  ORGANIZATION_BASICS: 2,
  ORGANIZATION_DETAILS: 3,
  BUSINESS_TYPE: 4,
  CHART_OF_ACCOUNTS: 5,
  SUBSCRIPTION: 6,
  LEGAL_CONSENT: 7,
  COMPLETION: 8,
} as const;

/**
 * Onboarding progress interface matching existing schema
 */
export interface OnboardingProgress {
  id: string;
  userId: string;
  step: number;
  totalSteps: number;
  welcomeData?: any;
  businessTypeData?: any;
  organizationData?: any;
  addressData?: any;
  contactData?: any;
  accountsData?: any;
  subscriptionData?: any;
  completionData?: any;
  lastActiveStep: number;
  isCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get step name from step number
 */
export function getStepName(step: number): string {
  switch (step) {
    case ONBOARDING_STEPS.WELCOME:
      return "Welcome";
    case ONBOARDING_STEPS.ORGANIZATION_BASICS:
      return "Organization Basics";
    case ONBOARDING_STEPS.ORGANIZATION_DETAILS:
      return "Organization Details";
    case ONBOARDING_STEPS.BUSINESS_TYPE:
      return "Business Type";
    case ONBOARDING_STEPS.CHART_OF_ACCOUNTS:
      return "Chart of Accounts";
    case ONBOARDING_STEPS.SUBSCRIPTION:
      return "Subscription";
    case ONBOARDING_STEPS.LEGAL_CONSENT:
      return "Legal Consent & Review";
    case ONBOARDING_STEPS.COMPLETION:
      return "Completion";
    default:
      return "Unknown Step";
  }
}

/**
 * Get next step number
 */
export function getNextStep(currentStep: number): number | null {
  if (currentStep < ONBOARDING_STEPS.COMPLETION) {
    return currentStep + 1;
  }
  return null;
}

/**
 * Get previous step number
 */
export function getPreviousStep(currentStep: number): number | null {
  if (currentStep > ONBOARDING_STEPS.WELCOME) {
    return currentStep - 1;
  }
  return null;
}

/**
 * Check if user can access a specific onboarding step
 */
export function canAccessStep(progress: OnboardingProgress, targetStep: number): boolean {
  // Can access current step or any previous step
  return targetStep <= progress.lastActiveStep;
} 