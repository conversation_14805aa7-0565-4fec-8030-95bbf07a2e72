"use client";

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DateRange } from 'react-day-picker';
import { PageHeader } from "@/components/layout/PageHeader";
import FinancialOverview from "@/components/dashboard/FinancialOverview";
import PaymentReminders from "@/components/dashboard/PaymentReminders";
import TransactionsGrid from "@/components/dashboard/TransactionsGrid";
import { DateRangePicker } from '@/components/general/date-range-picker';
import { getDashboardData } from '@/lib/actions/dashboard/get-dashboard-data';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DashboardPageClientProps {
    userName: string;
}

export default function DashboardPageClient({ userName }: DashboardPageClientProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [retryCount, setRetryCount] = useState(0);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);

  const { data: dashboardData, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['dashboardData', dateRange, retryCount],
    queryFn: async () => {
      try {
        const data = await getDashboardData({ dateRange });
        return data;
      } catch (err) {
        // Capture detailed error information for debugging
        const errorMessage = err instanceof Error 
          ? `${err.name}: ${err.message}${err.stack ? `\nStack: ${err.stack}` : ''}` 
          : String(err);
        setErrorDetails(errorMessage);
        throw err;
      }
    },
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      setDateRange(range);
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    setErrorDetails(null);
    refetch();
  };
  
  if (isError) {
    return (
      <Alert variant="destructive" className="my-8">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error loading dashboard</AlertTitle>
        <AlertDescription>
          <p>There was a problem loading your dashboard data. This might be related to database permissions or RLS policies.</p>
          {errorDetails && (
            <div className="mt-2 p-2 bg-gray-800 text-white rounded text-xs overflow-auto max-h-40">
              <pre>{errorDetails}</pre>
            </div>
          )}
          <Button 
            variant="outline" 
            className="mt-2" 
            onClick={handleRetry}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading dashboard data...</p>
      </div>
    );
  }

  return (
    <div>
        <div className="flex justify-between items-start">
            <PageHeader
                title={`Welcome, ${userName}!`}
                description="Here's a snapshot of your business's financial health."
            />
            <DateRangePicker onDateRangeChange={handleDateRangeChange} value={dateRange} />
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        <div className="lg:col-span-2 space-y-6">
          <FinancialOverview 
            isLoading={isLoading}
            monthlyEarnings={dashboardData?.financialOverview?.monthlyEarnings}
            monthlyExpenses={dashboardData?.financialOverview?.monthlyExpenses}
            monthlyGrowth={dashboardData?.financialOverview?.monthlyGrowth}
          />
          <TransactionsGrid 
            isLoading={isLoading}
            transactions={dashboardData?.transactions}
          />
        </div>
        <div className="lg:col-span-1">
          <PaymentReminders 
            isLoading={isLoading}
            payments={dashboardData?.paymentReminders}
          />
        </div>
      </div>
    </div>
  );
} 