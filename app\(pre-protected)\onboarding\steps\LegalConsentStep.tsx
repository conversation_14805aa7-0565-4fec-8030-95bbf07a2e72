"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { updateOnboardingStep } from "@/lib/actions/onboarding/updateOnboardingStep";
import { ONBOARDING_STEPS } from "@/lib/onboarding-constants";
import { coaTemplatesMetadata } from "@/lib/data/coaTemplates";
import { getTimeZones } from '@vvo/tzdb';
import { countries } from "@/lib/country-utils";

// Helper to map businessType to label
const businessTypeLabels: Record<string, string> = {
  digital: "Digital Business",
  physical: "Physical Business",
  hybrid: "Hybrid Business",
  other: "Other",
  all: "All Types"
};

// Helper to get chart template label from metadata
function getChartTemplateLabel(templateId?: string) {
  if (!templateId) return "-";
  const meta = coaTemplatesMetadata.find(t => t.id === templateId);
  return meta ? meta.templateName : templateId;
}

// List of allowed European capitals and major US cities
const allowedTimeZones = [
  // European capitals
  'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Europe/Madrid', 'Europe/Rome',
  'Europe/Amsterdam', 'Europe/Brussels', 'Europe/Vienna', 'Europe/Prague', 'Europe/Warsaw',
  'Europe/Budapest', 'Europe/Copenhagen', 'Europe/Dublin', 'Europe/Helsinki', 'Europe/Lisbon',
  'Europe/Luxembourg', 'Europe/Oslo', 'Europe/Stockholm', 'Europe/Bucharest', 'Europe/Sofia',
  'Europe/Athens', 'Europe/Zagreb', 'Europe/Belgrade', 'Europe/Bratislava', 'Europe/Tallinn',
  'Europe/Riga', 'Europe/Vilnius', 'Europe/Valletta', 'Europe/Nicosia', 'Europe/Skopje',
  'Europe/Kiev', 'Europe/Minsk', 'Europe/Moscow', 'Europe/Sarajevo', 'Europe/Podgorica',
  'Europe/Tirane', 'Europe/Chisinau', 'Europe/Pristina', 'Europe/Vaduz', 'Europe/Monaco',
  'Europe/San_Marino', 'Europe/Vatican',
  // Major US cities
  'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
  'America/Phoenix', 'America/Anchorage', 'America/Detroit', 'America/Indianapolis',
  'America/Boise', 'America/Juneau', 'America/Minneapolis', 'America/Kentucky/Louisville',
  'America/Kentucky/Monticello', 'America/North_Dakota/Center', 'America/North_Dakota/New_Salem',
  'America/North_Dakota/Beulah', 'America/St_Louis', 'America/Denver', 'America/Los_Angeles',
  'America/Chicago', 'America/New_York', 'America/Anchorage', 'America/Phoenix',
  'America/Los_Angeles', 'America/Denver', 'America/Chicago', 'America/New_York'
];

// Map of European country codes to their capital's time zone
const countryToCapitalTz: Record<string, string> = {
  FR: 'Europe/Paris',
  DE: 'Europe/Berlin',
  AT: 'Europe/Vienna',
  BE: 'Europe/Brussels',
  NL: 'Europe/Amsterdam',
  IT: 'Europe/Rome',
  ES: 'Europe/Madrid',
  PT: 'Europe/Lisbon',
  CH: 'Europe/Zurich',
  PL: 'Europe/Warsaw',
  CZ: 'Europe/Prague',
  HU: 'Europe/Budapest',
  DK: 'Europe/Copenhagen',
  IE: 'Europe/Dublin',
  FI: 'Europe/Helsinki',
  LU: 'Europe/Luxembourg',
  NO: 'Europe/Oslo',
  SE: 'Europe/Stockholm',
  RO: 'Europe/Bucharest',
  BG: 'Europe/Sofia',
  GR: 'Europe/Athens',
  HR: 'Europe/Zagreb',
  RS: 'Europe/Belgrade',
  SK: 'Europe/Bratislava',
  EE: 'Europe/Tallinn',
  LV: 'Europe/Riga',
  LT: 'Europe/Vilnius',
  MT: 'Europe/Valletta',
  CY: 'Europe/Nicosia',
  MK: 'Europe/Skopje',
  UA: 'Europe/Kiev',
  BY: 'Europe/Minsk',
  RU: 'Europe/Moscow',
  BA: 'Europe/Sarajevo',
  ME: 'Europe/Podgorica',
  AL: 'Europe/Tirane',
  MD: 'Europe/Chisinau',
  XK: 'Europe/Pristina',
  LI: 'Europe/Vaduz',
  MC: 'Europe/Monaco',
  SM: 'Europe/San_Marino',
  VA: 'Europe/Vatican',
  GB: 'Europe/London',
  IS: 'Atlantic/Reykjavik',
};

function getTimeZoneLabel(tzName?: string, countryCode?: string) {
  if (!tzName) return "-";
  const tzs = getTimeZones();
  const tz = tzs.find(t => t.name === tzName);
  // If the selected tz is a European capital or major US city, show as before
  if (tz && allowedTimeZones.includes(tzName)) {
    const offsetMinutes = tz.currentTimeOffsetInMinutes;
    const sign = offsetMinutes >= 0 ? '+' : '-';
    const absMinutes = Math.abs(offsetMinutes);
    const hours = Math.floor(absMinutes / 60);
    const minutes = absMinutes % 60;
    const offsetStr = `GMT${sign}${hours}${minutes !== 0 ? ':' + minutes.toString().padStart(2, '0') : ''}`;
    const city = tz.mainCities && tz.mainCities.length > 0 ? tz.mainCities[0] : tz.name.split('/').pop();
    return `${city} (${offsetStr})`;
  }
  // If country is in Europe, try to use its capital's time zone
  if (countryCode) {
    const country = countries.find(c => c.code === countryCode);
    if (country && country.continent === 'EU') {
      let capitalTz = countryToCapitalTz[countryCode];
      // If capital tz not in allowed list, fallback to Berlin for CET/CEST
      if (!capitalTz || !tzs.find(t => t.name === capitalTz)) {
        // France: always Paris
        if (countryCode === 'FR') capitalTz = 'Europe/Paris';
        // UK: always London
        else if (countryCode === 'GB') capitalTz = 'Europe/London';
        // CET/CEST fallback: Berlin
        else capitalTz = 'Europe/Berlin';
      }
      const capitalTzObj = tzs.find(t => t.name === capitalTz);
      if (capitalTzObj) {
        const offsetMinutes = capitalTzObj.currentTimeOffsetInMinutes;
        const sign = offsetMinutes >= 0 ? '+' : '-';
        const absMinutes = Math.abs(offsetMinutes);
        const hours = Math.floor(absMinutes / 60);
        const minutes = absMinutes % 60;
        const offsetStr = `GMT${sign}${hours}${minutes !== 0 ? ':' + minutes.toString().padStart(2, '0') : ''}`;
        const city = capitalTzObj.mainCities && capitalTzObj.mainCities.length > 0 ? capitalTzObj.mainCities[0] : capitalTzObj.name.split('/').pop();
        return `${city} (${offsetStr})`;
      }
    }
  }
  // Otherwise, fallback to raw tzName
  return tzName;
}

// Helper to get country name from code
function getCountryName(code?: string) {
  if (!code) return "-";
  const country = countries.find(c => c.code === code);
  return country ? country.name : code;
}

interface LegalConsentStepProps {
  onNext: (data: any) => void;
  onPrevious: () => void;
  isLoading: boolean;
  userId: string;
}

export function LegalConsentStep({ onNext, onPrevious, isLoading, userId }: LegalConsentStepProps) {
  const { data: onboardingData, updateData } = useOnboardingStore();
  const [consent, setConsent] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleContinue = async () => {
    if (!consent) return;
    setSubmitting(true);
    const timestamp = new Date().toISOString();
    updateData({ legalConsentAcceptedAt: timestamp });
    await updateOnboardingStep(
      userId,
      ONBOARDING_STEPS.COMPLETION - 1,
      { legalConsent: true, legalConsentAcceptedAt: timestamp }
    );
    setSubmitting(false);
    onNext({ legalConsent: true, legalConsentAcceptedAt: timestamp });
  };

  // Helper to render a summary row
  const SummaryRow = ({ label, value }: { label: string; value: any }) => (
    <div className="flex justify-between py-1 text-sm">
      <span className="text-gray-300 font-medium">{label}</span>
      <span className="text-gray-100">{value || <span className="text-gray-500">-</span>}</span>
    </div>
  );

  // Extract summary data
  const org = onboardingData.organization || {};
  // Use 'any' for summary display to avoid linter errors on optional fields
  const orgDetails = (onboardingData.orgDetails || {}) as any;
  const business = (onboardingData.businessDetails || {}) as any;
  const subscription = onboardingData.subscription || {};

  return (
    <div className="max-w-3xl mx-auto space-y-6 px-4 sm:px-6">
      <div className="text-center space-y-2 sm:space-y-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-white">Review & Consent</h2>
        <p className="text-sm sm:text-base text-gray-300 max-w-2xl mx-auto">
          Please review your information and agree to our Terms & Privacy Policy to continue.
        </p>
      </div>

      <Card className="bg-gray-800/50 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white text-lg">Organization Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <SummaryRow label="Name" value={org.name} />
          <SummaryRow label="Email" value={org.email} />
          <SummaryRow label="Industry" value={org.industry} />
          <SummaryRow label="Website" value={org.website} />
          <SummaryRow label="Legal Entity" value={org.legalEntity} />
          <SummaryRow label="Phone" value={orgDetails?.phone ?? "-"} />
          <SummaryRow label="Country" value={getCountryName(orgDetails?.addresses?.[0]?.country)} />
          <SummaryRow label="Time Zone" value={getTimeZoneLabel(orgDetails?.timeZone, orgDetails?.addresses?.[0]?.country)} />
          <SummaryRow label="Currency" value={orgDetails?.currency ?? "-"} />
          <SummaryRow label="Business Type" value={business?.businessType ? businessTypeLabels[business.businessType] || business.businessType : "-"} />
          <SummaryRow label="Chart Template" value={getChartTemplateLabel(business?.chartTemplate)} />
          <SummaryRow label="Plan" value={subscription.planType} />
          <SummaryRow label="Billing Interval" value={subscription.interval} />
        </CardContent>
      </Card>

      <div className="flex items-center space-x-3 mt-4">
        <Checkbox id="consent" checked={consent} onCheckedChange={v => setConsent(!!v)} />
        <label htmlFor="consent" className="text-gray-200 text-sm select-none">
          I agree to the <a href="/terms" target="_blank" className="underline text-blue-400">Terms & Conditions</a> and <a href="/privacy" target="_blank" className="underline text-blue-400">Privacy Policy</a>.
        </label>
      </div>

      <div className="flex justify-end space-x-4 mt-6">
        <Button variant="outline" onClick={onPrevious} disabled={isLoading || submitting}>Previous</Button>
        <Button onClick={handleContinue} disabled={!consent || isLoading || submitting}>
          {submitting ? "Saving..." : "Continue"}
        </Button>
      </div>
    </div>
  );
} 