"use client";

import React from "react";
import { TransactionEntry } from "./TransactionEntriesSection";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Account {
  id: string;
  name: string;
  type: string;
}

interface TransactionEntryRowProps {
  entry: TransactionEntry;
  onChange: (entry: TransactionEntry) => void;
  onRemove: () => void;
  accounts: Account[];
}

export default function TransactionEntryRow({ entry, onChange, onRemove, accounts }: TransactionEntryRowProps) {
  return (
    <div className="flex items-center gap-2 bg-white/10 rounded p-2">
      <div className="flex-1">
        <Select
          value={entry.accountId}
          onValueChange={accountId => onChange({ ...entry, accountId })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select account" />
          </SelectTrigger>
          <SelectContent>
            {accounts.map(acc => (
              <SelectItem key={acc.id} value={acc.id}>
                {acc.name} ({acc.type})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <span className="w-24 text-gray-400">Debit</span>
      <span className="w-24 text-gray-400">Credit</span>
      <span className="flex-1 text-gray-400">Description</span>
      <button type="button" className="text-red-500" onClick={onRemove}>
        Remove
      </button>
    </div>
  );
} 