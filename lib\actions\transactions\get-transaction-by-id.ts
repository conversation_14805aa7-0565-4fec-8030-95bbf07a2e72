"use server";

import { db } from "@/db/drizzle";
import { transactions, journalEntries, accounts } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq, and } from "drizzle-orm";

async function getUserOrganizationId(userId: string) {
  const { member } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: member.organizationId })
    .from(member)
.where(eq(member.userId, userId));
  return orgs[0]?.organizationId || null;
}

export async function getTransactionById(transactionId: string) {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;
  if (!userId) return null;
  const organizationId = await getUserOrganizationId(userId);
  if (!organizationId) return null;

  const [transaction] = await db
    .select()
    .from(transactions)
    .where(and(eq(transactions.id, transactionId), eq(transactions.organizationId, organizationId)));
  if (!transaction) return null;

  const jEntries = await db
    .select({
      accountId: journalEntries.accountId,
      debit: journalEntries.debitAmount,
      credit: journalEntries.creditAmount,
      accountName: accounts.name,
      accountType: accounts.type,
    })
    .from(journalEntries)
    .innerJoin(accounts, eq(journalEntries.accountId, accounts.id))
    .where(eq(journalEntries.transactionId, transactionId));

  return { ...transaction, journalEntries: jEntries };
} 