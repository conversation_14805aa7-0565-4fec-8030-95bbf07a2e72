"use server";

import { db } from "@/db/drizzle";
import { bills } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, ne, lte } from "drizzle-orm";
import { ageBucket } from "./age-bucket";

export async function getAPAgingSummary(asOfDateStr?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;
    const asOfDate = asOfDateStr ? new Date(asOfDateStr) : new Date();

    const openBills = await db.select({
        id: bills.id,
        total: bills.total,
        dueDate: bills.dueDate,
        status: bills.status,
    }).from(bills)
      .where(and(
          eq(bills.organizationId, organizationId),
          ne(bills.status, 'paid'),
          ne(bills.status, 'void'),
          lte(bills.date, asOfDate)
      ));

    const summary = {
        current: 0,
        '1-30': 0,
        '31-60': 0,
        '61-90': 0,
        '91+': 0,
        total: 0,
    };

    for (const bill of openBills) {
        const bucket = ageBucket(bill.dueDate!, asOfDate);
        const amount = parseFloat(bill.total as string);
        (summary as any)[bucket] += amount;
        summary.total += amount;
    }
    
    return summary;
}