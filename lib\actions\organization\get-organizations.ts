"use server";

import { db } from "@/db/drizzle";
import { eq, inArray } from "drizzle-orm";
import { member, organization} from "@/db/schema/schema";
import { getCurrentUser } from "../user/get-current-user";

export async function getOrganizations() {
    const { currentUser } = await getCurrentUser();

    const members = await db.query.member.findMany({
        where: eq(member.userId, currentUser.id),
    });

    const userOrganizations = await db.query.organization.findMany({
where: inArray(organization.id, members.map((member) => member.organizationId)),
    });

    return {
        userOrganizations,
    };
}

export async function getActiveOrganization(userId: string) {
    const members = await db.query.member.findFirst({
where: eq(member.userId, userId),
    })

    if (!members) {
        return null;
    }
    const activeOrganization = await db.query.organization.findFirst({
where: eq(organization.id, members.organizationId),
    });

    return activeOrganization;
}



  