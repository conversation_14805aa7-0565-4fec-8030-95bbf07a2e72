"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const adjustmentTypeEnum = z.enum([
  "fixed",
  "percentage_increase",
  "percentage_decrease",
  "amount_increase",
  "amount_decrease",
]);

const adjustPriceSchema = z.object({
  adjustmentType: adjustmentTypeEnum,
  value: z.number().positive("Value must be a positive number."),
});

export type AdjustPriceFormData = z.infer<typeof adjustPriceSchema>;

interface BulkAdjustPriceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AdjustPriceFormData) => void;
  isPending: boolean;
  productCount: number;
}

export function BulkAdjustPriceDialog({
  isOpen,
  onClose,
  onSubmit,
  isPending,
  productCount,
}: BulkAdjustPriceDialogProps) {
  const form = useForm<AdjustPriceFormData>({
    resolver: zodResolver(adjustPriceSchema),
    defaultValues: { adjustmentType: "fixed" },
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Bulk Adjust Price</DialogTitle>
          <DialogDescription>
            Adjust the price for the {productCount} selected products.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="adjustmentType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adjustment Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an adjustment type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="fixed">Set Fixed Price</SelectItem>
                      <SelectItem value="percentage_increase">Increase by %</SelectItem>
                      <SelectItem value="percentage_decrease">Decrease by %</SelectItem>
                      <SelectItem value="amount_increase">Increase by Amount</SelectItem>
                      <SelectItem value="amount_decrease">Decrease by Amount</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="value"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Value</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Adjusting..." : "Adjust Prices"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 