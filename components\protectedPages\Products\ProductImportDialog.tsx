"use client";

import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useDropzone } from 'react-dropzone';
import { unparse, parse } from 'papaparse';
import { toast } from 'sonner';
import { importProducts } from '@/lib/actions/products/import-products';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type FileWithPath } from 'react-dropzone';

interface ProductImportDialogProps {
    isOpen: boolean;
    onClose: () => void;
}

const productFields = [
    { key: 'name', label: 'Product Name', required: true },
    { key: 'description', label: 'Description' },
    { key: 'sku', label: 'SKU' },
    { key: 'type', label: 'Product Type', required: true },
    { key: 'price', label: 'Price' },
    { key: 'costBasis', label: 'Cost Basis' },
    { key: 'category', label: 'Category' },
    { key: 'revenueAccount', label: 'Revenue Account', required: true },
    { key: 'cogsAccount', label: 'COGS Account' },
    { key: 'inventoryAccount', label: 'Inventory Account' },
    { key: 'id', label: 'Product ID (for updates)' },
];

export function ProductImportDialog({ isOpen, onClose }: ProductImportDialogProps) {
    const [file, setFile] = useState<File | null>(null);
    const [parsedData, setParsedData] = useState<any[]>([]);
    const [headers, setHeaders] = useState<string[]>([]);
    const [mapping, setMapping] = useState<Record<string, string>>({});
    const queryClient = useQueryClient();

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop: (acceptedFiles: FileWithPath[]) => {
            const uploadedFile = acceptedFiles[0];
            setFile(uploadedFile);
            parse(uploadedFile, {
                header: true,
                skipEmptyLines: true,
                complete: (results) => {
                    setHeaders(results.meta.fields || []);
                    setParsedData(results.data);
                    // Auto-map headers
                    const initialMapping: Record<string, string> = {};
                    (results.meta.fields || []).forEach(header => {
                        const matchingField = productFields.find(f => f.label.toLowerCase() === header.toLowerCase() || f.key.toLowerCase() === header.toLowerCase());
                        if (matchingField) {
                            initialMapping[matchingField.key] = header;
                        }
                    });
                    setMapping(initialMapping);
                }
            });
        },
        accept: { 'text/csv': ['.csv'] }
    });
    
    const mutation = useMutation({
        mutationFn: importProducts,
        onSuccess: (result) => {
            if ('message' in result) {
                toast.error(result.message);
                return;
            }

            if (result.errors && result.errors.length > 0) {
                toast.error(`${result.errors.length} rows had errors. See console for details.`);
                console.error("Import errors:", result.errors);
            } else {
                toast.success(`Import successful! ${result.created} created, ${result.updated} updated.`);
            }
            queryClient.invalidateQueries({ queryKey: ['products-with-analytics'] });
            onClose();
        },
        onError: (error) => {
            toast.error(error.message);
        }
    });

    const handleImport = () => {
        const mappedData = parsedData.map(row => {
            const newRow: Record<string, any> = {};
            for (const key in mapping) {
                newRow[key] = row[mapping[key]];
            }
            return newRow;
        });
        mutation.mutate(mappedData);
    };
    
    const resetState = () => {
        setFile(null);
        setParsedData([]);
        setHeaders([]);
        setMapping({});
    }

    const handleClose = () => {
        resetState();
        onClose();
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>Import Products from CSV</DialogTitle>
                </DialogHeader>
                
                {!file ? (
                    <div {...getRootProps()} className={`p-10 border-2 border-dashed rounded-lg text-center cursor-pointer ${isDragActive ? 'border-primary' : ''}`}>
                        <input {...getInputProps()} />
                        <p>Drag 'n' drop a CSV file here, or click to select a file.</p>
                    </div>
                ) : (
                    <div>
                        <h3 className="font-semibold mb-2">Map CSV Columns to Product Fields</h3>
                        <div className="grid grid-cols-2 gap-4 mb-4">
                            {productFields.map(field => (
                                <div key={field.key} className="flex items-center gap-2">
                                    <label className="w-1/3">{field.label}{field.required && '*'}</label>
                                    <Select 
                                        value={mapping[field.key]}
                                        onValueChange={(value) => setMapping(prev => ({ ...prev, [field.key]: value }))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select CSV column" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {headers.map(header => (
                                                <SelectItem key={header} value={header}>{header}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            ))}
                        </div>
                        <h3 className="font-semibold mb-2">Data Preview</h3>
                        <div className="overflow-auto max-h-64 border rounded-lg">
                             <Table>
                                <TableHeader>
                                    <TableRow>
                                        {productFields.map(field => (
                                            <TableHead key={field.key}>{field.label}</TableHead>
                                        ))}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {parsedData.slice(0, 5).map((row, i) => (
                                        <TableRow key={i}>
                                            {productFields.map(field => (
                                                <TableCell key={field.key}>{row[mapping[field.key]]}</TableCell>
                                            ))}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )}

                <DialogFooter>
                    <Button variant="outline" onClick={handleClose}>Cancel</Button>
                    <Button onClick={handleImport} disabled={!file || mutation.isPending}>
                        {mutation.isPending ? 'Importing...' : 'Import'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 