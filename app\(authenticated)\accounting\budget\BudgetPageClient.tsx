"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Plus, Calendar, DollarSign, TrendingUp, TrendingDown, FileSpreadsheet, Upload } from "lucide-react";
import { setDefaultBudgetPeriod } from "@/lib/actions/budget/set-default-budget-period";
import { CreateBudgetPeriodDialog } from "./CreateBudgetPeriodDialog";
import { BudgetLineItemsTable } from "./BudgetLineItemsTable";
import { BudgetVsActualReport } from "./BudgetVsActualReport";
import BudgetTemplateSheet from "./BudgetTemplateSheet";
import ImportBudgetDialog from "./ImportBudgetDialog";
import BudgetPeriodSheet from "./BudgetPeriodSheet";
import { deleteBudgetPeriod } from "@/lib/actions/budget/delete-budget-period";
import { getBudgetPeriods } from "@/lib/actions/budget/get-budget-periods";
import { getDefaultBudgetPeriod } from "@/lib/actions/budget/get-default-budget-period";
import { getBudgetVsActualData } from "@/lib/actions/budget/get-budget-vs-actual-data";

interface BudgetPeriod {
  id: string;
  name: string;
  description: string | null;
  startDate: Date;
  endDate: Date;
  status: "draft" | "active" | "closed" | "archived" | null;
  isDefault: boolean | null;
}

interface BudgetSummary {
  totalBudgetedRevenue: number;
  totalActualRevenue: number;
  revenueVariance: number;
  revenueVariancePercent: number;
  totalBudgetedExpenses: number;
  totalActualExpenses: number;
  expenseVariance: number;
  expenseVariancePercent: number;
  budgetedNetIncome: number;
  actualNetIncome: number;
  netIncomeVariance: number;
  netIncomeVariancePercent: number;
}

export function BudgetPageClient() {
  const [budgetPeriods, setBudgetPeriods] = useState<BudgetPeriod[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<BudgetPeriod | null>(null);
  const [budgetSummary, setBudgetSummary] = useState<BudgetSummary | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isTemplateSheetOpen, setIsTemplateSheetOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isPeriodSheetOpen, setIsPeriodSheetOpen] = useState(false);
  const [selectedPeriodForView, setSelectedPeriodForView] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    loadBudgetData();
  }, []);

  const loadBudgetData = async () => {
    try {
      setLoading(true);
      const [periods, defaultPeriod] = await Promise.all([
        getBudgetPeriods(),
        getDefaultBudgetPeriod(),
      ]);

      setBudgetPeriods(periods);
      
      if (defaultPeriod) {
        setSelectedPeriod(defaultPeriod);
        const budgetData = await getBudgetVsActualData(defaultPeriod.id);
        if (budgetData) {
          setBudgetSummary(budgetData.summary);
        }
      } else if (periods.length > 0) {
        setSelectedPeriod(periods[0]);
        const budgetData = await getBudgetVsActualData(periods[0].id);
        if (budgetData) {
          setBudgetSummary(budgetData.summary);
        }
      }
    } catch (error) {
      console.error("Error loading budget data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePeriodSelect = async (period: BudgetPeriod) => {
    setSelectedPeriod(period);
    const budgetData = await getBudgetVsActualData(period.id);
    if (budgetData) {
      setBudgetSummary(budgetData.summary);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getVarianceBadgeColor = (variance: number) => {
    if (variance > 0) return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    if (variance < 0) return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
    return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "closed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "archived":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const handleSetDefault = async () => {
    if (selectedPeriod) {
      await setDefaultBudgetPeriod(selectedPeriod.id);
      loadBudgetData(); // Refresh data
    }
  };
  
  const handleDelete = async () => {
    if (selectedPeriod) {
      await deleteBudgetPeriod(selectedPeriod.id);
      setSelectedPeriod(null); // Clear selection
      loadBudgetData(); // Refresh data
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500 dark:text-gray-400">Loading budget data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod?.id || ""}
            onChange={(e) => {
              const period = budgetPeriods.find((p) => p.id === e.target.value);
              if (period) handlePeriodSelect(period);
            }}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="">Select Budget Period</option>
            {budgetPeriods.map((period) => (
              <option key={period.id} value={period.id}>
                {period.name} {period.isDefault && "(Default)"}
              </option>
            ))}
          </select>
          {selectedPeriod && selectedPeriod.status && (
            <Badge className={getStatusBadgeColor(selectedPeriod.status || "")}>
              {selectedPeriod.status.charAt(0).toUpperCase() + selectedPeriod.status.slice(1)}
            </Badge>
          )}
        </div>
        <div className="flex space-x-2">
          {selectedPeriod && (
            <>
              <Button
                variant="outline"
                onClick={() => setIsTemplateSheetOpen(true)}
              >
                <FileSpreadsheet className="w-4 h-4 mr-2" />
                Apply Template
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsImportDialogOpen(true)}
              >
                <Upload className="w-4 h-4 mr-2" />
                Import CSV
              </Button>
            </>
          )}
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            New Budget Period
          </Button>
        </div>
      </div>

      {/* Budget Summary Cards */}
      {budgetSummary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Revenue Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {formatCurrency(budgetSummary.totalActualRevenue)}
                  </span>
                  <div className="flex items-center">
                    {budgetSummary.revenueVariance >= 0 ? (
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                    )}
                    <Badge className={getVarianceBadgeColor(budgetSummary.revenueVariance)}>
                      {budgetSummary.revenueVariancePercent >= 0 ? "+" : ""}
                      {budgetSummary.revenueVariancePercent.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Budget: {formatCurrency(budgetSummary.totalBudgetedRevenue)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Expense Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {formatCurrency(budgetSummary.totalActualExpenses)}
                  </span>
                  <div className="flex items-center">
                    {budgetSummary.expenseVariance <= 0 ? (
                      <TrendingDown className="w-4 h-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingUp className="w-4 h-4 text-red-500 mr-1" />
                    )}
                    <Badge className={getVarianceBadgeColor(-budgetSummary.expenseVariance)}>
                      {budgetSummary.expenseVariancePercent >= 0 ? "+" : ""}
                      {budgetSummary.expenseVariancePercent.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Budget: {formatCurrency(budgetSummary.totalBudgetedExpenses)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Net Income
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {formatCurrency(budgetSummary.actualNetIncome)}
                  </span>
                  <div className="flex items-center">
                    {budgetSummary.netIncomeVariance >= 0 ? (
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                    )}
                    <Badge className={getVarianceBadgeColor(budgetSummary.netIncomeVariance)}>
                      {budgetSummary.netIncomeVariancePercent >= 0 ? "+" : ""}
                      {budgetSummary.netIncomeVariancePercent.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Budget: {formatCurrency(budgetSummary.budgetedNetIncome)}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="line-items">Budget Line Items</TabsTrigger>
          <TabsTrigger value="vs-actual">Budget vs Actual</TabsTrigger>
          <TabsTrigger value="periods">Budget Periods</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Budget Overview</CardTitle>
              <CardDescription>
                {selectedPeriod
                  ? `Overview for ${selectedPeriod.name}`
                  : "Select a budget period to view overview"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedPeriod ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
                        Budget Period Details
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Period:</span>
                          <span className="text-gray-900 dark:text-gray-100">{selectedPeriod.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Start Date:</span>
                          <span className="text-gray-900 dark:text-gray-100">
                            {selectedPeriod.startDate.toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">End Date:</span>
                          <span className="text-gray-900 dark:text-gray-100">
                            {selectedPeriod.endDate.toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Status:</span>
                          <Badge className={getStatusBadgeColor(selectedPeriod.status || "")}>
                            {selectedPeriod.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {budgetSummary && (
                      <div>
                        <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
                          Performance Summary
                        </h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Revenue Variance:</span>
                            <span className={budgetSummary.revenueVariance >= 0 ? "text-green-600" : "text-red-600"}>
                              {formatCurrency(budgetSummary.revenueVariance)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Expense Variance:</span>
                            <span className={budgetSummary.expenseVariance <= 0 ? "text-green-600" : "text-red-600"}>
                              {formatCurrency(budgetSummary.expenseVariance)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Net Income Variance:</span>
                            <span className={budgetSummary.netIncomeVariance >= 0 ? "text-green-600" : "text-red-600"}>
                              {formatCurrency(budgetSummary.netIncomeVariance)}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    No budget period selected. Create a new budget period or select an existing one.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="line-items">
          {selectedPeriod ? (
            <BudgetLineItemsTable budgetPeriodId={selectedPeriod.id} />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  Select a budget period to manage line items.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="vs-actual">
          {selectedPeriod ? (
            <BudgetVsActualReport budgetPeriodId={selectedPeriod.id} />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  Select a budget period to view budget vs actual analysis.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="periods">
          <Card>
            <CardHeader>
              <CardTitle>Budget Periods</CardTitle>
              <CardDescription>
                Manage your budget periods and their settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {budgetPeriods.length > 0 ? (
                  budgetPeriods.map((period) => (
                    <div
                      key={period.id}
                      className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                    >
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                            {period.name}
                          </h3>
                          <Badge className={getStatusBadgeColor(period.status || "")}>
                            {period.status}
                          </Badge>
                          {period.isDefault && (
                            <Badge variant="outline">Default</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {period.startDate.toLocaleDateString()} - {period.endDate.toLocaleDateString()}
                        </p>
                        {period.description && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {period.description}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePeriodSelect(period)}
                        >
                          Select
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedPeriodForView(period.id);
                            setIsPeriodSheetOpen(true);
                          }}
                        >
                          Manage
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">
                      No budget periods created yet. Create your first budget period to get started.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Budget Period Dialog */}
      <CreateBudgetPeriodDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={loadBudgetData}
      />

      {/* Budget Template Sheet */}
      {selectedPeriod && (
        <BudgetTemplateSheet
          open={isTemplateSheetOpen}
          onOpenChange={setIsTemplateSheetOpen}
          budgetPeriodId={selectedPeriod.id}
          onTemplateApplied={loadBudgetData}
        />
      )}

      {/* Import Budget Dialog */}
      {selectedPeriod && (
        <ImportBudgetDialog
          open={isImportDialogOpen}
          onOpenChange={setIsImportDialogOpen}
          budgetPeriodId={selectedPeriod.id}
          onImportCompleted={loadBudgetData}
        />
      )}

      {/* Budget Period Management Sheet */}
      {selectedPeriod && (
        <BudgetPeriodSheet
          open={isPeriodSheetOpen}
          onOpenChange={setIsPeriodSheetOpen}
          budgetPeriodId={selectedPeriod.id}
          onPeriodUpdated={loadBudgetData}
        />
      )}
    </div>
  );
} 