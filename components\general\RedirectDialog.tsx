"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

interface RedirectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  redirectPath: string;
  redirectButtonText: string;
}

export function RedirectDialog({
  isOpen,
  onClose,
  title,
  description,
  redirectPath,
  redirectButtonText,
}: RedirectDialogProps) {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (!isOpen) {
      setCountdown(5); // Reset countdown when dialog is closed
      return;
    }

    if (countdown === 0) {
      router.push(redirectPath);
      onClose();
      return;
    }

    const timer = setTimeout(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [isOpen, countdown, router, redirectPath, onClose]);

  const handleRedirectNow = () => {
    router.push(redirectPath);
    onClose();
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
            <br />
            Redirecting in {countdown} seconds...
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <AlertDialogAction asChild>
            <Button onClick={handleRedirectNow}>{redirectButtonText}</Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}