"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { 
  Shield, Settings, Users, AlertTriangle, Activity, Clock, 
  Eye, FileText, Download, Filter, Search, 
  Lock, Unlock, Key, Globe, MapPin, Smartphone
} from "lucide-react";
import {
  RequirePermission,
  RequireOrgSettings,
  RequireBillingAccess,
  PermissionStatus,
} from "@/components/ui/PermissionGate";
import { FeatureGate } from "@/components/ui/FeatureGate";

interface AuditLogEntry {
  id: string;
  action: string;
  resourceType: string;
  resourceId?: string;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  createdAt: string;
  userId?: string;
  userName?: string;
}

interface SecurityAlert {
  id: string;
  action: string;
  description: string;
  riskLevel: 'critical' | 'high';
  createdAt: string;
  userId: string;
  userName: string;
  ipAddress: string;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'owner' | 'admin' | 'manager' | 'editor' | 'viewer';
  status: 'active' | 'pending' | 'suspended';
  lastActivity: string;
  permissions: string[];
}

export default function EnhancedOrgManagement() {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterRisk, setFilterRisk] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Organization settings state
  const [orgSettings, setOrgSettings] = useState({
    requireTwoFactor: false,
    sessionTimeout: 30,
    allowExternalSharing: false,
    auditRetention: 90,
    ipWhitelist: "",
    securityNotifications: true,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Mock data for demonstration
      setAuditLogs([
        {
          id: "1",
          action: "delete",
          resourceType: "transaction",
          description: "Deleted transaction worth $1,250",
          riskLevel: "critical",
          createdAt: new Date().toISOString(),
          userName: "John Doe",
          ipAddress: "*************",
        },
        {
          id: "2",
          action: "change_role",
          resourceType: "member",
          description: "Changed role from viewer to admin",
          riskLevel: "high",
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          userName: "Jane Smith",
          ipAddress: "*********",
        },
      ]);

      setSecurityAlerts([
        {
          id: "1",
          action: "Failed login attempts",
          description: "5 failed login attempts from unknown IP",
          riskLevel: "critical",
          createdAt: new Date().toISOString(),
          userId: "user1",
          userName: "Unknown",
          ipAddress: "************",
        }
      ]);

      setTeamMembers([
        {
          id: "1",
          name: "John Doe",
          email: "<EMAIL>",
          role: "admin",
          status: "active",
          lastActivity: new Date().toISOString(),
          permissions: ["financial:read", "financial:write", "team:manage"],
        },
        {
          id: "2",
          name: "Jane Smith",
          email: "<EMAIL>",
          role: "manager",
          status: "active",
          lastActivity: new Date(Date.now() - 1800000).toISOString(),
          permissions: ["financial:read", "reports:basic"],
        }
      ]);
    } catch (error) {
      console.error("Error loading organization data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getRiskBadgeColor = (risk: string) => {
    switch (risk) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'destructive';
      case 'admin': return 'destructive';
      case 'manager': return 'secondary';
      case 'editor': return 'outline';
      case 'viewer': return 'outline';
      default: return 'outline';
    }
  };

  const filteredAuditLogs = auditLogs.filter(log => {
    const matchesRisk = filterRisk === "all" || log.riskLevel === filterRisk;
    const matchesSearch = !searchTerm || 
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.resourceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.description?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesRisk && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Organization Management</h1>
          <p className="text-muted-foreground">
            Advanced security, permissions, and audit controls
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <FeatureGate feature="advanced_reports">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </FeatureGate>
        </div>
      </div>

      {/* Security Alerts */}
      {securityAlerts.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="flex items-center justify-between">
              <span>
                {securityAlerts.length} security alert{securityAlerts.length > 1 ? 's' : ''} require attention
              </span>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Members</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {teamMembers.filter(m => m.status === 'active').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {teamMembers.filter(m => m.status === 'pending').length} pending
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {securityAlerts.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last 24 hours
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Audit Events</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {auditLogs.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {auditLogs.filter(log => log.riskLevel === 'high' || log.riskLevel === 'critical').length} high-risk
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">94%</div>
                <p className="text-xs text-muted-foreground">
                  Excellent
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent High-Risk Activity</CardTitle>
              <CardDescription>Critical actions requiring attention</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {auditLogs
                    .filter(log => log.riskLevel === 'high' || log.riskLevel === 'critical')
                    .slice(0, 10)
                    .map((log) => (
                      <div key={log.id} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center space-x-3">
                          <Badge variant={getRiskBadgeColor(log.riskLevel)}>
                            {log.riskLevel}
                          </Badge>
                          <div>
                            <p className="font-medium">{log.action} {log.resourceType}</p>
                            <p className="text-sm text-muted-foreground">
                              {log.description || `${log.action} operation on ${log.resourceType}`}
                            </p>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(log.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-6">
          <RequirePermission permission="team:manage">
            <Card>
              <CardHeader>
                <CardTitle>Team Permissions</CardTitle>
                <CardDescription>Manage role-based access control for your organization</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {teamMembers.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div>
                          <p className="font-medium">{member.name}</p>
                          <p className="text-sm text-muted-foreground">{member.email}</p>
                        </div>
                        <Badge variant={getRoleColor(member.role)}>
                          {member.role}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="grid grid-cols-3 gap-1">
                          {member.permissions.slice(0, 6).map((permission) => (
                            <PermissionStatus key={permission} permission={permission} />
                          ))}
                        </div>
                        <Button variant="outline" size="sm">
                          <Key className="w-4 h-4 mr-2" />
                          Manage
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </RequirePermission>
        </TabsContent>

        {/* Audit Logs Tab */}
        <TabsContent value="audit" className="space-y-6">
          <RequirePermission permission="audit:view">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Audit Logs</CardTitle>
                    <CardDescription>Complete activity tracking and compliance monitoring</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Select value={filterRisk} onValueChange={setFilterRisk}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Risk</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search logs..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8 w-64"
                      />
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {filteredAuditLogs.map((log) => (
                      <div key={log.id} className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50">
                        <div className="flex items-center space-x-3">
                          <Badge variant={getRiskBadgeColor(log.riskLevel)}>
                            {log.riskLevel}
                          </Badge>
                          <div className="flex flex-col">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{log.action}</span>
                              <span className="text-muted-foreground">•</span>
                              <span className="text-sm">{log.resourceType}</span>
                              {log.resourceId && (
                                <>
                                  <span className="text-muted-foreground">•</span>
                                  <span className="text-sm text-muted-foreground">{log.resourceId}</span>
                                </>
                              )}
                            </div>
                            {log.description && (
                              <p className="text-sm text-muted-foreground">{log.description}</p>
                            )}
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                              {log.userName && (
                                <span className="flex items-center space-x-1">
                                  <Users className="w-3 h-3" />
                                  <span>{log.userName}</span>
                                </span>
                              )}
                              {log.ipAddress && (
                                <span className="flex items-center space-x-1">
                                  <Globe className="w-3 h-3" />
                                  <span>{log.ipAddress}</span>
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{new Date(log.createdAt).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </RequirePermission>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <RequireOrgSettings>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Security Alerts</CardTitle>
                  <CardDescription>Recent security incidents and notifications</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-3">
                      {securityAlerts.map((alert) => (
                        <div key={alert.id} className="p-3 border rounded-lg border-red-200 bg-red-50">
                          <div className="flex items-center justify-between">
                            <Badge variant="destructive">{alert.riskLevel}</Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(alert.createdAt).toLocaleString()}
                            </span>
                          </div>
                          <p className="font-medium mt-2">{alert.action}</p>
                          <p className="text-sm text-muted-foreground">{alert.description}</p>
                          <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-2">
                            <span>User: {alert.userName}</span>
                            <span>IP: {alert.ipAddress}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Active Sessions</CardTitle>
                  <CardDescription>Monitor current user sessions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {teamMembers
                      .filter(member => member.status === 'active')
                      .slice(0, 5)
                      .map((member) => (
                        <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div>
                              <p className="font-medium">{member.name}</p>
                              <p className="text-sm text-muted-foreground">
                                Last active: {new Date(member.lastActivity).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            <MapPin className="w-4 h-4 mr-2" />
                            Details
                          </Button>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </RequireOrgSettings>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <RequireOrgSettings>
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>Configure organization-wide security policies</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">
                      Mandatory 2FA for all organization members
                    </p>
                  </div>
                  <Switch
                    checked={orgSettings.requireTwoFactor}
                    onCheckedChange={(checked) =>
                      setOrgSettings(prev => ({ ...prev, requireTwoFactor: checked }))
                    }
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Session Timeout (minutes)</Label>
                  <Input
                    type="number"
                    value={orgSettings.sessionTimeout}
                    onChange={(e) =>
                      setOrgSettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))
                    }
                    className="w-32"
                  />
                  <p className="text-sm text-muted-foreground">
                    Automatically log out inactive users
                  </p>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Allow External Sharing</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable sharing reports with external users
                    </p>
                  </div>
                  <Switch
                    checked={orgSettings.allowExternalSharing}
                    onCheckedChange={(checked) =>
                      setOrgSettings(prev => ({ ...prev, allowExternalSharing: checked }))
                    }
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Audit Log Retention (days)</Label>
                  <Select
                    value={orgSettings.auditRetention.toString()}
                    onValueChange={(value) =>
                      setOrgSettings(prev => ({ ...prev, auditRetention: parseInt(value) }))
                    }
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 days</SelectItem>
                      <SelectItem value="90">90 days</SelectItem>
                      <SelectItem value="180">180 days</SelectItem>
                      <SelectItem value="365">1 year</SelectItem>
                      <SelectItem value="1095">3 years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>IP Whitelist</Label>
                  <Textarea
                    placeholder="Enter IP addresses or ranges, one per line"
                    value={orgSettings.ipWhitelist}
                    onChange={(e) =>
                      setOrgSettings(prev => ({ ...prev, ipWhitelist: e.target.value }))
                    }
                    className="h-24"
                  />
                  <p className="text-sm text-muted-foreground">
                    Restrict access to specific IP addresses
                  </p>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button variant="outline">Cancel</Button>
                  <Button>Save Settings</Button>
                </div>
              </CardContent>
            </Card>
          </RequireOrgSettings>
        </TabsContent>
      </Tabs>
    </div>
  );
} 