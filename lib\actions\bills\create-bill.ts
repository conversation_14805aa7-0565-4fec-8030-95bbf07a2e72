"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { bills, billItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { billFormSchema, type NewBill } from "@/lib/actions/bills/bill.schema";
import { and, eq } from "drizzle-orm";
import { randomUUID } from 'crypto';

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function createBill(billData: NewBill): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const validatedFields = billFormSchema.safeParse(billData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid data provided." };
    }

    const { lineItems, appliedCredit = 0, ...newBillData } = validatedFields.data;
    
    const total = lineItems.reduce((acc, item) => acc + item.quantity * item.unitPrice, 0);

    await db.transaction(async (tx) => {
      const [bill] = await tx
        .insert(bills)
        .values({
          id: randomUUID(),
          ...newBillData,
          subtotal: String(total),
          total: String(total),
          organizationId: orgId,
          amountPaid: "0.00",
          amountRemaining: String(total),
          overpaid: "0.00",
          tax: newBillData.tax?.toString() || "0.00",
        })
        .returning();

      const itemsToInsert = lineItems.map((item) => ({
        id: randomUUID(),
        billId: bill.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: String(item.unitPrice),
        total: String(item.quantity * item.unitPrice),
        productId: item.productId || null,
      }));

      await tx.insert(billItems).values(itemsToInsert);

      // Apply supplier credit if present
      if (appliedCredit > 0 && bill.vendorId) {
        // Find all overpaid bills for this vendor, oldest first
        const overpaidBills = await db.query.bills.findMany({
          where: and(
            eq(bills.vendorId, bill.vendorId),
            eq(bills.organizationId, orgId),
          ),
          orderBy: [bills.date],
        });
        let remainingCredit = appliedCredit;
        for (const b of overpaidBills) {
          let over = Number(b.overpaid);
          if (over > 0 && remainingCredit > 0) {
            const toApply = Math.min(over, remainingCredit);
            await tx.update(bills)
              .set({ overpaid: (over - toApply).toFixed(2) })
              .where(eq(bills.id, b.id));
            remainingCredit -= toApply;
          }
          if (remainingCredit <= 0) break;
        }
      }
    });

    revalidatePath("/bills");
    return { success: true, message: "Bill created successfully." };
  } catch (error) {
    return { success: false, message: error instanceof Error ? error.message : "Failed to create bill." };
  }
} 