"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { db } from "@/db/drizzle";
import { accounts, clients, vendors, products } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

/**
 * Get Business Context for Smart Defaults
 * Returns organization context for intelligent form pre-filling
 */
export async function getBusinessContext() {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    // This would load business context including:
    // - Default accounts
    // - Recent customers/vendors
    // - Tax settings
    // - Preferred payment methods
    // - Business type settings
    
    const [
      allAccounts,
      allClients,
      allVendors,
      allProducts
    ] = await Promise.all([
      db.query.accounts.findMany({ where: eq(accounts.organizationId, orgId) }),
      db.query.clients.findMany({ where: eq(clients.organizationId, orgId) }),
      db.query.vendors.findMany({ where: eq(vendors.organizationId, orgId) }),
      db.query.products.findMany({ where: eq(products.organizationId, orgId) }),
    ]);

    return {
      success: true,
      context: {
        accounts: allAccounts,
        clients: allClients,
        vendors: allVendors,
        products: allProducts,
        // Add more context as needed
      }
    };
  } catch (error) {
    console.error("Error getting business context:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 