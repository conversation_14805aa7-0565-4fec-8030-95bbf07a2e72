"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDescription } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle, DollarSign, TrendingUp, Building2, Zap, ShoppingCart, Users, Factory, Check } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { applyBudgetTemplate } from "@/lib/actions/budget/apply-budget-template";
import { getBudgetTemplates } from "@/lib/actions/budget/get-budget-templates";
import { toast } from "sonner";

interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  totalBudget: number;
  lineItems: Array<{
    accountType: string;
    accountNamePattern: string;
    suggestedName: string;
    budgetedAmount: number;
    percentage: number;
    notes: string;
  }>;
}

interface BudgetTemplateSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  budgetPeriodId: string;
  onTemplateApplied: () => void;
}

const categoryIcons: Record<string, any> = {
  "General": Building2,
  "Technology": Zap,
  "Retail": ShoppingCart,
  "Professional Services": Users,
  "Manufacturing": Factory,
};

const categoryColors: Record<string, string> = {
  "General": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  "Technology": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  "Retail": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  "Professional Services": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
  "Manufacturing": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

export default function BudgetTemplateSheet({
  open,
  onOpenChange,
  budgetPeriodId,
  onTemplateApplied,
}: BudgetTemplateSheetProps) {
  const [templates, setTemplates] = useState<BudgetTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<BudgetTemplate | null>(null);
  const [scaleFactor, setScaleFactor] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isApplying, setIsApplying] = useState(false);

  useEffect(() => {
    if (open) {
      loadTemplates();
    }
  }, [open]);

  const loadTemplates = async () => {
    setIsLoading(true);
    try {
      const templatesData = await getBudgetTemplates();
      setTemplates(templatesData);
      if (templatesData.length > 0) {
        setSelectedTemplate(templatesData[0]);
      }
    } catch (error) {
      console.error("Error loading templates:", error);
      toast.error("Failed to load budget templates");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplyTemplate = async () => {
    if (!selectedTemplate) return;

    setIsApplying(true);
    try {
      const result = await applyBudgetTemplate(
        budgetPeriodId,
        selectedTemplate.id,
        scaleFactor
      );

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success("Budget template applied successfully!");
        onTemplateApplied();
        onOpenChange(false);
        setSelectedTemplate(null);
        setScaleFactor(1);
      }
    } catch (error) {
      console.error("Error applying template:", error);
      toast.error("Failed to apply budget template");
    } finally {
      setIsApplying(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount * scaleFactor);
  };

  const groupedTemplates = templates.reduce((groups, template) => {
    const category = template.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(template);
    return groups;
  }, {} as Record<string, BudgetTemplate[]>);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="h-[90vh] max-w-none w-full p-6" aria-describedby="budget-template-description">
        <SheetHeader className="mb-4">
          <SheetTitle className="text-lg">Apply Budget Template</SheetTitle>
          <SheetDescription id="budget-template-description">
            Choose from professional templates to populate your budget with industry-standard categories and amounts.
          </SheetDescription>
        </SheetHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3">Loading templates...</span>
          </div>
        ) : (
          <div className="grid grid-cols-12 gap-6 h-[calc(90vh-140px)] mx-2">
            {/* Template Selection - Left Sidebar */}
            <div className="col-span-4">
              <div className="space-y-3">
                <div>
                  <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">Select Template</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">Choose a template to get started</p>
                </div>
                
                <ScrollArea className="h-[calc(90vh-200px)]">
                  <div className="space-y-2 pr-3">
                    {templates.map((template) => {
                      const IconComponent = categoryIcons[template.category] || Building2;
                      const isSelected = selectedTemplate?.id === template.id;
                      
                      return (
                        <Card
                          key={template.id}
                          className={`cursor-pointer transition-all duration-200 ${
                            isSelected
                              ? "ring-2 ring-blue-500 border-blue-300 bg-blue-50 dark:bg-blue-950/50 shadow-md"
                              : "hover:border-gray-300 hover:shadow-sm"
                          }`}
                          onClick={() => setSelectedTemplate(template)}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-center gap-2.5">
                              <div className="flex-shrink-0">
                                <div className={`p-1 rounded ${isSelected ? 'bg-blue-100 dark:bg-blue-900' : 'bg-gray-100 dark:bg-gray-800'}`}>
                                  <IconComponent className={`h-3.5 w-3.5 ${isSelected ? 'text-blue-600' : 'text-gray-600 dark:text-gray-400'}`} />
                                </div>
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-0.5">
                                  <h4 className="font-semibold text-xs text-gray-900 dark:text-gray-100 truncate pr-2">
                                    {template.name}
                                  </h4>
                                  {isSelected && (
                                    <Check className="h-3.5 w-3.5 text-blue-600 flex-shrink-0" />
                                  )}
                                </div>
                                
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-1 mb-1.5">
                                  {template.description}
                                </p>
                                
                                <div className="flex items-center justify-between">
                                  <Badge 
                                    variant="secondary" 
                                    className={`text-xs py-0 px-1.5 ${categoryColors[template.category] || categoryColors["General"]}`}
                                  >
                                    {template.category}
                                  </Badge>
                                  <span className="text-xs font-semibold text-gray-900 dark:text-gray-100">
                                    {formatCurrency(template.totalBudget)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </ScrollArea>
              </div>
            </div>

            {/* Template Preview - Main Content */}
            <div className="col-span-5">
              {selectedTemplate ? (
                                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Budget Breakdown</CardTitle>
                      <CardDescription className="text-xs">
                        {selectedTemplate.lineItems.length} items • {formatCurrency(selectedTemplate.totalBudget)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-0">
                      <ScrollArea className="h-[calc(90vh-260px)] px-6">
                        <div className="space-y-2">
                          {selectedTemplate.lineItems.map((item, index) => (
                            <div
                              key={index}
                              className="flex justify-between items-center p-2.5 bg-gray-50 dark:bg-gray-800/50 rounded border border-gray-200 dark:border-gray-700"
                            >
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-0.5">
                                  <Badge
                                    variant={item.accountType === "revenue" ? "default" : "secondary"}
                                    className="text-xs font-medium py-0 px-1.5"
                                  >
                                    {item.accountType}
                                  </Badge>
                                  <span className="font-semibold text-xs text-gray-900 dark:text-gray-100 truncate">{item.suggestedName}</span>
                                </div>
                                <p className="text-xs text-gray-600 dark:text-gray-400 mb-0.5 line-clamp-1">
                                  {item.notes}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">
                                  "{item.accountNamePattern}"
                                </p>
                              </div>
                              <div className="text-right ml-3 flex-shrink-0">
                                <div className="font-bold text-sm text-gray-900 dark:text-gray-100">
                                  {formatCurrency(item.budgetedAmount)}
                                </div>
                                <div className="text-xs text-gray-600 dark:text-gray-400">
                                  {(item.percentage * scaleFactor).toFixed(1)}%
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                      <Building2 className="h-10 w-10 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Select a template to preview</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Choose from the templates on the left to see detailed breakdown</p>
                  </div>
                </div>
              )}
            </div>

            {/* Configuration & Actions - Right Sidebar */}
            <div className="col-span-3">
              {selectedTemplate && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Configuration</CardTitle>
                      <CardDescription className="text-xs">Customize before applying</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <Label htmlFor="scaleFactor" className="text-xs font-medium">Scale Factor</Label>
                        <Input
                          id="scaleFactor"
                          type="number"
                          value={scaleFactor}
                          onChange={(e) => setScaleFactor(parseFloat(e.target.value) || 1)}
                          step="0.1"
                          min="0.1"
                          max="10"
                          className="mt-1 h-8"
                        />
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                          Multiply all amounts by this factor
                        </p>
                      </div>

                      <Separator />

                      <div className="space-y-1.5">
                        <h4 className="font-medium text-xs text-gray-900 dark:text-gray-100">Summary</h4>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Original:</span>
                            <span className="font-medium">{formatCurrency(selectedTemplate.totalBudget / scaleFactor)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Factor:</span>
                            <span className="font-medium">{scaleFactor}x</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between">
                            <span className="font-semibold text-gray-900 dark:text-gray-100">Total:</span>
                            <span className="font-bold text-blue-600">{formatCurrency(selectedTemplate.totalBudget)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Items:</span>
                            <span className="font-medium">{selectedTemplate.lineItems.length}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950 py-2">
                    <AlertCircle className="h-3.5 w-3.5 text-blue-600" />
                    <AlertDescription className="text-xs text-blue-800 dark:text-blue-200">
                      Template matches accounts by name patterns. Non-matching accounts will be skipped.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-1.5">
                    <Button
                      onClick={handleApplyTemplate}
                      disabled={isApplying}
                      className="w-full h-8 text-xs"
                    >
                      {isApplying ? (
                        <div className="flex items-center space-x-1.5">
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                          <span>Applying...</span>
                        </div>
                      ) : (
                        `Apply Template`
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => onOpenChange(false)}
                      disabled={isApplying}
                      className="w-full h-8 text-xs"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
} 