"use server";

import { getTrialBalance } from "./get-trial-balance";
import { getBalanceSheet } from "./get-balance-sheet";
import { getIncomeStatement } from "./get-income-statement";
import { getCashFlowStatement } from "./get-cash-flow-statement";
import { getARAgingSummary } from "./get-ar-aging-summary";
import { getARAgingDetail } from "./get-ar-aging-detail";
import { getAPAgingSummary } from "./get-ap-aging-summary";
import { getAPAgingDetail } from "./get-ap-aging-detail";
import { getBudgetVsActual } from "./get-budget-vs-actual";
import { getVendorPerformanceReport } from "./get-vendor-performance-report";
import { getExpenseAnalysisReport } from "./get-expense-analysis-report";

export async function getReportingData(fromDate?: string, toDate?: string, budgetPeriodId?: string) {
    const [
        trialBalance,
        balanceSheet,
        incomeStatement,
        cashFlowStatement,
        arAgingSummary,
        arAgingDetail,
        apAgingSummary,
        apAgingDetail,
        vendorPerformance,
        expenseAnalysis,
    ] = await Promise.all([
        getTrialBalance(fromDate, toDate),
        getBalanceSheet(toDate),
        getIncomeStatement(fromDate, toDate),
        getCashFlowStatement(fromDate, toDate),
        getARAgingSummary(toDate),
        getARAgingDetail(toDate),
        getAPAgingSummary(toDate),
        getAPAgingDetail(toDate),
        getVendorPerformanceReport(fromDate, toDate),
        getExpenseAnalysisReport(fromDate, toDate),
    ]);

    // Handle budget vs actual separately to avoid affecting other reports
    let budgetVsActual = null;
    try {
        budgetVsActual = await getBudgetVsActual(budgetPeriodId);
    } catch (error) {
        console.warn("Budget vs actual data not available:", error);
        budgetVsActual = {
            period: null,
            report: [],
            totals: { budgeted: 0, actual: 0, variance: 0 }
        };
    }

    return {
        trialBalance,
        balanceSheet,
        incomeStatement,
        cashFlowStatement,
        arAgingSummary,
        arAgingDetail,
        apAgingSummary,
        apAgingDetail,
        budgetVsActual,
        vendorPerformance,
        expenseAnalysis,
    };
} 