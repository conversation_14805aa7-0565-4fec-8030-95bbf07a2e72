"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";



import { Separator } from "@/components/ui/separator";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { 
  DollarSign, 
  User, 
  Calendar, 
  CreditCard, 
  FileText, 
  Info,
  ArrowLeft,
  ArrowRight,
  Plus
} from "lucide-react";
import { moneyReceivedActionSchema, PaymentMethod } from "@/lib/actions/automation/types";
import { getClients } from "@/lib/actions/clients/get-clients";
import { getProducts } from "@/lib/actions/products/get-products";
import { getInvoices } from "@/lib/actions/invoices/get-invoices";
import { processCustomerPaymentReceived } from "@/lib/actions/automation/process-customer-payment-received";
import { toast } from "sonner";
import AddClient from "@/components/protectedPages/Clients/AddClient";
import { createClient } from "@/lib/actions/clients/create-client";
import { useQuery } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

interface CustomerPaymentFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const formSchema = moneyReceivedActionSchema.extend({
  date: z.string().min(1, "Date is required"),
  clientId: z.string().min(1, "Client is required"),
  productId: z.string().min(1, "Product/Service is required"),
  invoiceId: z.string().optional().nullable(),
  description: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

const paymentMethods: { value: PaymentMethod; label: string; description: string }[] = [
  { value: "cash", label: "Cash", description: "Physical cash payment" },
  { value: "credit_card", label: "Credit Card", description: "Credit or debit card payment" },
  { value: "bank_transfer", label: "Bank Transfer", description: "Electronic bank transfer" },
  { value: "check", label: "Check", description: "Paper check payment" },
  { value: "digital_wallet", label: "Digital Wallet", description: "PayPal, Venmo, etc." },
  { value: "financing", label: "Financing", description: "Payment plan or financing" },
  { value: "other", label: "Other", description: "Other payment method" },
];

export function CustomerPaymentForm({ onSubmit, onCancel, isLoading }: CustomerPaymentFormProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAddClientOpen, setIsAddClientOpen] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "money_received",
      date: new Date().toISOString().split('T')[0],
      amount: 0,
      description: "",
      clientId: "",
      productId: "",
      invoiceId: "",
    },
  });

  const handleCreateClient = async (clientData: any) => {
    try {
      const result = await createClient(clientData);
      if (result.success) {
        toast.success("Client created successfully!");
        setIsAddClientOpen(false);
        // Refresh clients list by invalidating the query
        queryClient.invalidateQueries({ queryKey: ["clientsForCustomerPayment"] });
      } else {
        toast.error(result.message || "Failed to create client");
      }
    } catch (error) {
      toast.error("Error creating client");
      console.error(error);
    }
  };

  const handleSubmit = async (data: FormData) => {
    setIsProcessing(true);
    
    try {
      // Convert string date to Date object
      const processedData = {
        ...data,
        date: new Date(data.date),
        description: data.description ?? "",
      };

      // Process the payment
      const result = await processCustomerPaymentReceived(processedData);
      
      if (result.success) {
        toast.success(result.message);
        onSubmit(processedData);
      } else {
        toast.error(result.error || "Failed to process payment");
      }
    } catch (error) {
      console.error("Error processing payment:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsProcessing(false);
    }
  };

  // Fetch dropdown data using React Query with optimization
  const { data: clients = [] } = useQuery({
    queryKey: ["clientsForCustomerPayment"],
    queryFn: getClients,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: products = [] } = useQuery({
    queryKey: ["productsForCustomerPayment"],
    queryFn: getProducts,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: invoices = [] } = useQuery({
    queryKey: ["invoicesForCustomerPayment"],
    queryFn: getInvoices,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  return (
    <div className="space-y-4">
      {/* Compact Header */}
      <div className="text-center mb-2">
        <div className="mx-auto w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-2">
          <DollarSign className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold mb-1">Customer Payment Received</h3>
        <p className="text-muted-foreground text-sm">
          Record a customer payment for products or services. We'll handle the accounting.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-2">
          <Card>
            <CardHeader className="pb-1">
              <CardTitle className="flex items-center gap-2 text-base">
                <DollarSign className="h-4 w-4" />
                Money Received
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {/* First row: Client, Date, Invoice */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-1">
                <FormField
                  control={form.control}
                  name="clientId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client *</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          if (value === "__add_new__") {
                            setIsAddClientOpen(true);
                            // Don't set the form value, just open the sheet
                          } else {
                            field.onChange(value);
                          }
                        }} 
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="max-w-xs">
                            <SelectValue placeholder="Select client" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {clients.map((client) => (
                            <SelectItem key={client.id} value={client.id}>{client.name}</SelectItem>
                          ))}
                          <SelectItem 
                            value="__add_new__" 
                            className="text-primary font-medium cursor-pointer"
                          >
                            <Plus className="h-4 w-4 mr-2 inline" />
                            + Add new Client
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="date"
                            className="pr-2 w-full max-w-xs"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="invoiceId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Invoice (optional)</FormLabel>
                      <Select
                        onValueChange={val => field.onChange(val === undefined ? null : val)}
                        value={field.value || undefined}
                      >
                        <FormControl>
                          <SelectTrigger className="max-w-xs">
                            <SelectValue placeholder="Invoice (optional)" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {invoices.map((invoice) => (
                            <SelectItem key={invoice.id} value={invoice.id}>
                              {invoice.invoiceNumber || invoice.id}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* Second row: Amount, Product/Service */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="number"
                            placeholder="0.00"
                            className="pr-2 w-full max-w-xs pl-8"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        How much did you receive?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="productId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product/Service *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="max-w-xs">
                            <SelectValue placeholder="Select product or service" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {products.map((product) => (
                            <SelectItem key={product.id} value={product.id}>{product.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* Description below (optional) */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe this transaction (optional)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Summary */}
          {form.watch("amount") > 0 && (
            <Card className="border-primary/20 bg-primary/5">
              <CardHeader>
                <CardTitle className="text-lg">Payment Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-semibold text-lg text-green-600">
                      ${form.watch("amount").toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Date:</span>
                    <span className="font-medium">{form.watch("date") || "Not specified"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Description:</span>
                    <span className="font-medium">{form.watch("description") || "Not specified"}</span>
                  </div>
                  <Separator className="my-3" />
                  <div className="text-sm text-muted-foreground">
                    <p className="font-medium mb-1">What will happen:</p>
                    <ul className="space-y-1">
                      <li>✅ Your cash/bank account will increase by ${form.watch("amount").toFixed(2)}</li>
                      <li>✅ Revenue will be recorded (or receivables reduced if paying an invoice)</li>
                      <li>✅ Your financial reports will update automatically</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
              disabled={isProcessing || isLoading}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={isProcessing || isLoading}
            >
              {isProcessing ? "Processing..." : "Record Payment"}
              {!isProcessing && <ArrowRight className="h-4 w-4 ml-2" />}
            </Button>
          </div>
        </form>
      </Form>

      {/* Add Client Dialog */}
      <AddClient
        open={isAddClientOpen}
        onOpenChange={setIsAddClientOpen}
        onSubmit={handleCreateClient}
        clientId={null}
        loading={false}
      />
    </div>
  );
} 