"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Dialog<PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle, DollarSign, TrendingUp, Building2, Zap, ShoppingCart, Users, Factory } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { applyBudgetTemplate, getBudgetTemplates } from "@/lib/actions/budget.actions";
import { toast } from "sonner";

interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  totalBudget: number;
  lineItems: Array<{
    accountType: string;
    accountNamePattern: string;
    suggestedName: string;
    budgetedAmount: number;
    percentage: number;
    notes: string;
  }>;
}

interface BudgetTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  budgetPeriodId: string;
  onTemplateApplied: () => void;
}

const categoryIcons: Record<string, any> = {
  "General": Building2,
  "Technology": Zap,
  "Retail": ShoppingCart,
  "Professional Services": Users,
  "Manufacturing": Factory,
};

export default function BudgetTemplateDialog({
  open,
  onOpenChange,
  budgetPeriodId,
  onTemplateApplied,
}: BudgetTemplateDialogProps) {
  const [templates, setTemplates] = useState<BudgetTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<BudgetTemplate | null>(null);
  const [scaleFactor, setScaleFactor] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isApplying, setIsApplying] = useState(false);

  // Load templates when dialog opens
  useEffect(() => {
    if (open) {
      loadTemplates();
    }
  }, [open]);

  const loadTemplates = async () => {
    setIsLoading(true);
    try {
      const templatesData = await getBudgetTemplates();
      setTemplates(templatesData);
    } catch (error) {
      console.error("Error loading templates:", error);
      toast.error("Failed to load budget templates");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplyTemplate = async () => {
    if (!selectedTemplate) return;

    setIsApplying(true);
    try {
      const result = await applyBudgetTemplate(
        budgetPeriodId,
        selectedTemplate.id,
        scaleFactor
      );

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(result.message);
        onTemplateApplied();
        onOpenChange(false);
        setSelectedTemplate(null);
        setScaleFactor(1);
      }
    } catch (error) {
      console.error("Error applying template:", error);
      toast.error("Failed to apply budget template");
    } finally {
      setIsApplying(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount * scaleFactor);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Apply Budget Template</DialogTitle>
          <DialogDescription>
            Choose a pre-built budget template and customize it for your business needs.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(90vh-120px)]">
          {/* Template Selection */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-3">Select Template</h3>
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                  ) : (
                    templates.map((template) => {
                      const IconComponent = categoryIcons[template.category] || Building2;
                      const isSelected = selectedTemplate?.id === template.id;
                      
                      return (
                        <Card
                          key={template.id}
                          className={`cursor-pointer transition-all ${
                            isSelected
                              ? "ring-2 ring-blue-600 border-blue-200 bg-blue-50 dark:bg-blue-950"
                              : "hover:border-gray-300"
                          }`}
                          onClick={() => setSelectedTemplate(template)}
                        >
                          <CardHeader className="pb-2">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center space-x-2">
                                <IconComponent className="h-5 w-5 text-blue-600" />
                                <CardTitle className="text-base">{template.name}</CardTitle>
                              </div>
                              <Badge variant="outline">{template.category}</Badge>
                            </div>
                            <CardDescription className="text-sm">
                              {template.description}
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                Total Budget
                              </span>
                              <span className="font-semibold">
                                {formatCurrency(template.totalBudget)}
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })
                  )}
                </div>
              </ScrollArea>
            </div>

            {selectedTemplate && (
              <div className="space-y-4">
                <Separator />
                <div>
                  <Label htmlFor="scaleFactor">Scale Factor</Label>
                  <Input
                    id="scaleFactor"
                    type="number"
                    value={scaleFactor}
                    onChange={(e) => setScaleFactor(parseFloat(e.target.value) || 1)}
                    step="0.1"
                    min="0.1"
                    max="10"
                    className="mt-1"
                  />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Multiply all amounts by this factor (e.g., 1.5 for 150% of template amounts)
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Template Preview */}
          <div className="space-y-4">
            {selectedTemplate ? (
              <>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Template Preview</h3>
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle>{selectedTemplate.name}</CardTitle>
                        <Badge variant="outline">{selectedTemplate.category}</Badge>
                      </div>
                      <CardDescription>{selectedTemplate.description}</CardDescription>
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-green-600" />
                          <span>Total: {formatCurrency(selectedTemplate.totalBudget)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="h-4 w-4 text-blue-600" />
                          <span>{selectedTemplate.lineItems.length} accounts</span>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>
                </div>

                <ScrollArea className="h-[350px]">
                  <div className="space-y-2">
                    {selectedTemplate.lineItems.map((item, index) => (
                      <Card key={index} className="p-3">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <Badge
                                variant={item.accountType === "revenue" ? "default" : "secondary"}
                                className="text-xs"
                              >
                                {item.accountType}
                              </Badge>
                              <span className="font-medium text-sm">{item.suggestedName}</span>
                            </div>
                            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                              {item.notes}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-sm">
                              {formatCurrency(item.budgetedAmount)}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              {(item.percentage * scaleFactor).toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    The template will try to match accounts in your chart of accounts by name and type. 
                    Accounts that don't exist will be skipped.
                  </AlertDescription>
                </Alert>

                <div className="flex justify-end space-x-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={isApplying}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleApplyTemplate}
                    disabled={isApplying}
                  >
                    {isApplying ? "Applying..." : "Apply Template"}
                  </Button>
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                <div className="text-center">
                  <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a template to see preview</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 