"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and } from "drizzle-orm";
import { getBudgetLineItems } from "./get-budget-line-items";
import { getActualAmountsForPeriod } from "./get-actual-amounts-for-period";

export async function getBudgetVsActualData(budgetPeriodId?: string) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    let period;
    if (budgetPeriodId) {
      [period] = await db.select().from(budgetPeriods).where(and(eq(budgetPeriods.id, budgetPeriodId), eq(budgetPeriods.organizationId, organizationId))).limit(1);
    } else {
      [period] = await db.select().from(budgetPeriods).where(and(eq(budgetPeriods.organizationId, organizationId), eq(budgetPeriods.isDefault, true))).limit(1);
    }

    if (!period) {
      return { lineItems: [], summary: {}, period: "No active budget period" };
    }

    const budgetData = await getBudgetLineItems(period.id);
    const actualAmounts = await getActualAmountsForPeriod(period.startDate, period.endDate);

    const lineItems = budgetData.map((item) => {
        const budgeted = parseFloat(item.budgetedAmount) || 0;
        const actual = actualAmounts[item.accountId!] || 0;
        return {
            accountId: item.accountId,
            accountName: item.accountName || "Unknown Account",
            accountType: item.accountType || "expense",
            budgetedAmount: budgeted,
            actualAmount: actual,
            variance: actual - budgeted,
            variancePercent: budgeted !== 0 ? ((actual - budgeted) / budgeted) * 100 : 0,
        }
    });
    
    const summary = lineItems.reduce((acc, item) => {
        const type = item.accountType === 'revenue' ? 'Revenue' : 'Expenses';
        acc[`totalBudgeted${type}`] = (acc[`totalBudgeted${type}`] || 0) + (item.budgetedAmount || 0);
        acc[`totalActual${type}`] = (acc[`totalActual${type}`] || 0) + (item.actualAmount || 0);
        return acc;
    }, {} as any);

    // Ensure all summary values are properly initialized
    summary.totalBudgetedRevenue = summary.totalBudgetedRevenue || 0;
    summary.totalActualRevenue = summary.totalActualRevenue || 0;
    summary.totalBudgetedExpenses = summary.totalBudgetedExpenses || 0;
    summary.totalActualExpenses = summary.totalActualExpenses || 0;

    summary.budgetedNetIncome = summary.totalBudgetedRevenue - summary.totalBudgetedExpenses;
    summary.actualNetIncome = summary.totalActualRevenue - summary.totalActualExpenses;

    // Calculate variance amounts
    summary.revenueVariance = summary.totalActualRevenue - summary.totalBudgetedRevenue;
    summary.expenseVariance = summary.totalActualExpenses - summary.totalBudgetedExpenses;
    summary.netIncomeVariance = summary.actualNetIncome - summary.budgetedNetIncome;

    // Calculate variance percentages with proper null checks
    summary.revenueVariancePercent = summary.totalBudgetedRevenue !== 0 
      ? (summary.revenueVariance / summary.totalBudgetedRevenue) * 100 
      : 0;
    summary.expenseVariancePercent = summary.totalBudgetedExpenses !== 0 
      ? (summary.expenseVariance / summary.totalBudgetedExpenses) * 100 
      : 0;
    summary.netIncomeVariancePercent = summary.budgetedNetIncome !== 0 
      ? (summary.netIncomeVariance / summary.budgetedNetIncome) * 100 
      : 0;

    return {
      lineItems,
      summary,
      period: `${period.name} (${period.startDate.toLocaleDateString()} - ${period.endDate.toLocaleDateString()})`,
    };
  } catch (error) {
    console.error("Error fetching budget vs actual data:", error);
    return null;
  }
} 