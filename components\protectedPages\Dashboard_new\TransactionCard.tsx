import { Card, CardContent } from "@/components/ui/card";
import { DollarSign, Calendar } from "lucide-react";

export interface Transaction {
    id: string;
    type: "income" | "expense";
    amount: number;
    description: string;
    date: string;
    category: string;
  }
  
  interface TransactionCardProps {
    transaction?: Transaction;
  }
  
  const TransactionCard = ({
    transaction = {
      id: "1",
      type: "income",
      amount: 2500,
      description: "Website Development",
      date: "2024-04-15",
      category: "Freelance Work",
    },
  }: TransactionCardProps) => {
    const isIncome = transaction.type === "income";
  
    return (
      <Card className="w-full bg-black/30 border-white/10 hover:bg-black/40 transition-colors">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0 mb-2">
            <div className="flex items-center gap-2">
              <div
                className={`h-10 w-10 rounded-full flex items-center justify-center ${isIncome ? "bg-green-500/10" : "bg-red-500/10"}`}
              >
                <DollarSign
                  className={`h-5 w-5 ${isIncome ? "text-green-500" : "text-red-500"}`}
                />
              </div>
              <div>
                <p className="font-medium text-white">
                  {transaction.description}
                </p>
                <p className="text-sm text-gray-400">{transaction.category}</p>
              </div>
            </div>
            <div className="text-right">
              <p
                className={`text-lg font-semibold ${isIncome ? "text-green-500" : "text-red-500"}`}
              >
                {isIncome ? "+" : "-"}${transaction.amount.toLocaleString()}
              </p>
              <div className="flex items-center gap-1 text-sm text-gray-400">
                <Calendar className="h-4 w-4 text-gray-400" />
                {new Date(transaction.date).toLocaleDateString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };
  
  export default TransactionCard;
  