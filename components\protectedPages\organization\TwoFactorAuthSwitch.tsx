"use client";

import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { update2FAStatus } from "@/lib/actions/user/update-2fa-status";
import { useState, useTransition } from "react";

export function TwoFactorAuthSwitch({ isEnabled }: { isEnabled: boolean }) {
  const [is2FAEnabled, setIs2FAEnabled] = useState(isEnabled);
  const [isPending, startTransition] = useTransition();

  const handle2FAToggle = (checked: boolean) => {
    startTransition(async () => {
      const result = await update2FAStatus(checked);
      if (result.success) {
        setIs2FAEnabled(checked);
        toast.success(`Two-Factor Authentication has been ${checked ? "enabled" : "disabled"}.`);
      } else {
        toast.error(result.message || "An unexpected error occurred.");
      }
    });
  };

  return (
    <Switch
      id="2fa-switch"
      checked={is2FAEnabled}
      onCheckedChange={handle2FAToggle}
      disabled={isPending}
    />
  );
} 