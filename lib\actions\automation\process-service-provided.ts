"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";
import { getServerUserContext } from "@/lib/server-auth";
import { serviceProvidedSchema } from "@/lib/actions/automation/types";
import { createJournalAutomationService } from "@/lib/actions/automation/journal-automation";

// Response types
interface BusinessActionResponse {
  success: boolean;
  transactionId?: string;
  message: string;
  error?: string;
  warnings?: string[];
  journalEntries?: any[];
}

/**
 * Process Service Provided
 * Business-friendly action: "I provided services to a customer"
 */
export async function processServiceProvided(
  data: z.infer<typeof serviceProvidedSchema>
): Promise<BusinessActionResponse> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    // Validate input
    const validatedData = serviceProvidedSchema.parse(data);
    
    // Create journal automation service
    const journalService = await createJournalAutomationService(orgId);
    
    // Process the business action
    const result = await journalService.processBusinessAction(validatedData);
    
    if (result.success) {
      revalidatePath("/transactions");
      revalidatePath("/dashboard");
      revalidatePath("/projects");
      
      return {
        success: true,
        transactionId: result.transactionId,
        message: `Service "${validatedData.serviceName}" for ${validatedData.customerName} ($${validatedData.amount.toFixed(2)}) recorded successfully!`,
        warnings: result.warnings,
        journalEntries: result.journalEntries
      };
    } else {
      return {
        success: false,
        message: "Failed to process service",
        error: result.error
      };
    }
  } catch (error) {
    console.error("Error processing service provided:", error);
    return {
      success: false,
      message: "Failed to process service",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 