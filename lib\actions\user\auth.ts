"use server";
import { auth } from "@/lib/auth";
import { db } from "@/db/drizzle";
import { user } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

export async function signIn(email: string, password: string) {
    try {
        await auth.api.signInEmail({
            body: {
                email,
                password,
            },
        });

        // After successful sign-in, check if 2FA is enabled
        const userRow = await db.query.user.findFirst({
            where: eq(user.email, email),
        });

        if (userRow?.twoFactorEnabled) {
            // Trigger the 2FA flow
            await auth.api.sendTwoFactorOTP({ body: {} });
            return { success: true, requires2FA: true };
        }

        return { success: true };
    } catch (error) {
        if (error instanceof Error) {
            const message = error.message.toLowerCase();
            if (message.includes("invalid credentials") || message.includes("user not found")) {
                return { success: false, error: "Invalid email or password." };
            }
            if (message.includes("email not verified")) {
                return { success: false, error: "Please verify your email before signing in." };
            }
        }
        return { success: false, error: "An unexpected error occurred." };
    }
}

export async function signUp(values: {name: string, email: string, password: string}) {
  console.log("Server Action: 'signUp' has been invoked for:", values.email);
  try {
      await auth.api.signUpEmail({
          body: {
              name: values.name,
              email: values.email,
              password: values.password,
          }
      });
      console.log("Server Action: 'signUpEmail' completed successfully for:", values.email);
      return { success: true };
  } catch (error) {
      console.error("Server Action: 'signUp' caught an error:", error);
      if (error instanceof Error) {
          if (error.message.includes('UNIQUE_CONSTRAINT_VIOLATION') || error.message.toLowerCase().includes('unique constraint')) {
              return { success: false, error: 'An account with this email already exists.' };
          }
          return { success: false, error: error.message };
      }
      return { success: false, error: 'An unknown error occurred.' };
  }
} 