import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { deleteVendors } from "@/lib/actions/vendor.actions";
import type { Vendor } from "./columns";

interface DeleteVendorDialogProps {
  vendor: Vendor;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const DeleteVendorDialog = ({ vendor, open, onOpenChange, onSuccess }: DeleteVendorDialogProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleDelete = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      await deleteVendors([vendor.id]);
      setSuccess("Vendor deleted successfully.");
      if (onSuccess) onSuccess();
      setTimeout(() => {
        onOpenChange(false);
        setSuccess(null);
      }, 1200);
    } catch (err: any) {
      setError(err.message || "Failed to delete vendor.");
    }
    setLoading(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Delete Vendor</DialogTitle>
          <DialogDescription>
            This action cannot be undone. The vendor will be permanently removed from your records.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          Are you sure you want to delete <b>{vendor.name}</b>?
        </div>
        {error && <div className="text-red-500 text-sm">{error}</div>}
        {success && <div className="text-green-600 text-sm">{success}</div>}
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDelete} disabled={loading}>
            {loading ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteVendorDialog; 