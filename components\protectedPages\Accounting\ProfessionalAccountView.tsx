import React from "react";
import { Account } from "./columns";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ProfessionalAccountViewProps {
  accounts: Account[];
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (id: string) => void;
}

interface AccountGroup {
  section: string;
  sectionTitle: string;
  accounts: Account[];
  totalBalance: number;
}

export function ProfessionalAccountView({
  accounts,
  onEditAccount,
  onDeleteAccount,
}: ProfessionalAccountViewProps) {
  const [openSections, setOpenSections] = React.useState<Set<string>>(new Set([
    'current_assets',
    'current_liabilities',
    'operating_revenue',
    'operating_expenses'
  ]));

  const toggleSection = (section: string) => {
    const newOpenSections = new Set(openSections);
    if (newOpenSections.has(section)) {
      newOpenSections.delete(section);
    } else {
      newOpenSections.add(section);
    }
    setOpenSections(newOpenSections);
  };

  const groupAccountsBySection = (): AccountGroup[] => {
    const sectionTitles: Record<string, string> = {
      'current_assets': 'Current Assets',
      'non_current_assets': 'Non-Current Assets',
      'current_liabilities': 'Current Liabilities',
      'non_current_liabilities': 'Non-Current Liabilities',
      'equity': 'Equity',
      'operating_revenue': 'Operating Revenue',
      'other_revenue': 'Other Revenue',
      'cost_of_goods_sold': 'Cost of Goods Sold',
      'operating_expenses': 'Operating Expenses',
      'other_expenses': 'Other Expenses',
    };

    const grouped = accounts.reduce((acc, account) => {
      const section = account.financialStatementSection || 'other';
      if (!acc[section]) {
        acc[section] = [];
      }
      acc[section].push(account);
      return acc;
    }, {} as Record<string, Account[]>);

    return Object.entries(grouped)
      .map(([section, accounts]) => ({
        section,
        sectionTitle: sectionTitles[section] || section.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        accounts: accounts.sort((a, b) => (a.accountNumber || '').localeCompare(b.accountNumber || '')),
        totalBalance: 0, // Balance calculation would need to be done with journal entries
      }))
      .sort((a, b) => {
        const order = [
          'current_assets',
          'non_current_assets',
          'current_liabilities',
          'non_current_liabilities',
          'equity',
          'operating_revenue',
          'other_revenue',
          'cost_of_goods_sold',
          'operating_expenses',
          'other_expenses',
        ];
        return order.indexOf(a.section) - order.indexOf(b.section);
      });
  };

  const accountGroups = groupAccountsBySection();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getClassificationBadgeColor = (classification: string) => {
    switch (classification) {
      case 'current':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'non_current':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'operating':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'non_operating':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  return (
    <div className="space-y-4">
      {accountGroups.map((group) => (
        <Card key={group.section} className="w-full">
          <Collapsible
            open={openSections.has(group.section)}
            onOpenChange={() => toggleSection(group.section)}
          >
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {openSections.has(group.section) ? (
                      <ChevronDownIcon className="h-4 w-4" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4" />
                    )}
                    <CardTitle className="text-lg font-semibold">
                      {group.sectionTitle}
                    </CardTitle>
                    <Badge variant="secondary" className="ml-2">
                      {group.accounts.length} accounts
                    </Badge>
                  </div>
                  <div className="text-sm font-medium text-gray-600">
                    Total: {formatCurrency(group.totalBalance)}
                  </div>
                </div>
              </CardHeader>
            </CollapsibleTrigger>
            
            <CollapsibleContent>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {group.accounts.map((account) => (
                    <div
                      key={account.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <span className="font-medium text-sm text-gray-500 min-w-[80px]">
                            {account.accountNumber}
                          </span>
                          <span className="font-medium text-gray-900">
                            {account.name}
                          </span>
                          <div className="flex space-x-2">
                            <Badge 
                              variant="outline" 
                              className={getClassificationBadgeColor(account.classification || '')}
                            >
                              {account.classification?.replace('_', ' ')}
                            </Badge>
                            {account.accountGroup && (
                              <Badge variant="outline" className="text-xs">
                                {account.accountGroup.replace('_', ' ')}
                              </Badge>
                            )}
                          </div>
                        </div>
                        {account.description && (
                          <p className="text-sm text-gray-500 mt-1 ml-[92px]">
                            {account.description}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="font-medium text-gray-900">
                            {formatCurrency(0)}
                          </div>
                          <div className="text-xs text-gray-500">
                            {account.normalBalance} balance
                          </div>
                        </div>
                        
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditAccount(account)}
                            className="h-8 px-2"
                          >
                            Edit
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteAccount(account.id)}
                            className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {group.accounts.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No accounts in this category
                    </div>
                  )}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      ))}
    </div>
  );
}
