"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { getAccountBalances } from "./get-account-balances";

export async function getIncomeStatement(fromDate?: string, toDate?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;
  
    const accountsWithBalances = await getAccountBalances(organizationId, fromDate, toDate);
  
    const revenue = accountsWithBalances.filter((ab) => ab.account.type === "revenue");
    const expenses = accountsWithBalances.filter((ab) => ab.account.type === "expense");
  
    const totalRevenue = revenue.reduce((sum, ab) => sum + ab.balance, 0);
    const totalExpenses = expenses.reduce((sum, ab) => sum + ab.balance, 0);
    const netIncome = totalRevenue - totalExpenses;
  
    return {
      revenue,
      expenses,
      totalRevenue,
      totalExpenses,
      netIncome,
      period: `From ${fromDate || 'start'} to ${toDate || 'end'}`,
    };
} 