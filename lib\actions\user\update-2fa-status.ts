"use server";

import { db } from "@/db/drizzle";
import { user } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { headers } from "next/headers";

export async function update2FAStatus(isEnabled: boolean) {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;

  if (!userId) {
    throw new Error("User not authenticated");
  }

  try {
    await db
      .update(user)
      .set({ twoFactorEnabled: isEnabled })
      .where(eq(user.id, userId));

    revalidatePath("/settings/profile");

    return { success: true };
  } catch (error) {
    console.error("Failed to update 2FA status:", error);
    return { success: false, message: "Could not update your setting. Please try again." };
  }
} 