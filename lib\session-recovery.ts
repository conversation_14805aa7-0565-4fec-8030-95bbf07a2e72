"use server";

import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { db } from "@/db/drizzle";
import { session } from "@/db/schema/schema";
import { eq, and, gt } from "drizzle-orm";

interface SessionRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  fallbackToCache?: boolean;
}

/**
 * Enhanced session retrieval with recovery mechanisms
 */
export async function getSessionWithRecovery(options: SessionRecoveryOptions = {}) {
  const { maxRetries = 3, retryDelay = 1000, fallbackToCache = true } = options;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const session = await auth.api.getSession({
        headers: await headers()
      });

      if (session?.user?.id) {
        return session;
      }

      // If no session found, try to recover from database
      if (fallbackToCache) {
        const recoveredSession = await attemptSessionRecovery();
        if (recoveredSession) {
          return recoveredSession;
        }
      }

      return null;
    } catch (error) {
      console.warn(`Session retrieval attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        console.error("All session recovery attempts failed");
        throw error;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  return null;
}

/**
 * Attempt to recover session from database when auth.api.getSession fails
 */
async function attemptSessionRecovery() {
  try {
    const headersList = await headers();
    const sessionCookie = headersList.get('cookie')?.match(/session=([^;]+)/)?.[1];
    
    if (!sessionCookie) {
      return null;
    }

    // Query database directly for session
    const [sessionData] = await db
      .select()
      .from(session)
      .where(
        and(
          eq(session.token, sessionCookie),
          gt(session.expiresAt, new Date())
        )
      );

    if (!sessionData) {
      return null;
    }

    // Reconstruct session object
    return {
      user: {
        id: sessionData.userId,
        // Add other user fields as needed
      },
      expiresAt: sessionData.expiresAt,
      // Add other session fields as needed
    };
  } catch (error) {
    console.error("Session recovery failed:", error);
    return null;
  }
}

/**
 * Enhanced server user context with session recovery
 */
export async function getServerUserContextWithRecovery() {
  try {
    const session = await getSessionWithRecovery({
      maxRetries: 3,
      retryDelay: 1000,
      fallbackToCache: true
    });

    if (!session?.user?.id) {
      redirect('/signin');
    }

    // Continue with existing logic...
    // This would integrate with your existing getServerUserContext logic
    return session;
  } catch (error) {
    console.error("Session recovery failed completely:", error);
    redirect('/signin');
  }
}

/**
 * Check if session is about to expire and refresh if needed
 */
export async function checkAndRefreshSession() {
  try {
    const session = await auth.api.getSession({
      headers: await headers()
    });

    if (!session) {
      return false;
    }

    // Check if session expires in the next hour
    const expiresAt = new Date(session.session?.expiresAt || 0);
    const oneHourFromNow = new Date(Date.now() + 60 * 60 * 1000);

    if (expiresAt < oneHourFromNow) {
      // Session is expiring soon, trigger refresh
      // Note: refreshSession might not be available in better-auth
      // We'll let the session update naturally via updateAge
      console.log("Session expires soon, will be refreshed automatically");
      return true;
    }

    return true;
  } catch (error) {
    console.error("Session refresh failed:", error);
    return false;
  }
}

/**
 * Graceful session validation with error handling
 */
export async function validateSessionGracefully() {
  try {
    const session = await auth.api.getSession({
      headers: await headers()
    });

    return {
      isValid: !!session?.user?.id,
      session,
      error: null
    };
  } catch (error) {
    console.error("Session validation failed:", error);
    
    return {
      isValid: false,
      session: null,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 