# Smart Accounting Automation Engine: Technical Implementation

**Parent Plan**: `app-overhaul.md`  
**Dependencies**: All phases  
**Priority**: Critical - Core automation logic  

## Overview

This document details the exact mechanics of how business actions automatically generate accurate journal entries, handle edge cases, and maintain accounting integrity.

## Core Automation Architecture

### 1. **Transaction Intent Processing Engine**

```typescript
// lib/services/automation-engine.ts
export class AutomationEngine {
  async processBusinessAction(
    action: BusinessAction,
    context: BusinessContext
  ): Promise<AutomationResult> {
    
    // 1. Validate business action
    const validation = await this.validateBusinessAction(action, context);
    if (!validation.isValid) {
      return { success: false, errors: validation.errors };
    }
    
    // 2. Resolve all required accounts
    const accounts = await this.resolveAccounts(action, context);
    if (!accounts.isComplete) {
      return await this.handleMissingAccounts(accounts, action, context);
    }
    
    // 3. Generate journal entries
    const entries = await this.generateJournalEntries(action, accounts, context);
    
    // 4. Validate double-entry accuracy
    const balanceCheck = this.validateBalance(entries);
    if (!balanceCheck.isBalanced) {
      throw new Error(`Journal entries don't balance: ${balanceCheck.difference}`);
    }
    
    // 5. Execute transaction atomically
    return await this.executeTransaction(action, entries, context);
  }
}
```

### 2. **Smart Account Resolution System**

```typescript
export class AccountResolutionService {
  async resolveAccounts(
    action: BusinessAction,
    context: BusinessContext
  ): Promise<ResolvedAccounts> {
    
    const resolver = this.getResolverForAction(action.type);
    const requiredAccounts = resolver.getRequiredAccounts(action);
    const resolvedAccounts: ResolvedAccounts = {};
    
    for (const accountType of requiredAccounts) {
      const account = await this.resolveAccount(accountType, action, context);
      if (!account) {
        // Try to create missing account automatically
        account = await this.createMissingAccount(accountType, action, context);
      }
      resolvedAccounts[accountType] = account;
    }
    
    return resolvedAccounts;
  }
  
  private async resolveAccount(
    accountType: string,
    action: BusinessAction,
    context: BusinessContext
  ): Promise<Account | null> {
    
    switch (accountType) {
      case 'revenue':
        if (action.type === 'RECORD_SALE' && action.productId) {
          const product = await this.getProduct(action.productId);
          return product.revenueAccount;
        }
        break;
        
      case 'cogs':
        if (action.type === 'RECORD_SALE' && action.productId) {
          const product = await this.getProduct(action.productId);
          return product.cogsAccount;
        }
        break;
        
      case 'accounts_receivable':
        return await this.getStandardAccount(context.organizationId, '1200');
        
      case 'cash':
        return await this.getStandardAccount(context.organizationId, '1000');
        
      case 'sales_tax_payable':
        return await this.getOrCreateTaxAccount(context.organizationId, context.taxJurisdiction);
    }
    
    return null;
  }
}
```

## Missing Critical Components

### 3. **Tax Calculation Engine**

```typescript
export class TaxCalculationService {
  async calculateTaxes(
    action: BusinessAction,
    context: BusinessContext
  ): Promise<TaxCalculation> {
    
    const taxConfig = await this.getTaxConfiguration(context.organizationId);
    const customerLocation = await this.getCustomerLocation(action.clientId);
    const businessLocation = context.organization.address;
    
    // Determine tax jurisdiction and rates
    const jurisdiction = this.determineTaxJurisdiction(businessLocation, customerLocation);
    const taxRates = await this.getTaxRates(jurisdiction);
    
    let taxCalculation: TaxCalculation = {
      subtotal: action.amount,
      taxes: [],
      total: action.amount
    };
    
    // Calculate applicable taxes
    if (taxRates.salesTax > 0) {
      const salesTax = action.amount * (taxRates.salesTax / 100);
      taxCalculation.taxes.push({
        type: 'sales_tax',
        rate: taxRates.salesTax,
        amount: salesTax,
        accountId: await this.getTaxAccount('sales_tax_payable', context.organizationId)
      });
      taxCalculation.total += salesTax;
    }
    
    return taxCalculation;
  }
}
```

### 4. **Inventory Management Integration**

```typescript
export class InventoryAutomationService {
  async processInventoryTransaction(
    action: BusinessAction,
    context: BusinessContext
  ): Promise<InventoryResult> {
    
    if (action.type === 'RECORD_SALE' && action.productId) {
      return await this.processSaleInventoryUpdate(action, context);
    }
    
    if (action.type === 'BUY_INVENTORY' && action.productId) {
      return await this.processPurchaseInventoryUpdate(action, context);
    }
    
    return { inventoryUpdated: false };
  }
  
  private async processSaleInventoryUpdate(
    action: SaleAction,
    context: BusinessContext
  ): Promise<InventoryResult> {
    
    const product = await this.getProduct(action.productId);
    
    if (product.type !== 'physical_product') {
      return { inventoryUpdated: false };
    }
    
    // Check inventory availability
    const currentInventory = await this.getCurrentInventory(action.productId);
    if (currentInventory.quantity < action.quantity) {
      throw new Error(`Insufficient inventory: ${currentInventory.quantity} available, ${action.quantity} requested`);
    }
    
    // Calculate COGS using FIFO/LIFO/Average cost method
    const cogsCalculation = await this.calculateCOGS(action.productId, action.quantity);
    
    // Update inventory quantities
    await this.updateInventoryQuantity(action.productId, -action.quantity);
    
    // Return COGS information for journal entries
    return {
      inventoryUpdated: true,
      cogsAmount: cogsCalculation.totalCost,
      inventoryReduction: cogsCalculation.totalCost,
      cogsEntries: cogsCalculation.entries
    };
  }
  
  private async calculateCOGS(
    productId: string,
    quantitySold: number
  ): Promise<COGSCalculation> {
    
    // Get inventory lots (FIFO method)
    const inventoryLots = await this.getInventoryLots(productId);
    
    let remainingQuantity = quantitySold;
    let totalCost = 0;
    const cogsEntries = [];
    
    for (const lot of inventoryLots) {
      if (remainingQuantity <= 0) break;
      
      const quantityFromLot = Math.min(lot.quantity, remainingQuantity);
      const costFromLot = quantityFromLot * lot.unitCost;
      
      totalCost += costFromLot;
      remainingQuantity -= quantityFromLot;
      
      cogsEntries.push({
        lotId: lot.id,
        quantity: quantityFromLot,
        unitCost: lot.unitCost,
        totalCost: costFromLot
      });
    }
    
    if (remainingQuantity > 0) {
      throw new Error(`Insufficient inventory: ${remainingQuantity} units still needed`);
    }
    
    return { totalCost, entries: cogsEntries };
  }
}
```

### 5. **Multi-Payment Handling**

```typescript
export class PaymentProcessingService {
  async processPayment(
    action: BusinessAction,
    paymentDetails: PaymentDetails
  ): Promise<PaymentResult> {
    
    if (paymentDetails.method === 'split') {
      return await this.processSplitPayment(action, paymentDetails);
    }
    
    if (paymentDetails.method === 'partial') {
      return await this.processPartialPayment(action, paymentDetails);
    }
    
    return await this.processSinglePayment(action, paymentDetails);
  }
  
  private async processSplitPayment(
    action: BusinessAction,
    paymentDetails: SplitPaymentDetails
  ): Promise<PaymentResult> {
    
    const journalEntries = [];
    
    // Process each payment method
    for (const payment of paymentDetails.payments) {
      const paymentAccount = await this.resolvePaymentAccount(payment.method);
      
      journalEntries.push({
        accountId: paymentAccount.id,
        debitAmount: payment.amount,
        creditAmount: 0,
        description: `Payment via ${payment.method}: ${action.description}`
      });
    }
    
    // Credit the revenue account
    const revenueAccount = await this.getRevenueAccount(action);
    journalEntries.push({
      accountId: revenueAccount.id,
      debitAmount: 0,
      creditAmount: action.amount,
      description: `Revenue: ${action.description}`
    });
    
    return { entries: journalEntries, fullyPaid: true };
  }
}
```

### 6. **Error Recovery and Validation Framework**

```typescript
export class AutomationValidationService {
  async validateBeforeProcessing(
    action: BusinessAction,
    context: BusinessContext
  ): Promise<ValidationResult> {
    
    const validations = [
      this.validateAccountMappings(action, context),
      this.validateInventoryAvailability(action, context),
      this.validateCreditLimits(action, context),
      this.validateBusinessRules(action, context),
      this.validateTaxConfiguration(action, context)
    ];
    
    const results = await Promise.all(validations);
    const errors = results.flatMap(r => r.errors);
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings: results.flatMap(r => r.warnings)
    };
  }
  
  async handleAutomationFailure(
    action: BusinessAction,
    error: AutomationError,
    context: BusinessContext
  ): Promise<FailureRecovery> {
    
    // Log the failure for admin review
    await this.logAutomationFailure(action, error, context);
    
    // Attempt automatic recovery
    if (error.type === 'missing_account') {
      const account = await this.createMissingAccount(error.accountType, context);
      if (account) {
        // Retry automation with new account
        return { recovery: 'retry', newAccount: account };
      }
    }
    
    if (error.type === 'insufficient_inventory') {
      // Convert to backorder or suggest alternatives
      return { recovery: 'backorder', suggestedActions: ['create_backorder', 'suggest_alternatives'] };
    }
    
    // Fall back to manual entry mode
    return {
      recovery: 'manual_entry',
      template: await this.generateManualEntryTemplate(action, context),
      guidance: this.getManualEntryGuidance(error)
    };
  }
}
```

### 7. **Organization Setup and Validation Service**

```typescript
export class OrganizationSetupService {
  async validateAutomationReadiness(organizationId: string): Promise<SetupValidation> {
    
    // 1. Validate Chart of Accounts completeness
    const coaValidation = await this.validateChartOfAccounts(organizationId);
    if (!coaValidation.isComplete) {
      await this.createMissingStandardAccounts(organizationId, coaValidation.missing);
    }
    
    // 2. Validate business configuration
    const businessProfile = await this.getBusinessProfile(organizationId);
    await this.validateBusinessConfiguration(organizationId, businessProfile);
    
    // 3. Set up tax configuration if needed
    await this.configureTaxSettings(organizationId, businessProfile);
    
    return {
      coaComplete: coaValidation.isComplete,
      businessConfigured: true,
      taxConfigured: true,
      readyForAutomation: true // Clean database = ready to go!
    };
  }
  
  async createMissingStandardAccounts(
    organizationId: string, 
    businessType: string
  ): Promise<Account[]> {
    
    // Create standard accounts based on business type
    const standardAccounts = this.getStandardAccountsForBusiness(businessType);
    const createdAccounts = [];
    
    for (const accountTemplate of standardAccounts) {
      const existing = await this.findAccount(organizationId, accountTemplate.number);
      if (!existing) {
        const account = await this.createAccount(organizationId, accountTemplate);
        createdAccounts.push(account);
      }
    }
    
    return createdAccounts;
  }
}
```

## Implementation Strategy

### Phase 1A: Core Automation Engine (Add to Products Phase)
- [ ] Basic account resolution service
- [ ] Simple journal entry generation
- [ ] Validation framework foundation

### Phase 2A: Advanced Automation (Add to Transactions Phase)  
- [ ] Tax calculation engine
- [ ] Inventory integration
- [ ] Multi-payment handling
- [ ] Error recovery system

### Phase 4A: Organization Setup and Performance (Add to Integration Phase)
- [ ] Organization setup validation service
- [ ] Automation readiness assessment
- [ ] Performance optimization
- [ ] Monitoring and alerting

## Success Metrics for Automation

- **Accuracy**: 99.9%+ of automated entries are correct
- **Coverage**: 95%+ of transactions can be automated
- **Performance**: < 500ms for automation processing
- **Recovery**: < 5% of automations require manual intervention
- **Data Integrity**: Zero accounting principle violations

---

This detailed automation engine ensures we deliver on the promise of intelligent, accurate, and reliable automated accounting while handling real-world complexity and edge cases. 