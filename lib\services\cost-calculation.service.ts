import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { eq, and } from "drizzle-orm";

// Define a more specific Product type for our service
type Product = {
  id: string;
  price: number | null;
  costBasis: number | null;
  laborCostPerHour: number | null;
  overheadRate: number | null;
  productType: 'service' | 'physical_product' | 'digital_product' | 'subscription' | 'bundle';
};

// Context for calculations that might need external data
// For now, it's simple, but it can be expanded
type CostContext = {
  hoursEstimate?: number;
};

// A structured response for cost calculations
export type ProductCost = {
  directCost: number;
  overheadCost: number;
  totalCost: number;
  profitMargin: number;
  profitMarginPercent: number;
  breakdown: { name: string; value: number }[];
};

export class CostCalculationService {
  private organizationId: string;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
  }

  public async getProductCost(
    productId: string,
    context: CostContext = {}
  ): Promise<ProductCost | null> {
    const product = await this.fetchProduct(productId);
    if (!product) {
      return null;
    }

    switch (product.productType) {
      case "service":
        return this.calculateServiceCost(product, context);
      case "physical_product":
        return this.calculateInventoryCost(product);
      // Other cases can be added here
      default:
        // Default to a simple cost calculation if type-specific logic doesn't exist
        const price = product.price || 0;
        const cost = product.costBasis || 0;
        return {
          directCost: cost,
          overheadCost: 0,
          totalCost: cost,
          profitMargin: price - cost,
          profitMarginPercent: price > 0 ? ((price - cost) / price) * 100 : 0,
          breakdown: [{ name: "Direct Cost", value: cost }],
        };
    }
  }

  private async fetchProduct(productId: string): Promise<Product | null> {
    const result = await db
      .select({
        id: products.id,
        price: products.price,
        costBasis: products.costBasis,
        laborCostPerHour: products.laborCostPerHour,
        overheadRate: products.overheadRate,
        productType: products.type,
      })
      .from(products)
      .where(and(eq(products.id, productId), eq(products.organizationId, this.organizationId)))
      .limit(1);

    if (result.length === 0) return null;

    // The schema stores decimals as strings, so we need to parse them
    const rawProduct = result[0];
    return {
      ...rawProduct,
      price: rawProduct.price ? parseFloat(rawProduct.price) : null,
      costBasis: rawProduct.costBasis ? parseFloat(rawProduct.costBasis) : null,
      laborCostPerHour: rawProduct.laborCostPerHour ? parseFloat(rawProduct.laborCostPerHour) : null,
      overheadRate: rawProduct.overheadRate ? parseFloat(rawProduct.overheadRate) : null,
    } as Product;
  }

  private calculateServiceCost(product: Product, context: CostContext): ProductCost {
    const price = product.price || 0;
    const laborCost = (product.laborCostPerHour || 0) * (context.hoursEstimate || 1); // Default to 1 hour
    const overheadCost = laborCost * ((product.overheadRate || 0) / 100);
    const totalCost = laborCost + overheadCost;

    return {
      directCost: laborCost,
      overheadCost: overheadCost,
      totalCost: totalCost,
      profitMargin: price - totalCost,
      profitMarginPercent: price > 0 ? ((price - totalCost) / price) * 100 : 0,
      breakdown: [
        { name: "Direct Labor", value: laborCost },
        { name: "Overhead", value: overheadCost },
      ],
    };
  }

  private calculateInventoryCost(product: Product): ProductCost {
    const price = product.price || 0;
    const cost = product.costBasis || 0;

    return {
      directCost: cost,
      overheadCost: 0, // Overhead for inventory is often handled differently, e.g., in COGS
      totalCost: cost,
      profitMargin: price - cost,
      profitMarginPercent: price > 0 ? ((price - cost) / price) * 100 : 0,
      breakdown: [{ name: "Material Cost", value: cost }],
    };
  }
} 