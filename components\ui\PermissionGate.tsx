"use client";

import { useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Lock, Shield, UserX, AlertTriangle } from "lucide-react";
import { FeatureGate, UpgradePrompt } from "@/components/ui/FeatureGate";

interface PermissionGateProps {
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  requireFeatureAccess?: boolean; // Also check feature gates
}

export function PermissionGate({
  permission,
  children,
  fallback,
  showUpgradePrompt = true,
  requireFeatureAccess = true,
}: PermissionGateProps) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [reason, setReason] = useState<string>("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkPermission() {
      try {
        const response = await fetch("/api/permissions/check", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            permission,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setHasAccess(data.allowed);
          setReason(data.reason || "");
        } else {
          setHasAccess(false);
          setReason("Unable to verify permission");
        }
      } catch (error) {
        console.error("Error checking permission:", error);
        setHasAccess(false);
        setReason("Error checking permission");
      } finally {
        setLoading(false);
      }
    }

    checkPermission();
  }, [permission]);

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (hasAccess) {
    // If permission is granted, still check feature access if required
    if (requireFeatureAccess) {
      return (
        <FeatureGate feature={getFeatureForPermission(permission)} fallback={fallback}>
          {children}
        </FeatureGate>
      );
    }
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return <PermissionDeniedPrompt reason={reason} permission={permission} />;
  }

  return null;
}

export function PermissionDeniedPrompt({
  reason,
  permission,
}: {
  reason: string;
  permission: string;
}) {
  const getIcon = () => {
    if (reason.includes("role")) {
      return <Shield className="w-5 h-5" />;
    }
    if (reason.includes("deactivated")) {
      return <UserX className="w-5 h-5" />;
    }
    return <Lock className="w-5 h-5" />;
  };

  const getTitle = () => {
    if (reason.includes("role")) {
      return "Insufficient Permissions";
    }
    if (reason.includes("deactivated")) {
      return "Organization Deactivated";
    }
    return "Access Denied";
  };

  return (
    <Card className="border-dashed border-orange-200 bg-orange-50">
      <CardHeader className="text-center pb-2">
        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
          {getIcon()}
        </div>
        <CardTitle className="text-lg text-orange-900">{getTitle()}</CardTitle>
        <CardDescription className="text-orange-700">{reason}</CardDescription>
      </CardHeader>
      <CardContent className="text-center space-y-3">
        <Badge variant="outline" className="text-xs border-orange-300 text-orange-700">
          Permission Required: {permission}
        </Badge>
        <p className="text-sm text-orange-600">
          Contact your administrator to request access to this feature.
        </p>
      </CardContent>
    </Card>
  );
}

// Helper function to map permissions to features for backward compatibility
function getFeatureForPermission(permission: string): string {
  const permissionToFeatureMap: Record<string, string> = {
    'financial:read': 'basic_reports',
    'financial:write': 'manual_transactions',
    'financial:export': 'advanced_reports',
    'invoices:read': 'basic_reports',
    'invoices:write': 'manual_transactions',
    'invoices:send': 'manual_transactions',
    'team:view': 'basic_reports',
    'team:invite': 'projects',
    'team:manage': 'projects',
    'org:settings:view': 'basic_reports',
    'org:settings:edit': 'advanced_reports',
    'org:billing:view': 'advanced_reports',
    'org:billing:manage': 'advanced_reports',
    'reports:basic': 'basic_reports',
    'reports:advanced': 'advanced_reports',
    'reports:export': 'advanced_reports',
  };

  return permissionToFeatureMap[permission] || 'basic_reports';
}

// Convenient permission-specific components
export function RequirePermission({
  permission,
  children,
  fallback,
}: {
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <PermissionGate permission={permission} fallback={fallback}>
      {children}
    </PermissionGate>
  );
}

export function RequireTeamManagement({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <PermissionGate permission="team:manage" fallback={fallback}>
      {children}
    </PermissionGate>
  );
}

export function RequireFinancialWrite({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <PermissionGate permission="financial:write" fallback={fallback}>
      {children}
    </PermissionGate>
  );
}

export function RequireOrgSettings({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <PermissionGate permission="org:settings:edit" fallback={fallback}>
      {children}
    </PermissionGate>
  );
}

export function RequireBillingAccess({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <PermissionGate permission="org:billing:view" fallback={fallback}>
      {children}
    </PermissionGate>
  );
}

// Permission status display component
export function PermissionStatus({ permission }: { permission: string }) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkPermission() {
      try {
        const response = await fetch("/api/permissions/check", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({ permission }),
        });

        if (response.ok) {
          const data = await response.json();
          setHasAccess(data.allowed);
        } else {
          setHasAccess(false);
        }
      } catch (error) {
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    }

    checkPermission();
  }, [permission]);

  if (loading) {
    return <Badge variant="outline">Checking...</Badge>;
  }

  return (
    <Badge variant={hasAccess ? "default" : "destructive"}>
      {hasAccess ? "Granted" : "Denied"}
    </Badge>
  );
}

// Role-based gates (for backward compatibility with existing system)
export function RequireRole({
  roles,
  children,
  fallback,
}: {
  roles: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkRole() {
      try {
        const response = await fetch("/api/permissions/user-info", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          setHasAccess(roles.includes(data.role));
        } else {
          setHasAccess(false);
        }
      } catch (error) {
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    }

    checkRole();
  }, [roles]);

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    );
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  return fallback ? <>{fallback}</> : null;
} 