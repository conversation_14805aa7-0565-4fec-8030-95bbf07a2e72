"use client";

import React, { useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { columns, Invoice } from "./columns";
import { InvoicesDataTable } from "./invoices-data-table";
import AddInvoiceSheet from "./AddInvoiceSheet";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  deleteInvoices,
} from "@/lib/actions/invoices/delete-invoices";
import { deleteInvoice } from "@/lib/actions/invoices/delete-invoice";
import { updateInvoice } from "@/lib/actions/invoices/update-invoice";
import { createInvoice } from "@/lib/actions/invoices/create-invoice";
import { getInvoices } from "@/lib/actions/invoices/get-invoices";
import { getClients } from "@/lib/actions/clients/get-clients";
import { createClient } from "@/lib/actions/clients/create-client";
import { NewClient } from "@/lib/actions/clients/client.schema";
import BulkActionDialog from "./BulkActionDialog";
import { invoiceFormSchema } from "@/lib/actions/invoices/invoice.schema";
import { z } from "zod";
import AddClient from "@/components/protectedPages/Clients/AddClient";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";

type InvoiceFormData = z.infer<typeof invoiceFormSchema>;

export default function InvoicesPageClient() {
  const queryClient = useQueryClient();
  const [selected, setSelected] = React.useState<string[]>([]);
  const [bulkDialogOpen, setBulkDialogOpen] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [addClientSheetOpen, setAddClientSheetOpen] = React.useState(false);
  const [editingInvoiceId, setEditingInvoiceId] = React.useState<string | null>(null);

  const { data: invoices = [], isLoading: isLoadingInvoices } = useQuery({
    queryKey: ["invoices"],
    queryFn: getInvoices,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });

  const { data: clients = [], isLoading: isLoadingClients } = useQuery({
    queryKey: ["clientsForInvoices"],
    queryFn: getClients,
    retry: 1, // Limit retries to 1
    retryDelay: 1000, // Wait 1 second between retries
    staleTime: 5 * 60 * 1000, // Keep data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnReconnect: false, // Prevent refetch on reconnect
  });
  
  const { mutate: mutateCreateUpdate, isPending: isCreatingOrUpdating } = useMutation({
    mutationFn: async (data: { id?: string; formData: InvoiceFormData }) => {
      try {
        const result = data.id ? await updateInvoice(data.id, data.formData) : await createInvoice(data.formData);
        return result;
      } catch (error) {
        console.error("Mutation error:", error);
        throw error;
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["invoices"] });
        setDialogOpen(false);
        setEditingInvoiceId(null);
      } else {
        toast.error(result.message || "An unexpected error occurred.");
      }
    },
    onError: (error: any) => {
      console.error("Mutation error:", error);
      toast.error(error.message || "Failed to save invoice.");
    },
  });

  const { mutate: mutateDelete, isPending: isDeleting } = useMutation({
    mutationFn: (id: string) => deleteInvoice(id),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["invoices"] });
      } else {
        toast.error(result.message || "Failed to delete invoice.");
      }
    },
  });
  
  const { mutate: mutateBulkDelete, isPending: isBulkDeleting } = useMutation({
    mutationFn: (ids: string[]) => deleteInvoices(ids),
    onSuccess: (result) => {
      if (result.success) {
        toast.success(result.message);
        queryClient.invalidateQueries({ queryKey: ["invoices"] });
        setSelected([]);
      } else {
        toast.error(result.message || "Failed to delete invoices.");
      }
      setBulkDialogOpen(false);
    },
  });

  const { mutate: mutateAddClient, isPending: isAddingClient } = useMutation({
    mutationFn: (clientData: NewClient) => createClient(clientData),
    onSuccess: (result) => {
      if (result.success) {
        toast.success("Client created successfully.");
        queryClient.invalidateQueries({ queryKey: ["clientsForInvoices"] });
        setAddClientSheetOpen(false);
      } else {
        toast.error(result.message || "Failed to create client.");
      }
    },
  });

  const handleSubmit = (data: InvoiceFormData) => {
    mutateCreateUpdate({ id: editingInvoiceId ?? undefined, formData: data });
  };

  const handleDelete = (invoiceId: string) => {
    mutateDelete(invoiceId);
  };
  
  const handleBulkDelete = () => {
    mutateBulkDelete(selected);
  };

  const handleAddClient = (clientData: NewClient) => {
    mutateAddClient(clientData);
  };

  const openEditDialog = (invoice: Invoice) => {
    setEditingInvoiceId(invoice.id);
    setDialogOpen(true);
  };

  const openNewDialog = () => {
    setEditingInvoiceId(null);
    setDialogOpen(true);
  };

  const columnsWithActions = useMemo(
    () =>
      columns.map((column) => {
        if (column.id === "actions") {
          return {
            ...column,
            cell: ({ row }: any) => (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(row.original)}>
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDelete(row.original.id)}>
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ),
          };
        }
        return column;
      }),
    []
  );

  if (isLoadingInvoices || isLoadingClients) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Invoices</h1>
        <Button onClick={openNewDialog}>Add Invoice</Button>
      </div>

      {selected.length > 0 && (
        <div className="flex gap-2">
          <Button onClick={() => setBulkDialogOpen(true)} variant="outline">
            Bulk Actions
          </Button>
          <span className="text-sm self-center text-muted-foreground">
            {selected.length} selected
          </span>
        </div>
      )}

      <InvoicesDataTable
        columns={columnsWithActions}
        data={invoices}
        selected={selected}
        setSelected={setSelected}
      />

      <AddInvoiceSheet
        isOpen={dialogOpen}
        onClose={() => {
          setEditingInvoiceId(null);
          setDialogOpen(false);
        }}
        onSubmit={handleSubmit}
        invoiceId={editingInvoiceId}
        isPending={isCreatingOrUpdating}
      />

      <AddClient
        open={addClientSheetOpen}
        onOpenChange={setAddClientSheetOpen}
        onSubmit={handleAddClient}
        clientId={null}
        loading={isAddingClient}
      />

      <BulkActionDialog
        isOpen={bulkDialogOpen}
        onClose={() => setBulkDialogOpen(false)}
        onConfirm={handleBulkDelete}
        count={selected.length}
        isPending={isBulkDeleting}
      />
    </div>
  );
} 