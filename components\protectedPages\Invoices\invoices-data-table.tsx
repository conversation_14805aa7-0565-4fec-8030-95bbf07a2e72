"use client";

import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { type ColumnDef } from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { invoices } from "@/db/schema/schema";

type Invoice = typeof invoices.$inferSelect;

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  selected: string[];
  setSelected: (ids: string[]) => void;
  highlightedRowId?: string | null;
}

export function InvoicesDataTable<TData extends { id: string }, TValue>({
  columns,
  data,
  selected,
  setSelected,
  highlightedRowId,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Select all logic
  const allIds = data.map((row) => row.id);
  const allSelected = allIds.length > 0 && allIds.every((id) => selected.includes(id));
  const someSelected = allIds.some((id) => selected.includes(id));
  const toggleAll = () => {
    if (allSelected) setSelected([]);
    else setSelected(allIds);
  };
  const toggleOne = (id: string) => {
    if (selected.includes(id)) setSelected(selected.filter((sid) => sid !== id));
    else setSelected([...selected, id]);
  };

  return (
    <div role="region" aria-label="Invoices Table">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup, i) => (
              <TableRow key={headerGroup.id}>
                {i === 0 && (
                  <TableHead>
                    <input
                      type="checkbox"
                      checked={allSelected}
                      ref={el => { if (el) el.indeterminate = !allSelected && someSelected; }}
                      onChange={toggleAll}
                      aria-label="Select all invoices"
                      className="focus-visible:ring-2 focus-visible:ring-primary outline-none"
                    />
                  </TableHead>
                )}
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody aria-live="polite">
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className={highlightedRowId === row.original.id ? "bg-nextgen-primary/10 animate-pulse" : ""}
                >
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selected.includes(row.original.id)}
                      onChange={() => toggleOne(row.original.id)}
                      aria-label={selected.includes(row.original.id) ? "Deselect invoice" : "Select invoice"}
                      className="focus-visible:ring-2 focus-visible:ring-primary outline-none"
                    />
                  </TableCell>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length + 1} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>
      {/* TODO: Add mobile responsiveness and swipe actions if required by PRD */}
    </div>
  );
}
