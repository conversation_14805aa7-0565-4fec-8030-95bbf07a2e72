"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, sql } from "drizzle-orm";

/**
 * Get profitability metrics for dashboard
 */
export async function getProfitabilityMetrics() {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    
    // Get basic metrics
    const [metricsResult] = await db
      .select({
        totalProducts: sql<number>`COUNT(*)`,
        profitableProducts: sql<number>`COUNT(CASE WHEN ${products.totalRevenue} - ${products.totalCosts} > 0 THEN 1 END)`,
        totalRevenue: sql<number>`COALESCE(SUM(${products.totalRevenue}), 0)`,
        totalCosts: sql<number>`COALESCE(SUM(${products.totalCosts}), 0)`,
        averageMargin: sql<number>`
          CASE 
            WHEN SUM(${products.totalRevenue}) > 0 
            THEN (SUM(${products.totalRevenue}) - SUM(${products.totalCosts})) * 100.0 / SUM(${products.totalRevenue})
            ELSE 0 
          END
        `,
      })
      .from(products)
      .where(eq(products.organizationId, orgId));

    // Get best and worst performers
    const bestPerformer = await db
      .select()
      .from(products)
      .where(eq(products.organizationId, orgId))
      .orderBy(sql`(${products.totalRevenue} - ${products.totalCosts}) DESC`)
      .limit(1);

    const worstPerformer = await db
      .select()
      .from(products)
      .where(eq(products.organizationId, orgId))
      .orderBy(sql`(${products.totalRevenue} - ${products.totalCosts}) ASC`)
      .limit(1);

    return {
      ...metricsResult,
      totalProfit: (metricsResult?.totalRevenue || 0) - (metricsResult?.totalCosts || 0),
      bestPerformer: bestPerformer[0] || null,
      worstPerformer: worstPerformer[0] || null,
    };
  } catch (error) {
    console.error("Failed to fetch profitability metrics:", error);
    return {
      totalProducts: 0,
      profitableProducts: 0,
      totalRevenue: 0,
      totalCosts: 0,
      totalProfit: 0,
      averageMargin: 0,
      bestPerformer: null,
      worstPerformer: null,
    };
  }
} 