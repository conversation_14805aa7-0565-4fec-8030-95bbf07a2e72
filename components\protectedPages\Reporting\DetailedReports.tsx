"use client";
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

interface DetailedReportsProps {
  invoices: Array<{
    id: string;
    invoice_number: string;
    due_date: string | Date | null;
    total: number;
    status: string;
    client: { id: string; name: string };
  }>;
}

const DetailedReports = ({ invoices }: DetailedReportsProps) => {
  // Calculate aging buckets
  const agingBuckets: Record<string, number> = {
    current: 0,
    "1-30": 0,
    "31-60": 0,
    "61-90": 0,
    "90+": 0,
  };

  const now = new Date();
  invoices.forEach((invoice) => {
    if (invoice.status === "paid") return;
    const dueDate = invoice.due_date ? new Date(invoice.due_date) : null;
    if (!dueDate) return;
    const daysDiff = Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff <= 0) {
      agingBuckets.current += invoice.total;
    } else if (daysDiff <= 30) {
      agingBuckets["1-30"] += invoice.total;
    } else if (daysDiff <= 60) {
      agingBuckets["31-60"] += invoice.total;
    } else if (daysDiff <= 90) {
      agingBuckets["61-90"] += invoice.total;
    } else {
      agingBuckets["90+"] += invoice.total;
    }
  });

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-black/50 border-white/10">
        <h2 className="text-xl font-semibold text-white mb-4">Accounts Receivable Aging</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Aging Bucket</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="text-gray-300">
            {Object.entries(agingBuckets).map(([bucket, amount]) => (
              <TableRow key={bucket}>
                <TableCell>
                  <Badge variant="outline" className={getBucketColor(bucket)}>
                    {bucket}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">${amount.toFixed(2)}</TableCell>
              </TableRow>
            ))}
            <TableRow className="font-bold text-white">
              <TableCell>Total Outstanding</TableCell>
              <TableCell className="text-right">
                ${Object.values(agingBuckets).reduce((sum, amount) => sum + amount, 0).toFixed(2)}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Card>
      <Card className="p-6 bg-black/50 border-white/10">
        <h2 className="text-xl font-semibold text-white mb-4">Outstanding Invoices</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Invoice #</TableHead>
              <TableHead>Client</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Days Overdue</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="text-gray-300">
            {invoices
              .filter((inv) => inv.status !== "paid")
              .sort((a, b) => {
                const aDue = a.due_date ? new Date(a.due_date).getTime() : 0;
                const bDue = b.due_date ? new Date(b.due_date).getTime() : 0;
                return aDue - bDue;
              })
              .map((invoice) => {
                const dueDate = invoice.due_date ? new Date(invoice.due_date) : null;
                const daysDiff = dueDate ? Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
                return (
                  <TableRow key={invoice.id}>
                    <TableCell>{invoice.invoice_number}</TableCell>
                    <TableCell>{invoice.client.name}</TableCell>
                    <TableCell>{dueDate ? format(dueDate, "MMM d, yyyy") : "-"}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getDaysOverdueColor(daysDiff)}>
                        {daysDiff <= 0 ? "Not Due" : `${daysDiff} days`}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">${invoice.total.toFixed(2)}</TableCell>
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
};

function getBucketColor(bucket: string) {
  switch (bucket) {
    case "current":
      return "bg-green-100 text-green-800";
    case "1-30":
      return "bg-yellow-100 text-yellow-800";
    case "31-60":
      return "bg-orange-100 text-orange-800";
    case "61-90":
      return "bg-red-100 text-red-800";
    case "90+":
      return "bg-red-200 text-red-900";
    default:
      return "";
  }
}

function getDaysOverdueColor(days: number) {
  if (days <= 0) return "bg-green-100 text-green-800";
  if (days <= 30) return "bg-yellow-100 text-yellow-800";
  if (days <= 60) return "bg-orange-100 text-orange-800";
  if (days <= 90) return "bg-red-100 text-red-800";
  return "bg-red-200 text-red-900";
}

export default DetailedReports; 