"use server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

export async function resendVerificationEmail() {
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;
  if (!user) return { error: "Not authenticated." };
  try {
    // Use the Better Auth API to send a verification email
    await auth.api.sendVerificationEmail({ body: { email: user.email } });
    return { success: true };
  } catch (error) {
    return { error: error instanceof Error ? error.message : String(error) };
  }
} 