"use server";

import { db } from "@/db/drizzle";
import { member } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq, and } from "drizzle-orm";
import { SecurityContext } from "./types";

/**
 * Get current user and organization context for audit logging
 */
export async function getAuditContext() {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;

  if (!userId) {
    return { userId: null, organizationId: null, sessionId: null };
  }

  const orgMember = await db
    .select({
      organizationId: member.organizationId,
    })
    .from(member)
    .where(
      and(
        eq(member.userId, userId),
      ),
    )
    .limit(1);

  return {
    userId,
    organizationId: orgMember[0]?.organizationId || null,
    sessionId: session?.session.id || null,
  };
}

/**
 * Extract security context from request headers
 */
export async function getSecurityContext(): Promise<SecurityContext> {
  const headersList = await headers();

  return {
    ipAddress:
      headersList.get("x-forwarded-for") ||
      headersList.get("x-real-ip") ||
      "unknown",
    userAgent: headersList.get("user-agent") || "unknown",
    // Location data would typically come from IP geolocation service
    location: {
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
  };
} 