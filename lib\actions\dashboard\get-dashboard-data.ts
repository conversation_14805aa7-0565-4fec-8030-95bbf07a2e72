"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { DateRange } from "react-day-picker";
import { db } from "@/db/drizzle";
import { organization } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

// Types based on component props
export interface Transaction {
  id: string;
  type: "income" | "expense";
  amount: number;
  description: string;
  date: string;
  category: string;
}

export interface Payment {
  id: string;
  clientName: string;
  amount: number;
  dueDate: string;
  status: "pending" | "overdue" | "upcoming";
}

export interface DashboardData {
  financialOverview: {
    monthlyEarnings: number;
    monthlyExpenses: number;
    monthlyGrowth: number;
    chartData: { date: string; income: number; expenses: number }[];
  };
  transactions: Transaction[];
  paymentReminders: Payment[];
}

export async function getDashboardData({
  dateRange,
}: {
  dateRange?: DateRange;
}): Promise<DashboardData> {
  try {
    // Get user context - this will throw if user is not authenticated
    const { user, organization } = await getServerUserContext();
    const organizationId = organization.id;
    const userId = user.id;

    // Verify RLS policies are working by doing a simple query
    try {
      const orgCheck = await db.select().from(organization).where(eq(organization.id, organizationId));
      
      if (orgCheck.length === 0) {
        throw new Error(`Organization not found or RLS policy denied access to organization ${organizationId}`);
      }
    } catch (rlsError) {
      throw new Error(`RLS policy check failed: ${rlsError instanceof Error ? rlsError.message : String(rlsError)}`);
    }

    // TODO: Replace with real data fetching logic from the database
    // You would query transactions, invoices, bills within the dateRange
    // and for the given organizationId to calculate these values.

    const chartData = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (30 - i));
      return {
        date: date.toISOString().split("T")[0],
        income: Math.floor(Math.random() * (2500 - 500 + 1)) + 500,
        expenses: Math.floor(Math.random() * (1500 - 300 + 1)) + 300,
      };
    });
    
    const monthlyEarnings = chartData.reduce((sum, d) => sum + d.income, 0);
    const monthlyExpenses = chartData.reduce((sum, d) => sum + d.expenses, 0);

    return {
      financialOverview: {
        monthlyEarnings,
        monthlyExpenses,
        monthlyGrowth: 15.6, // Placeholder
        chartData,
      },
      transactions: [
        {
          id: "1",
          type: "income",
          amount: 2500,
          description: "Website Development",
          date: "2024-07-15",
          category: "Client Project",
        },
        {
          id: "2",
          type: "expense",
          amount: 450,
          description: "Software Subscription",
          date: "2024-07-14",
          category: "Business Tools",
        },
      ],
      paymentReminders: [
        {
          id: "1",
          clientName: "Acme Corp",
          amount: 2500,
          dueDate: "2024-07-15",
          status: "pending",
        },
        {
          id: "2",
          clientName: "Tech Solutions",
          amount: 1800,
          dueDate: "2024-06-10",
          status: "overdue",
        },
      ],
    };
  } catch (error) {
    console.error("Error in getDashboardData:", error);
    throw error;
  }
} 