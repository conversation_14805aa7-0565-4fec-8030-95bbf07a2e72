import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, Minus, ChevronDown, ChevronRight } from "lucide-react";

interface Subcategory {
  name: string;
  amount: number;
  percent: number;
}

interface ExpenseCategory {
  category: string;
  currentPeriod: number;
  previousPeriod: number;
  budgeted: number;
  variance: number;
  variancePercent: number;
  percentOfTotal: number;
  trend: string;
  subcategories: Subcategory[];
}

interface ExpenseAnalysisData {
  categories: ExpenseCategory[];
  totalExpenses: number;
  totalBudgeted: number;
  totalVariance: number;
  totalVariancePercent: number;
  period: string;
}

interface Props {
  data: ExpenseAnalysisData;
}

export default function ExpenseAnalysisReport({ data }: Props) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(1)}%`;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "increasing":
        return <TrendingUp className="w-4 h-4 text-red-600" />;
      case "decreasing":
        return <TrendingDown className="w-4 h-4 text-green-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTrendBadge = (trend: string) => {
    switch (trend) {
      case "increasing":
        return <Badge className="bg-pink-500 text-white font-medium">Increasing</Badge>;
      case "decreasing":
        return <Badge className="bg-teal-500 text-white font-medium">Decreasing</Badge>;
      default:
        return <Badge className="bg-slate-500 text-white font-medium">Stable</Badge>;
    }
  };

  const getVarianceColor = (variance: number) => {
    // For expenses, negative variance (under budget) is good, positive is bad
    return variance > 0 ? "text-red-500 font-semibold" : variance < 0 ? "text-green-500 font-semibold" : "text-gray-400";
  };

  const getVarianceBadge = (variancePercent: number) => {
    const absPercent = Math.abs(variancePercent);
    if (absPercent > 15) {
      return variancePercent > 0 
        ? <Badge className="bg-red-500 text-white font-medium">Over Budget</Badge>
        : <Badge className="bg-green-500 text-white font-medium">Under Budget</Badge>;
    } else if (absPercent > 5) {
      return <Badge className="bg-orange-500 text-white font-medium">Moderate Variance</Badge>;
    }
    return <Badge className="bg-blue-500 text-white font-medium">On Track</Badge>;
  };

  const getPeriodChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const getPeriodChangeColor = (change: number) => {
    return change > 5 ? "text-red-500 font-medium" : change < -5 ? "text-green-500 font-medium" : "text-gray-400";
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-blue-600">Total Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.totalExpenses)}</div>
            <p className="text-xs text-gray-600">Current period</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-purple-600">Budgeted</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.totalBudgeted)}</div>
            <p className="text-xs text-gray-600">Total budget</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-orange-600">Variance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getVarianceColor(data.totalVariance)}`}>
              {formatCurrency(data.totalVariance)}
            </div>
            <p className="text-xs text-gray-600">{formatPercentage(data.totalVariancePercent)}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-green-600">Budget Utilization</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {((data.totalExpenses / data.totalBudgeted) * 100).toFixed(1)}%
            </div>
            <Progress value={(data.totalExpenses / data.totalBudgeted) * 100} className="h-2 mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Category Analysis Table */}
      <Card>
        <CardHeader>
          <CardTitle>Expense Category Analysis</CardTitle>
          <CardDescription>
            Detailed breakdown by category for {data.period}
          </CardDescription>
        </CardHeader>
        <CardContent>
                     <div className="relative overflow-hidden rounded-lg border border-gray-600">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-900 hover:bg-gray-900 border-b border-gray-700">
                  <TableHead className="font-semibold text-white">Category</TableHead>
                  <TableHead className="text-right font-semibold text-white">Current Period</TableHead>
                  <TableHead className="text-right font-semibold text-white">Previous Period</TableHead>
                  <TableHead className="text-right font-semibold text-white">Budgeted</TableHead>
                  <TableHead className="text-right font-semibold text-white">Variance</TableHead>
                  <TableHead className="text-center font-semibold text-white">% of Total</TableHead>
                  <TableHead className="text-center font-semibold text-white">Trend</TableHead>
                  <TableHead className="text-center font-semibold text-white">Status</TableHead>
                </TableRow>
              </TableHeader>
            <TableBody>
              {data.categories.map((category) => {
                const periodChange = getPeriodChange(category.currentPeriod, category.previousPeriod);
                const isExpanded = expandedCategories.has(category.category);
                return (
                  <React.Fragment key={category.category}>
                    <TableRow 
                      className={`cursor-pointer transition-colors duration-150 border-b border-gray-600 select-none
                        ${isExpanded ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-800 hover:bg-gray-700 active:bg-gray-600'}
                        data-[state=selected]:bg-transparent hover:data-[state=selected]:bg-gray-700`}
                      onClick={() => toggleCategory(category.category)}
                    >
                      <TableCell className="font-semibold text-white">
                        <div className="flex items-center space-x-2">
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4 text-gray-300" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-300" />
                          )}
                          <span>{category.category}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-semibold text-white">{formatCurrency(category.currentPeriod)}</TableCell>
                      <TableCell className="text-right">
                        <div className="space-y-1">
                          <div className="font-semibold text-white">{formatCurrency(category.previousPeriod)}</div>
                          <div className={`text-xs ${getPeriodChangeColor(periodChange)}`}>
                            {formatPercentage(periodChange)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-semibold text-white">{formatCurrency(category.budgeted)}</TableCell>
                      <TableCell className={`text-right ${getVarianceColor(category.variance)}`}>
                        <div className="flex items-center justify-end space-x-1">
                          {getTrendIcon(category.trend)}
                          <span>{formatCurrency(category.variance)}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="space-y-1">
                          <div>{category.percentOfTotal.toFixed(1)}%</div>
                          <Progress value={category.percentOfTotal} className="h-1" />
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {getTrendBadge(category.trend)}
                      </TableCell>
                      <TableCell className="text-center">
                        {getVarianceBadge(category.variancePercent)}
                      </TableCell>
                    </TableRow>
                                            {isExpanded && (
                      <TableRow className="hover:bg-gray-600">
                        <TableCell colSpan={8} className="bg-gray-700 border-l-4 border-l-blue-400 p-4 border-b border-gray-600">
                          <div className="space-y-3">
                            <h4 className="font-semibold text-sm text-gray-200">Subcategory Breakdown</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {category.subcategories.map((sub) => (
                                <div key={sub.name} className="flex justify-between items-center bg-gray-800 p-3 rounded-lg border border-gray-600 shadow-sm hover:shadow-md hover:bg-gray-750 transition-all">
                                  <span className="text-sm font-semibold text-gray-200">{sub.name}</span>
                                  <div className="text-right">
                                    <div className="text-sm font-bold text-white">{formatCurrency(sub.amount)}</div>
                                    <div className="text-xs text-blue-400 font-medium">{sub.percent.toFixed(1)}%</div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
          </div>
        </CardContent>
      </Card>

      {/* Insights and Recommendations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="border-red-600 bg-red-900/20">
          <CardHeader>
            <CardTitle className="text-sm font-semibold text-red-300">Categories Over Budget</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.categories
                .filter(cat => cat.variance > 0)
                .sort((a, b) => b.variancePercent - a.variancePercent)
                .slice(0, 3)
                .map((category) => (
                  <div key={category.category} className="flex justify-between items-center bg-gray-800 p-3 rounded-lg border border-red-600/50 hover:bg-gray-750 transition-colors">
                    <span className="text-sm font-semibold text-gray-200">{category.category}</span>
                    <div className="text-right">
                      <div className="text-sm font-bold text-red-400">{formatCurrency(category.variance)}</div>
                      <div className="text-xs text-red-300 font-medium">{formatPercentage(category.variancePercent)}</div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-orange-600 bg-orange-900/20">
          <CardHeader>
            <CardTitle className="text-sm font-semibold text-orange-300">Fastest Growing Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.categories
                .filter(cat => cat.trend === "increasing")
                .sort((a, b) => getPeriodChange(b.currentPeriod, b.previousPeriod) - getPeriodChange(a.currentPeriod, a.previousPeriod))
                .slice(0, 3)
                .map((category) => {
                  const growth = getPeriodChange(category.currentPeriod, category.previousPeriod);
                  return (
                    <div key={category.category} className="flex justify-between items-center bg-gray-800 p-3 rounded-lg border border-orange-600/50 hover:bg-gray-750 transition-colors">
                      <span className="text-sm font-semibold text-gray-200">{category.category}</span>
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4 text-orange-400" />
                        <span className="text-sm font-bold text-orange-400">{formatPercentage(growth)}</span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 