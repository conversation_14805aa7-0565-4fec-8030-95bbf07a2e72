"use server";

import { createTransaction } from "./create-transaction";

export async function importTransactions(imported: any[]) {
  for (const item of imported) {
    const formData = new FormData();
    formData.append("transactionType", item.transactionType);
    formData.append("amount", item.amount);
    formData.append("accountId", item.accountId);
    formData.append("date", item.date);
    formData.append("description", item.description);
    await createTransaction(formData);
  }
  return { success: true, message: "Import completed." };
} 