"use server";

import { db } from "@/db/drizzle";
import { bills } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, desc } from "drizzle-orm";
import { Bill } from "@/components/protectedPages/Bills/columns";

export async function getBills(): Promise<Bill[]> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const billList = await db.query.bills.findMany({
      where: eq(bills.organizationId, orgId),
      with: {
        vendor: {
          columns: {
            name: true,
          },
        },
      },
      orderBy: [desc(bills.date)],
    });

    return billList.map((b) => ({
      ...b,
      vendorName: b.vendor?.name ?? "N/A",
      vendorId: b.vendorId,
      overpaid: b.overpaid,
      total: b.total,
      status: b.status as "draft" | "submitted" | "approved" | "paid" | "void",
      date: new Date(b.date),
      dueDate: b.dueDate ? new Date(b.dueDate) : null,
    }));
  } catch (error) {
    console.error("Failed to fetch bills:", error);
    return [];
  }
} 