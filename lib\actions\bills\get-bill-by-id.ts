"use server";

import { db } from "@/db/drizzle";
import { bills } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";

export async function getBillById(billId: string) {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;
  const bill = await db.query.bills.findFirst({
    where: and(eq(bills.id, billId), eq(bills.organizationId, orgId)),
    with: {
      items: true,
    },
  });
  if (!bill) throw new Error("Bill not found or you do not have permission to view it.");
  const { items, ...billData } = bill;
  return { ...billData, lineItems: items };
} 