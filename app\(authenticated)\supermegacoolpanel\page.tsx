import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { SuperadminUserTable } from "@/components/general/SuperadminUserTable";
import { getAllUsersForSuperadmin } from "@/lib/actions/superadmin";
import { headers } from "next/headers";
import {
  activateUser,
  inactivateUser,
  deleteUser,
  resetUserPassword,
  impersonate<PERSON>ser,
  sendSystemEmail,
  getUserOrganizationsForSuperadmin,
  getUserDetailsForSuperadmin,
} from "@/lib/actions/superadmin";

const SUPERADMINS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

export default async function SuperAdminPanel() {
  const session = await auth.api.getSession({ headers: await headers() });
  if (!session || !SUPERADMINS.includes(session.user.email)) {
    redirect("/404");
  }
  const users = await getAllUsersForSuperadmin(session);

  // Remove the handlers object and only pass initialUsers
  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col w-full">
      <h1 className="text-3xl font-bold mb-6 px-8 pt-8">Superadmin Control Panel</h1>
      <div className="flex-1 w-full px-8 pb-8">
        <SuperadminUserTable
          initialUsers={users.map(u => ({
            ...u,
            createdAt: u.createdAt instanceof Date ? u.createdAt.toISOString() : u.createdAt,
          }))}
        />
      </div>
    </div>
  );
} 