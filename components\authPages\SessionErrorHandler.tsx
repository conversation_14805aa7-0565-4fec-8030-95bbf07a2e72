"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Clock, Shield } from 'lucide-react';

export function SessionErrorHandler() {
  const searchParams = useSearchParams();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [errorType, setErrorType] = useState<'expired' | 'error' | null>(null);

  useEffect(() => {
    const message = searchParams.get('message');
    
    if (message === 'session_expired') {
      setErrorType('expired');
      setErrorMessage('Your session has expired. Please sign in again to continue.');
    } else if (message === 'session_error') {
      setErrorType('error');
      setErrorMessage('There was an issue with your session. Please sign in again.');
    }
  }, [searchParams]);

  if (!errorMessage) {
    return null;
  }

  const getIcon = () => {
    switch (errorType) {
      case 'expired':
        return <Clock className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getVariant = () => {
    switch (errorType) {
      case 'expired':
        return 'default';
      case 'error':
        return 'destructive';
      default:
        return 'default';
    }
  };

  return (
    <div className="mb-4">
      <Alert variant={getVariant()}>
        {getIcon()}
        <AlertDescription>
          {errorMessage}
        </AlertDescription>
      </Alert>
    </div>
  );
} 