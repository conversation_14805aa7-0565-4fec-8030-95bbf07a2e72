"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { getAccountBalances } from "./get-account-balances";

export async function getTrialBalance(fromDate?: string, toDate?: string) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  const accountsWithBalances = await getAccountBalances(organizationId, fromDate, toDate);

  const totalDebits = accountsWithBalances.reduce((sum, ab) => sum + ab.debit_total, 0);
  const totalCredits = accountsWithBalances.reduce((sum, ab) => sum + ab.credit_total, 0);

  return {
    accounts: accountsWithBalances.map(ab => ({
        account: ab.account,
        debit_total: ab.debit_total,
        credit_total: ab.credit_total,
    })),
    totalDebits,
    totalCredits,
  };
} 