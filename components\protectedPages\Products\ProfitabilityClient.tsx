'use client';

import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProductProfitabilityDashboard } from './ProductProfitabilityDashboard';

type Product = {
  id: string;
  name: string;
  price: string | null;
  organizationId: string;
};

interface ProfitabilityClientProps {
  data: Product[];
  organizationId: string;
}

export default function ProfitabilityClient({ data, organizationId }: ProfitabilityClientProps) {
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);

  const totalRevenue = data.reduce((sum, p) => sum + parseFloat(p.price || '0'), 0);

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Product & Service Profitability</h1>
            <p className="text-muted-foreground">
              Get an overview of your revenue and analyze individual product performance.
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Overall Revenue Snapshot</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-primary">
              ${totalRevenue.toFixed(2)}
            </div>
            <p className="text-sm text-muted-foreground">
              Total revenue generated from {data.length} products.
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Individual Product Analysis</CardTitle>
            <p className="text-sm text-muted-foreground">
              Select a product to view its detailed profitability dashboard.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="w-full md:w-1/3">
              <Select onValueChange={setSelectedProductId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a product..." />
                </SelectTrigger>
                <SelectContent>
                  {data.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedProductId && (
              <div className="pt-4 border-t">
                <ProductProfitabilityDashboard
                  productId={selectedProductId}
                  organizationId={organizationId}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
