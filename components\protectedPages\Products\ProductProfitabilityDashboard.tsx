'use client';

import { useEffect, useState } from 'react';
import { getProductProfitability } from '@/lib/actions/products/get-product-profitability';
import type { ProductCost } from '@/lib/services/cost-calculation.service';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface ProfitabilityDashboardProps {
  productId: string;
  organizationId: string;
}

export function ProductProfitabilityDashboard({
  productId,
  organizationId,
}: ProfitabilityDashboardProps) {
  const [costData, setCostData] = useState<ProductCost | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCostData = async () => {
      setIsLoading(true);
      try {
        const data = await getProductProfitability(organizationId, productId);
        setCostData(data);
      } catch (error) {
        console.error('Failed to fetch profitability data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCostData();
  }, [productId, organizationId]);

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  if (!costData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profitability Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Could not load profitability data for this product.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profitability Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <StatCard title="Direct Cost" value={costData.directCost} isCurrency />
          <StatCard title="Overhead Cost" value={costData.overheadCost} isCurrency />
          <StatCard title="Total Cost" value={costData.totalCost} isCurrency />
          <StatCard title="Profit Margin" value={costData.profitMarginPercent} isPercentage />
        </div>
      </CardContent>
    </Card>
  );
}

const StatCard = ({ title, value, isCurrency = false, isPercentage = false }: {title: string, value: number, isCurrency?: boolean, isPercentage?: boolean}) => (
  <div className="p-4 border rounded-md">
    <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
    <p className="text-2xl font-bold">
      {isCurrency && '$'}
      {value.toFixed(2)}
      {isPercentage && '%'}
    </p>
  </div>
);

const DashboardSkeleton = () => (
  <Card>
    <CardHeader>
      <Skeleton className="h-6 w-1/2" />
    </CardHeader>
    <CardContent>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="p-4 border rounded-md space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-8 w-1/2" />
        </div>
        <div className="p-4 border rounded-md space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-8 w-1/2" />
        </div>
        <div className="p-4 border rounded-md space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-8 w-1/2" />
        </div>
        <div className="p-4 border rounded-md space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-8 w-1/2" />
        </div>
      </div>
    </CardContent>
  </Card>
);
