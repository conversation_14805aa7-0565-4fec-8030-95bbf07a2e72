import { Suspense } from "react";
import { BudgetPageClient } from "./BudgetPageClient";

export default function BudgetPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Budget Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Create and manage budgets, track variance against actuals, and analyze financial performance.
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <BudgetPageClient />
      </Suspense>
    </div>
  );
} 