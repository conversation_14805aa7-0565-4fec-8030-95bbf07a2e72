"use server";

import { db } from "@/db/drizzle";
import { clients } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq } from "drizzle-orm";
import { Client } from "@/components/protectedPages/Clients/columns";

export async function getClientById(clientId: string): Promise<Client | null> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const clientRow = await db
      .select()
      .from(clients)
      .where(and(eq(clients.id, clientId), eq(clients.organizationId, orgId)))
      .limit(1);
    return clientRow[0] ?? null;
  } catch (error) {
    return null;
  }
} 