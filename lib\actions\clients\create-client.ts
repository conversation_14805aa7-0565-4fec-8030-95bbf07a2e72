"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { clients } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { NewClient, clientFormSchema } from "@/lib/actions/clients/client.schema";
import { randomUUID } from 'crypto';

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

/**
 * Creates a new client in the database for the current organization.
 * @param clientData - The data for the new client, validated against clientFormSchema.
 * @returns The newly created client.
 */
export async function createClient(clientData: NewClient): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    const validatedFields = clientFormSchema.safeParse(clientData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid client data provided." };
    }

    await db.insert(clients).values({
      id: randomUUID(),
      ...validatedFields.data,
      organizationId: orgId,
    });

    revalidatePath("/clients");
    return { success: true, message: "Client created successfully." };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create client.",
    };
  }
} 