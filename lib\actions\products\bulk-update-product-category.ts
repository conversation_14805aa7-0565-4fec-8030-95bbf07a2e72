"use server";

import { db } from "@/db/drizzle";
import { products } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function bulkUpdateProductCategory(
  productIds: string[],
  categoryId: string
) {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    if (productIds.length === 0) {
      return { success: false, message: "No products selected." };
    }

    await db
      .update(products)
      .set({ categoryId })
      .where(
        and(
          eq(products.organizationId, orgId),
          inArray(products.id, productIds)
        )
      );

    revalidatePath("/products");

    return {
      success: true,
      message: `Successfully updated ${productIds.length} products.`,
    };
  } catch (error) {
    console.error("Failed to bulk update product category:", error);
    return {
      success: false,
      message: "An error occurred while updating products.",
    };
  }
} 