import { betterAuth } from "better-auth";
import { customSession, twoFactor, organization } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "@/db/drizzle";
import { nextCookies } from "better-auth/next-js";
import { schema } from "@/db/schema/schema";
import { Resend } from "resend";
import ForgotPasswordEmail from "@/components/emails/password-reset";
import VerifyAccountEmail from "@/components/emails/verify-email";
import TwoFactorAuthEmail from "@/components/emails/two-factor-auth";

import { eq } from "drizzle-orm";
import { polar, checkout, webhooks, portal } from "@polar-sh/better-auth"; 
import { Polar } from "@polar-sh/sdk"; 
import { getActiveOrganization } from "@/lib/actions/organization/get-organizations";
 
const resend = new Resend(process.env.RESEND_API_KEY);

const polarClient = new Polar({
    accessToken: process.env.POLAR_ACCESS_TOKEN,
    server: "sandbox",
});

export const auth = betterAuth({
    appName: "NextGenBusiness",
    emailAndPassword: {  
        enabled: true,
        sendResetPassword: async ({user, url}: {user: {name: string, email: string}, url: string}) => {
            await resend.emails.send({
                from: "NextGenBusiness <<EMAIL>>",
                to: user.email,
                subject: "Reset your password",
                react: ForgotPasswordEmail({userName: user.name, userEmail: user.email, resetUrl: url}),
            });
        },
        requireEmailVerification: true,
    },
    emailVerification: {
        sendVerificationEmail: async ({user, url}: {user: {name: string, email: string}, url: string}) => {
            try {
                await resend.emails.send({
                    from: "NextGenBusiness <<EMAIL>>",
                    to: user.email,
                    subject: "Verify your email address",
                    react: VerifyAccountEmail({userName: user.name, userEmail: user.email, verifyUrl: url}),
                });
            } catch (error) {
                console.error("Error sending verification email:", error);
            }
        },
        autoSignInAfterVerification: true,
        sendOnSignUp: true,
    },
    databaseHooks: {
        session: {
            create: {
                before: async(session)=>{
                    const organization = await getActiveOrganization(session.userId)
                    return {
                      data: {
                        ...session,
                        activeOrganizationId: organization?.id
                      }
                    }
                }
            }
        }
    },
    database: drizzleAdapter(db, {
        provider: "pg",
        schema,
    }),
    socialProviders: { 
        google: { 
           clientId: process.env.GOOGLE_CLIENT_ID as string, 
           clientSecret: process.env.GOOGLE_CLIENT_SECRET as string, 
        },
    },
    plugins: [
        twoFactor({
            otpOptions: {
                async sendOTP({ user, otp}) {
                    await resend.emails.send({
                        from: "NextGenBusiness <<EMAIL>>",
                        to: user.email,
                        subject: "Your Two-Factor Authentication Code",
                        react: TwoFactorAuthEmail({ userName: user.name, otpCode: otp }),
                    });
                },
            },
        }),
        customSession(async ({ user, session }) => {
            if (!user) {
                return { user, session };
            }
            
            // Get full user data
            const [fullUser] = await db.select().from(schema.user).where(eq(schema.user.id, user.id));
            
            // Get user's organizations and roles
            const userOrganizations = await db.select({
                organizationId: schema.organization.id,
                organizationName: schema.organization.name,
                role: schema.member.role,
            })
            .from(schema.member)
            .innerJoin(schema.organization, eq(schema.member.organizationId, schema.organization.id))
            .where(eq(schema.member.userId, user.id));

            return {
                session,
                user: {
                    ...user,
                    onboarded: fullUser?.onboarded ?? false,
                    twoFactorEnabled: fullUser?.twoFactorEnabled ?? false,
                    organizations: userOrganizations,
                    currentOrganizationId: userOrganizations[0]?.organizationId ?? null,
                },
            };
        }),
        polar({
            client: polarClient,
            createCustomerOnSignUp: false,
            use: [
                checkout({
                    products: [
                        {
                            productId: "a700aa67-b982-4c2d-9231-abb57acf64d0",
                            slug: "starter-monthly",
                        },
                        {
                            productId: "7f159a03-91fb-4eb9-8941-6b600976024c",
                            slug: "starter-annual",
                        },
                        {
                            productId: "e2273e83-a20d-48e4-b0b6-a7b9e8543491",
                            slug: "professional-monthly",
                        },
                        {
                            productId: "1dca0ff7-2548-4c3f-9a4e-eac854a8fa5c",
                            slug: "professional-annual",
                        },
                        {
                            productId: "0fce1d49-7998-4e65-9649-8776ffec76b2",
                            slug: "business-monthly",
                        },
                        {
                            productId: "c326bf62-44c5-4695-ab21-41d43ff43cf9",
                            slug: "business-annual",
                        },
                        {
                            productId: "ec35442c-9c52-4091-9f0f-f5aa1719489e",
                            slug: "unlimited-monthly",
                        },
                        {
                            productId: "cf96cc29-666f-4416-9e73-1412ec0b8f8a",
                            slug: "unlimited-annual",
                        },
                    ],
                    successUrl: process.env.NODE_ENV === "production" 
                        ? "https://your-domain.com/onboarding?step=completion&from_checkout=true"
                        : "http://localhost:3000/onboarding?step=completion&from_checkout=true",
                    authenticatedUsersOnly: true,
                }),
                webhooks({
                    secret: process.env.POLAR_WEBHOOK_SECRET as string,
                    onPayload: async (payload) => {
                        console.log("Polar webhook payload:", payload);
                        // The actual webhook processing happens in /api/webhooks/polar/route.ts
                    },
                }),
                portal(),
                ],
        }),
        organization({
            schema: {
                organization: {
                  additionalFields: {
                    // Business Information
                    businessType: { type: "string" },
                    industry: { type: "string" },
                    website: { type: "string" },
                    phone: { type: "string" },
                    email: { type: "string" },
                    taxId: { type: "string" },
                    currency: { type: "string" },
                    
                    // Subscription & Billing
                    planType: { type: "string" },
                    planStatus: { type: "string" },
                    subscriptionId: { type: "string" },
                    subscriptionCurrentPeriodEnd: { type: "date" },
                    trialEndsAt: { type: "date" },
                    monthlyTransactionCount: { type: "number" },
                    
                    // Organization Settings
                    logoUrl: { type: "string" },
                    requireTwoFactor: { type: "boolean" },
                    
                    // Metadata (if you want to store additional custom data)
                    metadata: { type: "string" },
                  }
                },
                member: {
                  additionalFields: {
                    // Member-specific custom fields if needed
                    status: { type: "string" },
                    invitedAt: { type: "date" },
                    acceptedAt: { type: "date" },
                  }
                }
              }
          }),
        nextCookies()],
});