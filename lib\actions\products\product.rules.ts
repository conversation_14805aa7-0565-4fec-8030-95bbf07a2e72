import { Product } from "@/components/protectedPages/Products/columns";

// As per the plan: products-and-services-overhaul.md
export const productValidationRules = {
  service: {
    required: ["revenueAccountId"],
    optional: ["cogsAccountId"],
  },
  physical_product: {
    required: ["revenueAccountId", "cogsAccountId", "inventoryAccountId"],
    optional: [],
  },
  digital_service: {
    required: ["revenueAccountId"],
    optional: ["cogsAccountId"],
  },
  digital_product: {
    required: ["revenueAccountId"],
    optional: ["cogsAccountId"],
  },
  subscription: {
    required: ["revenueAccountId"],
    optional: ["cogsAccountId"],
  },
  bundle: {
    required: ["revenueAccountId"],
    optional: [],
  },
};

type ValidationResult = {
  isValid: boolean;
  errors: string[];
};

type OptimizationSuggestions = {
  suggestions: string[];
};

type ProductInput = Omit<Product, 'id' | 'organizationId' | 'createdAt' | 'updatedAt' | 'totalSold' | 'totalRevenue' | 'totalCosts' | 'lastSaleDate' | 'margin' | 'profitPerUnit' | 'status'>;

export class ProductBusinessRules {
  validateProductSetup(product: ProductInput): ValidationResult {
    // Handle both 'type' and 'productType' field names for backward compatibility
    const productType = (product as any).productType || product.type;
    const rules = productValidationRules[productType];
    const errors: string[] = [];

    if (!rules) {
      return { isValid: false, errors: [`No validation rules for type: ${productType}`] };
    }

    for (const field of rules.required) {
      if (!product[field as keyof ProductInput]) {
        errors.push(`${field} is required for ${productType} products.`);
      }
    }

    if(product.price && product.costBasis){
        if (parseFloat(product.price) < parseFloat(product.costBasis)) {
            errors.push("Price is less than the cost basis, resulting in a loss.");
        }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  suggestOptimizations(product: Product): OptimizationSuggestions {
    const suggestions: string[] = [];
    const marginTarget = product.marginTarget ? parseFloat(product.marginTarget) : 0;

    if (product.margin < marginTarget) {
        suggestions.push("Profit margin is below target. Consider increasing the price or reducing costs.");
    }

    if (product.totalSold < 10 && product.createdAt && (Date.now() - new Date(product.createdAt).getTime()) > 30 * 24 * 60 * 60 * 1000) {
        suggestions.push("This product has low sales volume. Consider a marketing campaign or a price adjustment.");
    }

    return { suggestions };
  }
} 