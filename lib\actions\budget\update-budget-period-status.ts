"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function updateBudgetPeriodStatus(
  budgetPeriodId: string,
  status: "draft" | "active" | "closed" | "archived"
) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  try {
    const [updatedPeriod] = await db
      .update(budgetPeriods)
      .set({ status })
      .where(eq(budgetPeriods.id, budgetPeriodId))
      .returning();

    revalidatePath("/accounting/budget");
    return { success: true, data: updatedPeriod };
  } catch (error) {
    console.error("Error updating budget period status:", error);
    return { error: "Failed to update budget period status" };
  }
} 