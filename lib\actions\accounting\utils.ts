export function getDefaultClassification(accountType: string, accountNumber: string): string {
    const num = parseInt(accountNumber.substring(0, 1));
    if (accountType === 'asset') return num === 1 ? 'current' : 'non_current';
    if (accountType === 'liability') return num === 2 ? 'current' : 'non_current';
    return 'n_a';
}

export function getDefaultFinancialStatementSection(accountType: string, accountNumber: string): string {
    const num = parseInt(accountNumber.substring(0, 1));
    switch (accountType) {
        case 'asset': return 'Balance Sheet';
        case 'liability': return 'Balance Sheet';
        case 'equity': return 'Balance Sheet';
        case 'revenue': return 'Income Statement';
        case 'expense': return 'Income Statement';
        default: return 'n_a';
    }
}

export function getDefaultCashFlowCategory(accountType: string): string {
    if (['revenue', 'expense'].includes(accountType)) return 'operating';
    return 'n_a';
}

export function getAccountNumberRange(accountType: string): { min: number; max: number } {
    switch (accountType) {
        case 'asset': return { min: 1000, max: 1999 };
        case 'liability': return { min: 2000, max: 2999 };
        case 'equity': return { min: 3000, max: 3999 };
        case 'revenue': return { min: 4000, max: 4999 };
        case 'expense': return { min: 5000, max: 8999 };
        default: return { min: 9000, max: 9999 };
    }
} 