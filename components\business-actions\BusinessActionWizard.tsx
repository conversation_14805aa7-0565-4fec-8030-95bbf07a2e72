"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DollarSign, CreditCard, Sparkles } from "lucide-react";
import { toast } from "sonner";

// Import business action forms (only available ones)
import { CustomerPaymentForm } from "./forms/CustomerPaymentForm";
import { MoneyPaidForm } from "./forms/MoneyPaidForm";
// If you have a MoneyPaidForm, import it here. Otherwise, reuse CustomerPaymentForm as a placeholder.


import {
  BusinessActionType,
  BUSINESS_ACTION_DESCRIPTIONS,
  BusinessAction
} from "@/lib/actions/automation/types";
import { previewJournalEntries } from "@/lib/actions/automation/preview-journal-entries";
import { Sheet, She<PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDescription } from "@/components/ui/sheet";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Eye, CheckCircle, ArrowLeft } from "lucide-react";
import { cn } from "@/lib/utils";

interface BusinessActionWizardProps {
  trigger?: React.ReactNode;
}

type WizardStep = "select" | "form" | "preview" | "confirm";

interface JournalEntry {
  accountName: string;
  accountId: string;
  debitAmount: number;
  creditAmount: number;
  description: string;
}

export function BusinessActionWizard({ trigger }: BusinessActionWizardProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<WizardStep>("select");
  const [selectedAction, setSelectedAction] = useState<BusinessActionType | null>(null);
  const [formData, setFormData] = useState<any>(null);
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);

  // Action type icons
  const actionIcons: Record<BusinessActionType, React.ReactNode> = {
    money_received: <DollarSign className="h-8 w-8" />,
    money_paid: <CreditCard className="h-8 w-8" />,
  };

  // Step progress
  const stepProgress = {
    select: 25,
    form: 50,
    preview: 75,
    confirm: 100
  };

  useEffect(() => {
    setProgress(stepProgress[currentStep]);
  }, [currentStep]);

  const handleActionSelect = (actionType: BusinessActionType) => {
    setSelectedAction(actionType);
    setCurrentStep("form");
  };

  const handleFormSubmit = async (data: any) => {
    setFormData(data);
    setIsLoading(true);
    
    try {
      // Preview journal entries
      const result = await previewJournalEntries(data);
      if (result.success && result.entries) {
        setJournalEntries(result.entries);
        setCurrentStep("preview");
      } else {
        toast.error("Failed to generate preview: " + (result.error || "Unknown error"));
      }
    } catch (error) {
      toast.error("Error generating preview");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirm = () => {
    setCurrentStep("confirm");
    toast.success("Transaction recorded successfully!");
    setTimeout(() => {
      resetWizard();
      setIsOpen(false);
    }, 2000);
  };

  const resetWizard = () => {
    setCurrentStep("select");
    setSelectedAction(null);
    setFormData(null);
    setJournalEntries([]);
    setProgress(0);
  };

  const handleBack = () => {
    switch (currentStep) {
      case "form":
        setCurrentStep("select");
        setSelectedAction(null);
        break;
      case "preview":
        setCurrentStep("form");
        break;
      case "confirm":
        setCurrentStep("preview");
        break;
    }
  };

  const renderActionSelector = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">What did you do in your business?</h3>
        <p className="text-muted-foreground mb-6">
          Choose the action that best describes what happened. We'll handle all the accounting automatically.
        </p>
      </div>

      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {(["money_received", "money_paid"] as BusinessActionType[]).map((actionType) => {
            const description = BUSINESS_ACTION_DESCRIPTIONS[actionType];
            return (
              <Card
                key={actionType}
                className="cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.01] group p-3"
                onClick={() => handleActionSelect(actionType)}
              >
                <CardHeader className="pb-2 px-2">
                  <div className="flex items-start gap-2">
                    <div className="p-1.5 rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      {actionIcons[actionType]}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-0.5">
                        <CardTitle className="text-base font-semibold">{description.title}</CardTitle>
                        <span className="text-base">{description.icon}</span>
                      </div>
                      <CardDescription className="text-xs">
                        {description.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0 px-2 pb-2">
                  <p className="text-xs text-muted-foreground font-medium mb-0.5">Examples:</p>
                  <ul className="text-xs text-muted-foreground space-y-0.5">
                    {description.examples.slice(0, 2).map((example, index) => (
                      <li key={index} className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                        {example}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      <Alert>
        <Sparkles className="h-4 w-4" />
        <AlertDescription>
          <strong>Smart Automation:</strong> We automatically generate proper accounting journal entries, 
          so you don't need to understand debits, credits, or chart of accounts!
        </AlertDescription>
      </Alert>
    </div>
  );

  const renderForm = () => {
    const commonProps = {
      onSubmit: handleFormSubmit,
      onCancel: handleBack,
      isLoading,
    };
    switch (selectedAction) {
      case "money_received":
        return <CustomerPaymentForm {...commonProps} />;
      case "money_paid":
        return <MoneyPaidForm {...commonProps} />;
      default:
        return null;
    }
  };

  const renderPreview = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">Preview Your Transaction</h3>
        <p className="text-muted-foreground">
          Here's what will be recorded in your books. The accounting is handled automatically!
        </p>
      </div>

      {/* Business Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {selectedAction && actionIcons[selectedAction]}
            Business Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Action:</span>
              <span>{selectedAction && BUSINESS_ACTION_DESCRIPTIONS[selectedAction].title}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Amount:</span>
              <span className="font-semibold text-lg">${formData?.amount?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Description:</span>
              <span>{formData?.description}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Date:</span>
              <span>{formData?.date}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Accounting Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Accounting Details (Auto-Generated)
          </CardTitle>
          <CardDescription>
            These journal entries will be created automatically to maintain proper double-entry bookkeeping
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {journalEntries.map((entry, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <p className="font-medium">{entry.accountName}</p>
                  <p className="text-sm text-muted-foreground">{entry.description}</p>
                </div>
                <div className="text-right space-y-1">
                  {entry.debitAmount > 0 && (
                    <div className="text-green-600 font-semibold">
                      +${entry.debitAmount.toFixed(2)}
                    </div>
                  )}
                  {entry.creditAmount > 0 && (
                    <div className="text-blue-600 font-semibold">
                      -${entry.creditAmount.toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <Separator className="my-4" />
          
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <span>Total Debits:</span>
            <span>${journalEntries.reduce((sum, entry) => sum + entry.debitAmount, 0).toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <span>Total Credits:</span>
            <span>${journalEntries.reduce((sum, entry) => sum + entry.creditAmount, 0).toFixed(2)}</span>
          </div>
          
          <Alert className="mt-4">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              ✅ Books are balanced! Your accounting will be accurate and compliant.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handleBack} className="flex-1">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Edit
        </Button>
        <Button onClick={handleConfirm} className="flex-1">
          <CheckCircle className="h-4 w-4 mr-2" />
          Record Transaction
        </Button>
      </div>
    </div>
  );

  const renderConfirmation = () => (
    <div className="text-center space-y-6 py-8">
      <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle className="h-8 w-8 text-green-600" />
      </div>
      <div>
        <h3 className="text-xl font-semibold mb-2">Transaction Recorded Successfully!</h3>
        <p className="text-muted-foreground">
          Your business transaction has been automatically converted to proper accounting entries 
          and saved to your books.
        </p>
      </div>
      <div className="space-y-2 text-sm text-muted-foreground">
        <p>✅ Journal entries created</p>
        <p>✅ Chart of accounts updated</p>
        <p>✅ Financial reports refreshed</p>
        <p>✅ Dashboard metrics updated</p>
      </div>
    </div>
  );

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      {trigger ? <SheetTrigger asChild>{trigger}</SheetTrigger> : null}
      <SheetContent side="right" className="!w-[99vw] sm:!w-[95vw] md:!w-[90vw] lg:!w-[85vw] xl:!w-[80vw] 2xl:!w-[75vw] !max-w-none">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Business Transaction Wizard
          </SheetTitle>
          <SheetDescription>
            Tell us what happened in your business - we'll handle all the accounting complexity automatically
          </SheetDescription>
        </SheetHeader>

        {/* Progress Bar */}
        <div className="space-y-2 px-4 md:px-8">
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span className={cn(currentStep === "select" && "font-semibold text-primary")}> 
              1. Select Action
            </span>
            <span className={cn(currentStep === "form" && "font-semibold text-primary")}> 
              2. Enter Details
            </span>
            <span className={cn(currentStep === "preview" && "font-semibold text-primary")}> 
              3. Preview
            </span>
            <span className={cn(currentStep === "confirm" && "font-semibold text-primary")}> 
              4. Complete
            </span>
          </div>
        </div>

        <Separator />

        {/* Content */}
        <ScrollArea className="flex-1 px-4 md:px-8 pt-4 pb-8 max-h-[70vh] min-h-0 overflow-y-auto">
          {currentStep === "select" && renderActionSelector()}
          {currentStep === "form" && renderForm()}
          {currentStep === "preview" && renderPreview()}
          {currentStep === "confirm" && renderConfirmation()}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
} 