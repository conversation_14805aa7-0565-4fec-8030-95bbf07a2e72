"use client";

import { useState, useEffect, useRef } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { type ColumnDef, useReactTable, getCoreRowModel, flexRender, getPaginationRowModel } from "@tanstack/react-table";
import * as React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { useSwipeable } from 'react-swipeable';
import { BookOpen } from "lucide-react";

interface TransactionsDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  highlightedRowId?: string;
}

export function TransactionsDataTable<TData, TValue>({
  columns,
  data,
  highlightedRowId,
}: TransactionsDataTableProps<TData, TValue>) {
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [swipedRowId, setSwipedRowId] = useState<string | null>(null);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount: Math.ceil(data.length / pageSize),
    state: { pagination: { pageIndex, pageSize } },
    onPaginationChange: updater => {
      if (typeof updater === "function") {
        const next = updater({ pageIndex, pageSize });
        setPageIndex(next.pageIndex);
        setPageSize(next.pageSize);
      } else {
        setPageIndex(updater.pageIndex);
        setPageSize(updater.pageSize);
      }
    },
    manualPagination: false,
  });

  const totalPages = Math.max(1, Math.ceil(data.length / pageSize));

  return (
    <div className="overflow-x-auto bg-nextgen-background rounded-lg font-poppins" role="region" aria-label="Transactions Table">
      <Table role="table" className="min-w-full text-nextgen-accent bg-nextgen-background">
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id} role="row" className="bg-nextgen-background">
              {headerGroup.headers.map(header => (
                <TableHead key={header.id} role="columnheader" className="bg-nextgen-accent text-nextgen-background font-semibold px-2 py-2 md:px-4 md:py-3 whitespace-nowrap sticky top-0 z-10">
                  {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.length ? (
            table.getRowModel().rows.map(row => {
              // Swipe handlers for mobile
              const swipeHandlers = useSwipeable({
                onSwipedLeft: () => setSwipedRowId(row.id),
                onSwipedRight: () => setSwipedRowId(null),
                trackMouse: true,
              });
              const isSwiped = swipedRowId === row.id;
              return (
                <TableRow
                  key={row.id}
                  className="hover:bg-nextgen-primary/10 transition-colors duration-150"
                  role="row"
                  style={{ color: 'var(--nextgen-accent)', background: 'var(--nextgen-background)' }}
                  tabIndex={0}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} role="cell" className="py-2 px-2 md:px-4 md:py-3 text-nextgen-accent whitespace-nowrap text-sm md:text-base relative">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              );
            })
          ) : (
            <TableRow role="row" className="bg-nextgen-background">
              <TableCell colSpan={columns.length} className="h-48 text-center text-nextgen-accent p-0" role="cell">
                <div className="flex flex-col items-center justify-center h-full py-8">
                  <BookOpen className="h-10 w-10 text-muted-foreground mb-2" />
                  <div className="font-semibold text-lg mb-1">No transactions found</div>
                  <div className="text-sm text-muted-foreground">Start by adding a transaction or recording a business action.</div>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center gap-2">
          <span>Rows per page:</span>
          <select
            className="border rounded px-2 py-1"
            value={pageSize}
            onChange={e => {
              setPageSize(Number(e.target.value));
              setPageIndex(0);
            }}
          >
            {[10, 25, 50].map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
        </div>
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (pageIndex > 0) setPageIndex(pageIndex - 1);
                }}
                aria-disabled={pageIndex === 0}
              />
            </PaginationItem>
            {Array.from({ length: totalPages }).map((_, i) => (
              <PaginationItem key={i}>
                <PaginationLink
                  href="#"
                  isActive={i === pageIndex}
                  onClick={e => {
                    e.preventDefault();
                    setPageIndex(i);
                  }}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (pageIndex < totalPages - 1) setPageIndex(pageIndex + 1);
                }}
                aria-disabled={pageIndex === totalPages - 1}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
      <style jsx>{`
        .animate-row-highlight {
          animation: rowHighlightFade 1.5s;
        }
        @keyframes rowHighlightFade {
          0% { background-color: #bc00fe33; }
          100% { background-color: #222121; }
        }
        @media (max-width: 600px) {
          table, thead, tbody, th, td, tr {
            font-size: 0.95rem !important;
            padding: 0.5rem !important;
          }
          /* Swipe action styles */
          td { transition: transform 0.3s; }
        }
      `}</style>
    </div>
  );
} 