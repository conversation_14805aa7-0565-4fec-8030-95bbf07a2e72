"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";
import { getServerUserContext } from "@/lib/server-auth";
import { businessExpensePaidSchema } from "@/lib/actions/automation/types";
import { createJournalAutomationService } from "@/lib/actions/automation/journal-automation";

// Response types
interface BusinessActionResponse {
  success: boolean;
  transactionId?: string;
  message: string;
  error?: string;
  warnings?: string[];
  journalEntries?: any[];
}

/**
 * Process Business Expense Paid
 * Business-friendly action: "I paid for a business expense"
 */
export async function processBusinessExpensePaid(
  data: z.infer<typeof businessExpensePaidSchema>
): Promise<BusinessActionResponse> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    // Validate input
    const validatedData = businessExpensePaidSchema.parse(data);
    
    // Create journal automation service
    const journalService = await createJournalAutomationService(orgId);
    
    // Process the business action
    const result = await journalService.processBusinessAction(validatedData);
    
    if (result.success) {
      revalidatePath("/transactions");
      revalidatePath("/dashboard");
      
      return {
        success: true,
        transactionId: result.transactionId,
        message: `Business expense of $${validatedData.amount.toFixed(2)} has been recorded successfully!`,
        warnings: result.warnings,
        journalEntries: result.journalEntries
      };
    } else {
      return {
        success: false,
        message: "Failed to process expense",
        error: result.error
      };
    }
  } catch (error) {
    console.error("Error processing business expense:", error);
    return {
      success: false,
      message: "Failed to process expense",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 