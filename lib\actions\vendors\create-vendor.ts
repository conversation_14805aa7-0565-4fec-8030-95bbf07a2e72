"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { vendors } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { NewVendor, vendorFormSchema } from "@/lib/actions/vendors/vendor.schema";

type ActionResponse = Promise<{
  success: boolean;
  message: string;
}>;

export async function createVendor(vendorData: NewVendor): ActionResponse {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;
    const validatedFields = vendorFormSchema.safeParse(vendorData);

    if (!validatedFields.success) {
      return { success: false, message: "Invalid vendor data provided." };
    }

    await db.insert(vendors).values({
      ...validatedFields.data,
      organizationId: orgId,
    });

    revalidatePath("/vendors");
    return { success: true, message: "Vendor created successfully." };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create vendor.",
    };
  }
} 