"use server";

import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { and, eq, inArray } from "drizzle-orm";
import { FormState } from "@/lib/actions/accounting/account.schema";

export async function deleteAccounts(accountIds: string[]): Promise<FormState> {
  const { organization } = await getServerUserContext();
  const orgId = organization.id;

  try {
    await db
      .delete(accounts)
      .where(and(inArray(accounts.id, accountIds), eq(accounts.organizationId, orgId)));
    revalidatePath("/accounting/chart-of-accounts");
    return { success: true, message: "Account(s) deleted successfully." };
  } catch (error) {
    return { success: false, message: "Error: Failed to delete account(s)." };
  }
} 