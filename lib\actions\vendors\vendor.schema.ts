import { z } from "zod";

const addressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().optional(),
});

export const vendorFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  email: z.string().email("Invalid email address.").optional().or(z.literal('')),
  phone: z.string().optional(),
  category: z.string().optional(),
  address: addressSchema.optional(),
});

export type NewVendor = z.infer<typeof vendorFormSchema>;

export type FormState = {
  success: boolean;
  message: string;
}; 