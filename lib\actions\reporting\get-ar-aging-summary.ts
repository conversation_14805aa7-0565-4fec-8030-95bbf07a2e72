"use server";

import { db } from "@/db/drizzle";
import { invoices } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq, and, ne, lte } from "drizzle-orm";
import { ageBucket } from "./age-bucket";

export async function getARAgingSummary(asOfDateStr?: string) {
    const { organization } = await getServerUserContext();
    const organizationId = organization.id;
    const asOfDate = asOfDateStr ? new Date(asOfDateStr) : new Date();

    const openInvoices = await db.select({
        id: invoices.id,
        total: invoices.total,
        dueDate: invoices.dueDate,
        status: invoices.status,
    }).from(invoices)
      .where(and(
          eq(invoices.organizationId, organizationId),
          ne(invoices.status, 'paid'),
          ne(invoices.status, 'void'),
          lte(invoices.date, asOfDate)
      ));

    const summary = {
        current: 0,
        '1-30': 0,
        '31-60': 0,
        '61-90': 0,
        '91+': 0,
        total: 0,
    };

    for (const invoice of openInvoices) {
        const bucket = ageBucket(invoice.dueDate!, asOfDate);
        const amount = parseFloat(invoice.total);
        (summary as any)[bucket] += amount;
        summary.total += amount;
    }
    
    return summary;
} 