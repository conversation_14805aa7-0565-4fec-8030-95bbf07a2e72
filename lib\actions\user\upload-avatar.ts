"use server";
import { db } from "@/db/drizzle";
import { user as userTable } from "@/db/schema/schema";
import { eq } from "drizzle-orm";
import ImageKit from "imagekit";

export async function uploadUserAvatarToImageKit({ file, fileName, fileType }: { file: Buffer | string; fileName: string; fileType: string }) {
  const imagekit = new ImageKit({
    publicKey: process.env.IMAGEKIT_PUBLIC_KEY!,
    privateKey: process.env.IMAGEKIT_PRIVATE_KEY!,
    urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT!,
  });
  try {
    const uploadResponse = await imagekit.upload({
      file,
      fileName,
      folder: "/avatars/",
      useUniqueFileName: true,
      extensions: [
        { name: "auto-tag", maxTags: 5, minConfidence: 90 },
      ],
    });
    return { url: uploadResponse.url };
  } catch (error) {
    return { error: error instanceof Error ? error.message : String(error) };
  }
}

export async function updateUserAvatarUrl(userId: string, imageUrl: string) {
  try {
    await db.update(userTable).set({ image: imageUrl }).where(eq(userTable.id, userId));
    return { success: true };
  } catch (error) {
    return { error: error instanceof Error ? error.message : String(error) };
  }
}

const MAX_FILE_SIZE_MB = 10;
const ACCEPTED_TYPES = ["image/jpeg", "image/png", "image/webp"];

export async function uploadAvatarAction(formData: FormData): Promise<{ error?: string; success?: string; newImageUrl?: string }> {
  const file = formData.get("avatar") as File;
  const userId = formData.get("userId") as string;

  if (!file) return { error: "No file provided." };
  if (!userId) return { error: "User not found." };

  if (!ACCEPTED_TYPES.includes(file.type)) {
    return { error: "File must be JPEG, PNG, or WebP." };
  }
  if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
    return { error: `File must be less than ${MAX_FILE_SIZE_MB}MB.` };
  }

  try {
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const uploadResult = await uploadUserAvatarToImageKit({
      file: buffer,
      fileName: file.name,
      fileType: file.type,
    });

    if (uploadResult.error || !uploadResult.url) {
      return { error: `Upload failed: ${uploadResult.error || "Unknown error"}` };
    }

    const updateResult = await updateUserAvatarUrl(userId, uploadResult.url);
    if (updateResult.error) {
      return { error: `Failed to update profile: ${updateResult.error}` };
    }

    return { success: "Avatar updated successfully!", newImageUrl: uploadResult.url };
  } catch (err: any) {
    return { error: `Unexpected error: ${err?.message || String(err)}` };
  }
} 