"use server";
import { coaTemplatesMetadata, getTemplateAccounts } from "@/lib/data/coaTemplates";

export async function getChartOfAccountsTemplates(businessType: string, industry?: string) {
  
    if (!businessType) {
      return coaTemplatesMetadata;
    }
  
    const filteredTemplates = coaTemplatesMetadata.filter(template => {
      const typeMatch = template.businessType.includes(businessType.toLowerCase());
      const industryMatch = !industry || template.industry.toLowerCase() === industry.toLowerCase();
      
      return typeMatch && industryMatch;
    });
  
    if (filteredTemplates.length > 0) {
      return filteredTemplates;
    }
  
    // Fallback to generic template if no specific match
    let genericTemplate = coaTemplatesMetadata.filter(t => t.id === 'generic');
    if (!genericTemplate || genericTemplate.length === 0) {
      // Hardcoded fallback generic template
      genericTemplate = [{
        id: 'generic',
        businessType: 'other',
        industry: 'general',
        templateName: 'Generic Business Template',
        description: 'A basic chart of accounts for any business type.',
        isDefault: true,
      }];
    }
    return genericTemplate;
} 