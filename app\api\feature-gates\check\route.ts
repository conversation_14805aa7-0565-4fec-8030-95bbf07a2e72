import { NextRequest, NextResponse } from "next/server";
import { checkFeatureGate } from "@/lib/feature-gates";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { feature, checkType = "feature" } = body;

    const result = await checkFeatureGate(feature, checkType);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error checking feature gate:", error);
    return NextResponse.json(
      { 
        allowed: false, 
        reason: error instanceof Error ? error.message : "Unknown error" 
      },
      { status: 500 }
    );
  }
} 