"use server";

import { db } from "@/db/drizzle";
import { auditLogs } from "@/db/schema/schema";
import {
  AuditAction,
  AuditLogEntry,
  ResourceType,
  RiskLevel,
  SecurityContext,
} from "./types";
import { getAuditContext, getSecurityContext } from "./context";

/**
 * Determine risk level based on action and context
 */
function calculateRiskLevel(
  action: AuditAction,
  resourceType: ResourceType,
  metadata?: Record<string, any>,
): RiskLevel {
  // Critical actions
  if (["delete", "remove_member", "change_role"].includes(action)) {
    return "critical";
  }

  // High risk actions
  if (
    ["grant_permission", "revoke_permission", "backup", "restore"].includes(
      action,
    )
  ) {
    return "high";
  }

  // Medium risk actions
  if (["create", "update", "send", "approve", "export"].includes(action)) {
    return "medium";
  }

  // Special cases based on resource type
  if (resourceType === "organization" && action === "update") {
    return "high";
  }

  if (resourceType === "user" && ["create", "update"].includes(action)) {
    return "medium";
  }

  return "low";
}

/**
 * Create an audit log entry
 */
export async function createAuditLog(
  entry: AuditLogEntry,
  securityContext?: SecurityContext,
): Promise<void> {
  try {
    const { userId, organizationId, sessionId } = await getAuditContext();

    if (!organizationId) {
      console.warn("Attempted to create audit log without organization context");
      return;
    }

    const context = securityContext || (await getSecurityContext());
    const riskLevel =
      entry.riskLevel ||
      calculateRiskLevel(entry.action, entry.resourceType, entry.metadata);

    // Generate security flags based on patterns
    const flags: string[] = entry.flags || [];

    // Flag suspicious patterns
    if (context.ipAddress && context.ipAddress !== "unknown") {
      // Add IP-based flags (implement IP reputation checking here)
    }

    if (entry.action === "delete" && entry.resourceType === "transaction") {
      flags.push("financial_deletion");
    }

    if (riskLevel === "critical") {
      flags.push("high_risk_action");
    }

    await db.insert(auditLogs).values({
      organizationId,
      userId,
      sessionId,
      action: entry.action,
      resourceType: entry.resourceType,
      resourceId: entry.resourceId,
      description: entry.description,
      oldValues: entry.oldValues
        ? JSON.parse(JSON.stringify(entry.oldValues))
        : null,
      newValues: entry.newValues
        ? JSON.parse(JSON.stringify(entry.newValues))
        : null,
      changedFields: entry.changedFields
        ? JSON.parse(JSON.stringify(entry.changedFields))
        : null,
      metadata: entry.metadata
        ? JSON.parse(JSON.stringify(entry.metadata))
        : null,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      location: context.location
        ? JSON.parse(JSON.stringify(context.location))
        : null,
      riskLevel,
      flags: flags.length > 0 ? JSON.parse(JSON.stringify(flags)) : null,
    });
  } catch (error) {
    console.error("Failed to create audit log:", error);
    // Don't throw - audit logging failures shouldn't break the main operation
  }
} 