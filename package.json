{"name": "nextgenbusiness", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@neondatabase/serverless": "^1.0.1", "@polar-sh/better-auth": "^1.1.0", "@polar-sh/checkout": "^0.1.11", "@polar-sh/nextjs": "^0.4.4", "@polar-sh/sdk": "^0.34.11", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@react-email/components": "^0.5.0", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "@tanstack/react-table": "^8.21.3", "@types/jsonwebtoken": "^9.0.10", "@vvo/tzdb": "^6.183.0", "better-auth": "^1.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "embla-carousel-react": "^8.6.0", "imagekit": "^6.0.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "libphonenumber-js": "^1.12.12", "lucide-react": "^0.539.0", "motion": "^12.23.12", "next": "15.4.6", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "^19.1.1", "react-day-picker": "9.8.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^3.0.4", "react-swipeable": "^7.0.2", "react-use-measure": "^2.1.7", "recharts": "^3.1.2", "resend": "^6.0.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-grid-layout": "^1.3.5", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.4", "drizzle-zod": "^0.8.3", "eslint": "^9.33.0", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "prettier": "3.6.2", "tailwindcss": "^4.1.12", "tsx": "^4.20.4", "typescript": "^5.9.2"}, "pnpm": {"overrides": {"prettier": "3.6.2"}}, "overrides": {"@radix-ui/react-dismissable-layer": "1.1.8", "@radix-ui/react-focus-scope": "1.1.2", "@radix-ui/react-portal": "1.1.8", "prettier": "3.6.2"}}