"use server";

import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { headers } from "next/headers";
import { db } from "@/db/drizzle";
import { eq } from "drizzle-orm";
import { user} from "@/db/schema/schema";

export const getCurrentUser = async () => {

    const session = await auth.api.getSession({
        headers: await headers(),
    });

    if (!session) {
        redirect("/login");
    }

    const currentUser = await db.query.user.findFirst({
        where: eq(user.id, session.user.id),
    });

    if (!currentUser) {
        redirect("/login");
    }

    return {
        ...session,
        currentUser
    }}

    // Get current user info

    export async function getCurrentUserInfo() {
        const session = await auth.api.getSession({ headers: await headers() });
        const userId = session?.user?.id;
        if (!userId) return null;
        const userRow = await db
          .select({ 
            id: user.id,
            name: user.name,
            email: user.email,
            image: user.image,
            emailVerified: user.emailVerified,
            onboarded: user.onboarded,
            twoFactorEnabled: user.twoFactorEnabled })
          .from(user)
          .where(eq(user.id, userId))
          .limit(1);
        if (!userRow[0] || !userRow[0].id) return null;
        return userRow[0];
    } 