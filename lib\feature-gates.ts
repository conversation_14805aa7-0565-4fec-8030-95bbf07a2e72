"use server";

import { db } from "@/db/drizzle";
import { organizations, organizationMembers } from "@/db/schema/schema";
import { eq, and, count } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

// Define plan limits based on your pricing structure
export const PLAN_LIMITS = {
  trial: {
    users: 1,
    transactions: 50,
    features: ["basic_reports", "manual_transactions"],
  },
  starter: {
    users: 1,
    transactions: 500,
    features: ["basic_reports", "manual_transactions", "bank_sync"],
  },
  professional: {
    users: 3,
    transactions: 2500,
    features: ["basic_reports", "manual_transactions", "bank_sync", "advanced_reports", "projects"],
  },
  business: {
    users: 15,
    transactions: 10000,
    features: ["basic_reports", "manual_transactions", "bank_sync", "advanced_reports", "projects", "api_access", "integrations"],
  },
  unlimited: {
    users: Infinity,
    transactions: Infinity,
    features: ["basic_reports", "manual_transactions", "bank_sync", "advanced_reports", "projects", "api_access", "integrations", "priority_support", "custom_fields"],
  },
} as const;

export type PlanType = keyof typeof PLAN_LIMITS;
export type Feature = string;

// Helper to get organization context
async function getOrganizationContext(userId?: string) {
  const session = await auth.api.getSession({ headers: await headers() });
  const authUserId = userId || session?.user?.id;
  
  if (!authUserId) {
    throw new Error("Not authenticated");
  }

  const orgMember = await db
    .select({
      organizationId: organizationMembers.organizationId,
      role: organizationMembers.role,
    })
    .from(organizationMembers)
    .where(
      and(
        eq(organizationMembers.userId, authUserId),
        eq(organizationMembers.status, "active")
      )
    )
    .limit(1);

  if (!orgMember[0]) {
    throw new Error("No active organization found");
  }

  const [organization] = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, orgMember[0].organizationId));

  if (!organization) {
    throw new Error("Organization not found");
  }

  return {
    organization,
    userRole: orgMember[0].role,
  };
}

// Check if organization has access to a specific feature
export async function hasFeatureAccess(
  feature: Feature,
  userId?: string
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    const { organization } = await getOrganizationContext(userId);
    
    // Check if organization is active
    if (!organization.isActive) {
      return {
        allowed: false,
        reason: "Organization is deactivated",
      };
    }

    // Get plan limits
    const planType = (organization.planType || "trial") as PlanType;
    const planLimits = PLAN_LIMITS[planType];

    // Check if feature is included in plan
    if (!planLimits.features.includes(feature)) {
      return {
        allowed: false,
        reason: `Feature '${feature}' not available in ${planType} plan`,
      };
    }

    return { allowed: true };
  } catch (error) {
    return {
      allowed: false,
      reason: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Check user limit for organization
export async function checkUserLimit(
  userId?: string
): Promise<{ allowed: boolean; current: number; limit: number; reason?: string }> {
  try {
    const { organization } = await getOrganizationContext(userId);
    
    // Get current user count
    const [currentCount] = await db
      .select({ count: count() })
      .from(organizationMembers)
      .where(
        and(
          eq(organizationMembers.organizationId, organization.id),
          eq(organizationMembers.status, "active")
        )
      );

    const planType = (organization.planType || "trial") as PlanType;
    const userLimit = PLAN_LIMITS[planType].users;
    const current = currentCount?.count || 0;

    return {
      allowed: current < userLimit,
      current,
      limit: userLimit,
      reason: current >= userLimit ? `User limit reached (${userLimit} users)` : undefined,
    };
  } catch (error) {
    return {
      allowed: false,
      current: 0,
      limit: 0,
      reason: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Check transaction limit for organization
export async function checkTransactionLimit(
  userId?: string
): Promise<{ allowed: boolean; current: number; limit: number; reason?: string }> {
  try {
    const { organization } = await getOrganizationContext(userId);
    
    const planType = (organization.planType || "trial") as PlanType;
    const transactionLimit = PLAN_LIMITS[planType].transactions;
    const current = organization.monthlyTransactionCount || 0;

    return {
      allowed: current < transactionLimit,
      current,
      limit: transactionLimit,
      reason: current >= transactionLimit ? `Transaction limit reached (${transactionLimit} transactions/month)` : undefined,
    };
  } catch (error) {
    return {
      allowed: false,
      current: 0,
      limit: 0,
      reason: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Comprehensive feature gate check
export async function checkFeatureGate(
  feature: Feature,
  checkType: "feature" | "user_limit" | "transaction_limit" = "feature",
  userId?: string
) {
  switch (checkType) {
    case "feature":
      return await hasFeatureAccess(feature, userId);
    case "user_limit":
      return await checkUserLimit(userId);
    case "transaction_limit":
      return await checkTransactionLimit(userId);
    default:
      return { allowed: false, reason: "Invalid check type" };
  }
}

// Enforce feature gate (throws error if not allowed)
export async function enforceFeatureGate(
  feature: Feature,
  checkType: "feature" | "user_limit" | "transaction_limit" = "feature",
  userId?: string
) {
  const result = await checkFeatureGate(feature, checkType, userId);
  
  if (!result.allowed) {
    throw new Error(result.reason || "Access denied");
  }
  
  return result;
}

// Get organization plan info for UI
export async function getOrganizationPlanInfo(userId?: string) {
  try {
    const { organization } = await getOrganizationContext(userId);
    const planType = (organization.planType || "trial") as PlanType;
    
    // Get current usage
    const userCheck = await checkUserLimit(userId);
    const transactionCheck = await checkTransactionLimit(userId);
    
    return {
      planType,
      planLimits: PLAN_LIMITS[planType],
      usage: {
        users: {
          current: userCheck.current,
          limit: userCheck.limit,
          percentage: Math.round((userCheck.current / userCheck.limit) * 100),
        },
        transactions: {
          current: transactionCheck.current,
          limit: transactionCheck.limit,
          percentage: Math.round((transactionCheck.current / transactionCheck.limit) * 100),
        },
      },
      status: {
        isActive: organization.isActive,
        planStatus: organization.planStatus,
        trialEndsAt: organization.trialEndsAt,
        gracePeriodEndsAt: organization.gracePeriodEndsAt,
      },
    };
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Failed to get plan info");
  }
} 