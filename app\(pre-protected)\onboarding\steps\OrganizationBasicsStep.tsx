import { use<PERSON><PERSON>, <PERSON>mit<PERSON><PERSON><PERSON> } from "react-hook-form";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { UploadCloud } from "lucide-react";
import { Check, X, Loader2 } from "lucide-react";
import { authClient } from '@/lib/auth-client';
import { updateOnboardingStep } from "@/lib/actions/onboarding/updateOnboardingStep";
import { ONBOARDING_STEPS } from "@/lib/onboarding-constants";
import { useOnboardingStore } from "@/hooks/use-onboarding-store";
import { useRouter } from "next/navigation";

const OrganizationBasicsSchema = z.object({
  organizationName: z.string().min(2, "Organization name is required"),
  slug: z.string().min(2, "Organization slug is required").regex(/^[a-z0-9-]+$/, "Slug must be lowercase, alphanumeric, and may include hyphens"),
  logo: z.any().optional(), // Accept File or string or null
});

type OrganizationBasicsSchemaType = z.infer<typeof OrganizationBasicsSchema>;

interface OrganizationBasicsStepProps {
  isLoading: boolean;
  userId: string;
  onNext: (org: { id: string; name: string; slug: string; logo?: string }) => void;
}

export function OrganizationBasicsStep({ isLoading, userId, onNext }: OrganizationBasicsStepProps) {
  const { updateData } = useOnboardingStore();
  const router = useRouter();
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoUploading, setLogoUploading] = useState(false);
  const [logoUploadError, setLogoUploadError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [slugStatus, setSlugStatus] = useState<'idle' | 'checking' | 'available' | 'taken' | 'error'>('idle');
  const slugCheckTimeout = useRef<NodeJS.Timeout | null>(null);

  // Use a single form instance for both the form and the slug watcher
  const form = useForm<OrganizationBasicsSchemaType>({
    resolver: zodResolver(OrganizationBasicsSchema),
    mode: "onBlur",
    defaultValues: {
      organizationName: "",
      slug: "",
      logo: undefined,
    },
  });
  const slugValue = form.watch('slug');

  useEffect(() => {
    const trimmedSlug = (slugValue || '').trim();
    if (!trimmedSlug || !/^[a-z0-9-]+$/.test(trimmedSlug)) {
      setSlugStatus('idle');
      return;
    }
    setSlugStatus('checking');
    if (slugCheckTimeout.current) clearTimeout(slugCheckTimeout.current);
    slugCheckTimeout.current = setTimeout(async () => {
      try {
        console.log('Checking slug payload:', { slug: trimmedSlug });
        const result = await authClient.organization.checkSlug({ slug: trimmedSlug });
        console.log('Slug check result:', result);
        if (result && result.data && typeof result.data.status === 'boolean') {
          setSlugStatus(result.data.status ? 'available' : 'taken');
        } else {
          setSlugStatus('error');
        }
      } catch (err: any) {
        // Check for SLUG_IS_TAKEN in err.error.code
        if (err?.error?.code === 'SLUG_IS_TAKEN') {
          setSlugStatus('taken');
          return;
        }
        setSlugStatus('error');
      }
    }, 400);
    return () => {
      if (slugCheckTimeout.current) clearTimeout(slugCheckTimeout.current);
    };
  }, [slugValue]);

  const uploadLogoToImageKit = async (file: File): Promise<string | null> => {
    setLogoUploading(true);
    setLogoUploadError(null);
    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await fetch('/api/upload-logo', {
        method: 'POST',
        body: formData,
      });
      const data = await res.json();
      if (data.url) {
        setLogoPreview(data.url);
        setLogoUploading(false);
        return data.url;
      } else {
        setLogoUploadError(data.error || 'Upload failed');
        setLogoUploading(false);
        return null;
      }
    } catch (err: any) {
      setLogoUploadError(err?.message || 'Upload failed');
      setLogoUploading(false);
      return null;
    }
  };

  const onLogoChange = async (file: File | null) => {
    setLogoPreview(null);
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setLogoPreview(e.target?.result as string);
      reader.readAsDataURL(file);
      const url = await uploadLogoToImageKit(file);
      if (url) {
        form.setValue('logo', url);
      }
    } else {
      form.setValue('logo', undefined);
    }
  };

  const onSubmit: SubmitHandler<OrganizationBasicsSchemaType> = async (values) => {
    setError(null);
    let logoUrl = values.logo;
    if (logoUrl instanceof File) {
      logoUrl = await uploadLogoToImageKit(logoUrl);
    }
    try {
      const result = await authClient.organization.create({
        name: values.organizationName,
        slug: values.slug,
        logo: logoUrl,
      });
      if (result?.data?.id) {
        // Save org basics to onboarding table
        if (userId) {
          await updateOnboardingStep(userId, ONBOARDING_STEPS.ORGANIZATION_BASICS, {
            id: result.data.id,
            name: values.organizationName,
            slug: values.slug,
            logo: logoUrl,
          });
          updateData({
            organization: {
              name: values.organizationName,
              slug: values.slug,
              logo: logoUrl,
            }
          });
        }
        router.refresh();
        // Optionally, you can call onNext() after refresh if needed
      } else {
        setError("Failed to create organization. Please try again.");
      }
    } catch (err: any) {
      setError(err?.message || "Failed to create organization.");
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6 px-4 sm:px-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white">Create Your Organization</h2>
        <p className="text-sm text-gray-300">Start by providing your organization's name, slug, and logo.</p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card className="bg-gray-800/50 border-gray-600">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg text-white">Organization Basics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="organizationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Acme Inc."
                        onBlur={e => {
                          // Auto-generate slug if not manually edited
                          if (!form.getValues('slug')) {
                            const orgName = e.target.value;
                            const generatedSlug = orgName
                              .toLowerCase()
                              .replace(/[^a-z0-9\s-]/g, "")
                              .replace(/\s+/g, "-")
                              .replace(/-+/g, "-")
                              .replace(/^-+|-+$/g, "");
                            form.setValue("slug", generatedSlug);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField control={form.control} name="slug" render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Slug *</FormLabel>
                  <FormControl>
                    <div className="relative flex items-center">
                      <Input placeholder="your-company-slug" {...field} />
                      {slugStatus === 'checking' && (
                        <Loader2 className="w-4 h-4 text-blue-500 animate-spin absolute right-2" />
                      )}
                      {slugStatus === 'available' && (
                        <Check className="w-4 h-4 text-green-500 absolute right-2" />
                      )}
                      {slugStatus === 'taken' && (
                        <X className="w-4 h-4 text-red-500 absolute right-2" />
                      )}
                      {slugStatus === 'error' && (
                        <X className="w-4 h-4 text-yellow-500 absolute right-2" />
                      )}
                    </div>
                  </FormControl>
                  {slugStatus === 'taken' && <div className="text-xs text-red-600 mt-1">This slug is already taken.</div>}
                  {slugStatus === 'error' && <div className="text-xs text-yellow-600 mt-1">Could not check slug availability.</div>}
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="logo" render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Logo (optional)</FormLabel>
                  <FormControl>
                    <div
                      className={
                        `relative flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer ` +
                        (logoUploading ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-blue-500 hover:bg-blue-50')
                      }
                      onClick={() => {
                        document.getElementById('org-logo-input')?.click();
                      }}
                      onDragOver={e => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onDrop={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        const file = e.dataTransfer.files?.[0];
                        if (file) onLogoChange(file);
                      }}
                      style={{ minHeight: 120 }}
                    >
                      {logoUploading ? (
                        <div className="flex flex-col items-center">
                          <UploadCloud className="w-10 h-10 text-blue-400 animate-bounce mb-2" />
                          <span className="text-blue-600 text-sm">Uploading...</span>
                        </div>
                      ) : logoPreview ? (
                        <div className="flex flex-col items-center">
                          <img src={logoPreview} alt="Logo preview" className="h-20 w-20 object-contain rounded mb-2 border" />
                          <span className="text-xs text-gray-500">Click or drop to change</span>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center">
                          <UploadCloud className="w-10 h-10 text-gray-400 mb-2" />
                          <span className="text-gray-600 text-sm">Click or drag & drop to upload logo</span>
                          <span className="text-xs text-gray-400 mt-1">PNG, JPG, or WebP. Max 10MB.</span>
                        </div>
                      )}
                      <input
                        id="org-logo-input"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={e => onLogoChange(e.target.files?.[0] || null)}
                        tabIndex={-1}
                      />
                    </div>
                  </FormControl>
                  {logoUploadError && <div className="text-xs text-red-600 mt-1">{logoUploadError}</div>}
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
          {error && <div className="text-red-600 text-sm">{error}</div>}
          <Button type="submit" disabled={isLoading} className="w-full">{isLoading ? 'Creating...' : 'Next'}</Button>
        </form>
      </Form>
    </div>
  );
} 