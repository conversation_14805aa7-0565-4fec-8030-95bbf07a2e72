"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";
import { getServerUserContext } from "@/lib/server-auth";
import { moneyReceivedActionSchema } from "@/lib/actions/automation/types";
import { createJournalAutomationService } from "@/lib/actions/automation/journal-automation";
import { db } from "@/db/drizzle";
import * as schema from "@/db/schema/schema";
import { and, eq } from "drizzle-orm";

// Response types
interface BusinessActionResponse {
  success: boolean;
  transactionId?: string;
  message: string;
  error?: string;
  warnings?: string[];
  journalEntries?: any[];
}

/**
 * Process Customer Payment Received
 * Business-friendly action: "I got paid by a customer"
 */
export async function processCustomerPaymentReceived(
  data: z.infer<typeof moneyReceivedActionSchema>
): Promise<BusinessActionResponse> {
  try {
    const { organization } = await getServerUserContext();
    const orgId = organization.id;

    // Validate input
    const validatedData = moneyReceivedActionSchema.parse(data);
    
    // Create journal automation service
    const journalService = await createJournalAutomationService(orgId);
    
    // Process the business action
    const result = await journalService.processBusinessAction(validatedData);
    
    if (result.success && (validatedData as any).invoiceId) {
      const invoiceId = (validatedData as any).invoiceId;
      // Sum all payments for this invoice
      const payments = await db.query.transactions.findMany({
        where: and(
          eq(schema.transactions.invoiceId, invoiceId),
          eq(schema.transactions.organizationId, orgId)
        ),
      });
      const totalPaid = payments.reduce((sum: number, tx: any) => sum + Number(tx.totalAmount), 0);
      // Get the invoice
      const invoice = await db.query.invoices.findFirst({
        where: and(
          eq(schema.invoices.id, invoiceId),
          eq(schema.invoices.organizationId, orgId)
        ),
      });
      if (invoice) {
        const total = Number(invoice.total);
        let overpaid = 0;
        let amountRemaining = total - totalPaid;
        if (amountRemaining < 0) {
          overpaid = Math.abs(amountRemaining);
          amountRemaining = 0;
        }
        let status = invoice.status;
        if (amountRemaining === 0 && overpaid === 0) status = 'paid';
        if (overpaid > 0) status = 'overpaid';
        await db.update(schema.invoices).set({
          amountPaid: totalPaid.toFixed(2),
          amountRemaining: amountRemaining.toFixed(2),
          overpaid: overpaid.toFixed(2),
          status,
        }).where(
          and(
            eq(schema.invoices.id, invoiceId),
            eq(schema.invoices.organizationId, orgId)
          )
        );
      }
    }
    
    if (result.success) {
      revalidatePath("/transactions");
      revalidatePath("/dashboard");
      
      return {
        success: true,
        transactionId: result.transactionId,
        message: `Payment has been recorded successfully!`,
        warnings: result.warnings,
        journalEntries: result.journalEntries
      };
    } else {
      return {
        success: false,
        message: "Failed to process payment",
        error: result.error
      };
    }
  } catch (error) {
    console.error("Error processing customer payment:", error);
    return {
      success: false,
      message: "Failed to process payment",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
} 