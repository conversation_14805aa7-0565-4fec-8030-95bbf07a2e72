import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, CheckCircle, AlertCircle, Clock } from "lucide-react";

interface Vendor {
  vendorId: string;
  vendorName: string;
  totalSpent: number;
  invoiceCount: number;
  averageInvoiceAmount: number;
  paymentTerms: string;
  averagePaymentDays: number;
  discountsTaken: number;
  discountsAvailable: number;
  discountRate: number;
  onTimePaymentRate: number;
  category: string;
}

interface VendorPerformanceData {
  vendors: Vendor[];
  totalSpent: number;
  totalInvoices: number;
  averagePaymentDays: number;
  totalDiscountsTaken: number;
  totalDiscountsAvailable: number;
  period: string;
}

interface Props {
  data: VendorPerformanceData;
}

export default function VendorPerformanceReport({ data }: Props) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    return `${percent.toFixed(1)}%`;
  };

  const getPaymentRatingBadge = (onTimeRate: number) => {
    if (onTimeRate >= 95) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (onTimeRate >= 85) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>;
    if (onTimeRate >= 75) return <Badge className="bg-yellow-100 text-yellow-800">Fair</Badge>;
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>;
  };

  const getDiscountEfficiencyBadge = (discountRate: number) => {
    if (discountRate >= 95) return <Badge className="bg-green-100 text-green-800">High</Badge>;
    if (discountRate >= 75) return <Badge className="bg-blue-100 text-blue-800">Medium</Badge>;
    if (discountRate >= 50) return <Badge className="bg-yellow-100 text-yellow-800">Low</Badge>;
    return <Badge className="bg-gray-100 text-gray-800">None</Badge>;
  };

  const getPaymentIcon = (averageDays: number, terms: string) => {
    const termsDays = terms.includes("30") ? 30 : terms.includes("45") ? 45 : 15;
    if (averageDays <= termsDays) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (averageDays <= termsDays + 5) return <Clock className="w-4 h-4 text-yellow-600" />;
    return <AlertCircle className="w-4 h-4 text-red-600" />;
  };

  // Calculate summary metrics
  const totalDiscountEfficiency = data.totalDiscountsAvailable > 0 
    ? (data.totalDiscountsTaken / data.totalDiscountsAvailable) * 100 
    : 0;

  const averageOnTimeRate = data.vendors.length > 0 
    ? data.vendors.reduce((sum, vendor) => sum + vendor.onTimePaymentRate, 0) / data.vendors.length 
    : 0;

  // Group vendors by category
  const vendorsByCategory = data.vendors.reduce((acc, vendor) => {
    if (!acc[vendor.category]) {
      acc[vendor.category] = [];
    }
    acc[vendor.category].push(vendor);
    return acc;
  }, {} as Record<string, Vendor[]>);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-blue-600">Total Spend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.totalSpent)}</div>
            <p className="text-xs text-gray-600">{data.totalInvoices} invoices</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-green-600">Payment Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(averageOnTimeRate)}</div>
            <p className="text-xs text-gray-600">On-time payment rate</p>
            <Progress value={averageOnTimeRate} className="h-2 mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-purple-600">Discount Efficiency</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(totalDiscountEfficiency)}</div>
            <p className="text-xs text-gray-600">{formatCurrency(data.totalDiscountsTaken)} taken</p>
            <Progress value={totalDiscountEfficiency} className="h-2 mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-orange-600">Avg Payment Days</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.averagePaymentDays.toFixed(0)}</div>
            <p className="text-xs text-gray-600">days average</p>
          </CardContent>
        </Card>
      </div>

      {/* Vendor Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Performance Analysis</CardTitle>
          <CardDescription>
            Detailed performance metrics for {data.period}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {Object.entries(vendorsByCategory).map(([category, vendors]) => (
            <div key={category} className="mb-8">
              <h3 className="text-lg font-semibold mb-4 text-gray-100 bg-gray-800 px-4 py-2 rounded-md">{category}</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vendor</TableHead>
                    <TableHead className="text-right">Total Spent</TableHead>
                    <TableHead className="text-right">Invoices</TableHead>
                    <TableHead className="text-right">Avg Amount</TableHead>
                    <TableHead className="text-center">Payment Terms</TableHead>
                    <TableHead className="text-center">Avg Days</TableHead>
                    <TableHead className="text-center">On-Time Rate</TableHead>
                    <TableHead className="text-center">Discounts</TableHead>
                    <TableHead className="text-center">Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vendors.map((vendor) => (
                    <TableRow key={vendor.vendorId}>
                      <TableCell className="font-medium">{vendor.vendorName}</TableCell>
                      <TableCell className="text-right">{formatCurrency(vendor.totalSpent)}</TableCell>
                      <TableCell className="text-right">{vendor.invoiceCount}</TableCell>
                      <TableCell className="text-right">{formatCurrency(vendor.averageInvoiceAmount)}</TableCell>
                      <TableCell className="text-center">
                        <Badge variant="outline">{vendor.paymentTerms}</Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center space-x-2">
                          {getPaymentIcon(vendor.averagePaymentDays, vendor.paymentTerms)}
                          <span>{vendor.averagePaymentDays}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="space-y-1">
                          <div>{formatPercentage(vendor.onTimePaymentRate)}</div>
                          {getPaymentRatingBadge(vendor.onTimePaymentRate)}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="space-y-1">
                          <div className="text-sm">
                            {formatCurrency(vendor.discountsTaken)} / {formatCurrency(vendor.discountsAvailable)}
                          </div>
                          {getDiscountEfficiencyBadge(vendor.discountRate)}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {getPaymentRatingBadge(vendor.onTimePaymentRate)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Key Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Top Performing Vendors</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.vendors
                .filter(v => v.onTimePaymentRate >= 95)
                .slice(0, 3)
                .map((vendor) => (
                  <div key={vendor.vendorId} className="flex justify-between items-center">
                    <span className="text-sm font-medium">{vendor.vendorName}</span>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm">{formatPercentage(vendor.onTimePaymentRate)}</span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Vendors Needing Attention</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.vendors
                .filter(v => v.onTimePaymentRate < 85 || v.discountRate < 50)
                .slice(0, 3)
                .map((vendor) => (
                  <div key={vendor.vendorId} className="flex justify-between items-center">
                    <span className="text-sm font-medium">{vendor.vendorName}</span>
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm">{formatPercentage(vendor.onTimePaymentRate)}</span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 