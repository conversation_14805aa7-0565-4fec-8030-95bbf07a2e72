import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { db } from "@/db/drizzle";
import { organizations, organizationMembers } from "@/db/schema/schema";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: await headers() });
    
    if (!session?.user) {
      return NextResponse.json({ 
        error: "Not authenticated",
        redirectTo: "/signin"
      });
    }

    const user = session.user;

    // Get user's organizations
    const userOrgs = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        ownerId: organizations.ownerId,
        subscriptionId: organizations.subscriptionId,
        planStatus: organizations.planStatus,
        planType: organizations.planType,
        monthlyTransactionLimit: organizations.monthlyTransactionLimit,
        isActive: organizations.isActive,
        createdAt: organizations.createdAt,
        updatedAt: organizations.updatedAt
      })
      .from(organizations)
      .where(eq(organizations.ownerId, user.id));

    // Get user's memberships
    const memberships = await db
      .select({
        organizationId: organizationMembers.organizationId,
        role: organizationMembers.role,
        status: organizationMembers.status
      })
      .from(organizationMembers)
      .where(eq(organizationMembers.userId, user.id));

    const debugInfo = {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified,
        onboarded: user.onboarded
      },
      organizations: userOrgs,
      memberships: memberships,
      analysis: {
        hasOrganizations: userOrgs.length > 0,
        hasActiveSubscription: userOrgs.some(org => org.subscriptionId && org.planStatus === 'active'),
        hasActiveMembership: memberships.some(m => m.status === 'active'),
        shouldAccessOnboarding: userOrgs.some(org => org.subscriptionId && org.planStatus === 'active'),
        shouldAccessDashboard: user.onboarded && userOrgs.some(org => org.subscriptionId && org.planStatus === 'active') && memberships.some(m => m.status === 'active')
      }
    };

    return NextResponse.json(debugInfo, { status: 200 });
  } catch (error) {
    console.error("Debug endpoint error:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 