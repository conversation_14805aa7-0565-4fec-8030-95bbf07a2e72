import React from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface CashFlowData {
  operatingActivities: any[];
  investingActivities: any[];
  financingActivities: any[];
  netCashFromOperating: number;
  netCashFromInvesting: number;
  netCashFromFinancing: number;
  netChangeInCash: number;
  beginningCash: number;
  endingCash: number;
}

interface Props {
  data: CashFlowData;
}

export default function CashFlowStatement({ data }: Props) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cash Flow Statement</CardTitle>
        <CardDescription>
          Cash flows from operating, investing, and financing activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Operating Activities */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Cash Flows from Operating Activities</h3>
            <div className="pl-4 space-y-2">
              {data.operatingActivities.map((activity, index) => (
                <div key={index} className="flex justify-between">
                  <span>{activity.name}</span>
                  <span>{formatCurrency(activity.amount)}</span>
                </div>
              ))}
              <div className="border-t pt-2 font-semibold flex justify-between">
                <span>Net Cash from Operating Activities</span>
                <span>{formatCurrency(data.netCashFromOperating)}</span>
              </div>
            </div>
          </div>

          {/* Investing Activities */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Cash Flows from Investing Activities</h3>
            <div className="pl-4 space-y-2">
              {data.investingActivities.map((activity, index) => (
                <div key={index} className="flex justify-between">
                  <span>{activity.name}</span>
                  <span>{formatCurrency(activity.amount)}</span>
                </div>
              ))}
              <div className="border-t pt-2 font-semibold flex justify-between">
                <span>Net Cash from Investing Activities</span>
                <span>{formatCurrency(data.netCashFromInvesting)}</span>
              </div>
            </div>
          </div>

          {/* Financing Activities */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Cash Flows from Financing Activities</h3>
            <div className="pl-4 space-y-2">
              {data.financingActivities.map((activity, index) => (
                <div key={index} className="flex justify-between">
                  <span>{activity.name}</span>
                  <span>{formatCurrency(activity.amount)}</span>
                </div>
              ))}
              <div className="border-t pt-2 font-semibold flex justify-between">
                <span>Net Cash from Financing Activities</span>
                <span>{formatCurrency(data.netCashFromFinancing)}</span>
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="border-t-2 pt-4 space-y-2 font-semibold">
            <div className="flex justify-between">
              <span>Net Change in Cash</span>
              <span>{formatCurrency(data.netChangeInCash)}</span>
            </div>
            <div className="flex justify-between">
              <span>Cash at Beginning of Period</span>
              <span>{formatCurrency(data.beginningCash)}</span>
            </div>
            <div className="flex justify-between border-t pt-2">
              <span>Cash at End of Period</span>
              <span>{formatCurrency(data.endingCash)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 