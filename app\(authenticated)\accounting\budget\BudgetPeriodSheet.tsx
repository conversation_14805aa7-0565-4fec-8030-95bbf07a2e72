"use client";

import { useState, useEffect } from "react";
import { She<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle, SheetDescription } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Calendar, DollarSign, TrendingUp, TrendingDown, FileText, Trash2, Star } from "lucide-react";
import { getBudgetPeriodDetails } from "@/lib/actions/budget/get-budget-period-details";
import { deleteBudgetPeriod } from "@/lib/actions/budget/delete-budget-period";
import { toast } from "sonner";

interface BudgetPeriodSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  budgetPeriodId: string | null;
  onPeriodUpdated: () => void;
}

interface BudgetPeriodDetails {
  id: string;
  name: string;
  description: string | null;
  startDate: Date;
  endDate: Date;
  status: "draft" | "active" | "closed" | "archived" | null;
  isDefault: boolean | null;
  lineItemsCount: number;
  budgetSummary: {
    totalBudgetedRevenue: number;
    totalActualRevenue: number;
    revenueVariance: number;
    revenueVariancePercent: number;
    totalBudgetedExpenses: number;
    totalActualExpenses: number;
    expenseVariance: number;
    expenseVariancePercent: number;
    budgetedNetIncome: number;
    actualNetIncome: number;
    netIncomeVariance: number;
    netIncomeVariancePercent: number;
  } | null;
}

export default function BudgetPeriodSheet({
  open,
  onOpenChange,
  budgetPeriodId,
  onPeriodUpdated,
}: BudgetPeriodSheetProps) {
  const [periodDetails, setPeriodDetails] = useState<BudgetPeriodDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    if (open && budgetPeriodId) {
      loadPeriodDetails();
    }
  }, [open, budgetPeriodId]);

  const loadPeriodDetails = async () => {
    if (!budgetPeriodId) return;

    setIsLoading(true);
    try {
      const details = await getBudgetPeriodDetails(budgetPeriodId);
      if (details.error) {
        toast.error(details.error);
        setPeriodDetails(null);
      } else if (details.data) {
        const d = details.data;
        setPeriodDetails({
          id: d.id,
          name: d.name,
          description: d.description ?? null,
          startDate: d.startDate,
          endDate: d.endDate,
          status: d.status ?? null,
          isDefault: d.isDefault ?? null,
          lineItemsCount: Array.isArray(d.lineItems) ? d.lineItems.length : 0,
          budgetSummary: d.budgetSummary ?? null,
        });
      } else {
        setPeriodDetails(null);
      }
    } catch (error) {
      console.error("Error loading period details:", error);
      toast.error("Failed to load budget period details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePeriod = async () => {
    if (!periodDetails) return;

    setIsDeleting(true);
    try {
      const result = await deleteBudgetPeriod(periodDetails.id);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success("Budget period deleted successfully!");
        onPeriodUpdated();
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error deleting period:", error);
      toast.error("Failed to delete budget period");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusBadgeColor = (status: string | null) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "closed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "archived":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent 
          side="right" 
          className="!w-[95vw] sm:!w-[85vw] md:!w-[75vw] lg:!w-[65vw] xl:!w-[55vw] 2xl:!w-[50vw] !max-w-none p-0 gap-0 overflow-hidden"
          aria-describedby="budget-period-description"
        >
          <SheetHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
            <SheetTitle className="text-lg sm:text-xl md:text-2xl">Budget Period Details</SheetTitle>
            <SheetDescription id="budget-period-description">
              View detailed information about this budget period including financial summary and performance metrics
            </SheetDescription>
          </SheetHeader>

          {isLoading ? (
            <div className="flex items-center justify-center py-12 sm:py-16">
              <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 sm:ml-3 text-sm">Loading details...</span>
            </div>
          ) : periodDetails ? (
            <div className="flex-1 overflow-y-auto px-4 pb-4 sm:px-6 sm:pb-6">
              <div className="space-y-4 sm:space-y-6 mt-4 sm:mt-6">
                {/* Header Card - More Compact */}
                <Card className="shadow-sm">
                  <CardHeader className="pb-3 sm:pb-4">
                    <div className="flex flex-col gap-3 sm:gap-4">
                      <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 sm:gap-4">
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-base sm:text-lg md:text-xl truncate mb-1 sm:mb-2">{periodDetails.name}</CardTitle>
                          {periodDetails.description && (
                            <CardDescription className="line-clamp-2 text-xs sm:text-sm">
                              {periodDetails.description}
                            </CardDescription>
                          )}
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          {periodDetails.isDefault && (
                            <Badge variant="outline" className="whitespace-nowrap px-2 py-1 text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Default
                            </Badge>
                          )}
                          <Badge className={`${getStatusBadgeColor(periodDetails.status)} whitespace-nowrap px-2 py-1 text-xs`}>
                            {periodDetails.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div className="space-y-1 sm:space-y-2">
                        <div className="flex items-center space-x-2 text-xs sm:text-sm">
                          <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
                          <span className="text-gray-600 dark:text-gray-400">Start Date:</span>
                        </div>
                        <p className="font-medium text-xs sm:text-sm">{periodDetails.startDate.toLocaleDateString()}</p>
                      </div>
                      <div className="space-y-1 sm:space-y-2">
                        <div className="flex items-center space-x-2 text-xs sm:text-sm">
                          <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
                          <span className="text-gray-600 dark:text-gray-400">End Date:</span>
                        </div>
                        <p className="font-medium text-xs sm:text-sm">{periodDetails.endDate.toLocaleDateString()}</p>
                      </div>
                      <div className="space-y-1 sm:space-y-2">
                        <div className="flex items-center space-x-2 text-xs sm:text-sm">
                          <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
                          <span className="text-gray-600 dark:text-gray-400">Budget Items:</span>
                        </div>
                        <p className="font-medium text-xs sm:text-sm">{periodDetails.lineItemsCount}</p>
                      </div>
                      <div className="space-y-1 sm:space-y-2">
                        <div className="flex items-center space-x-2 text-xs sm:text-sm">
                          <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 flex-shrink-0" />
                          <span className="text-gray-600 dark:text-gray-400">Status:</span>
                        </div>
                        <p className="font-medium text-xs sm:text-sm capitalize">{periodDetails.status}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Performance - Better Grid Layout */}
                {periodDetails.budgetSummary && (
                  <Card className="shadow-sm">
                    <CardHeader className="pb-3 sm:pb-4">
                      <CardTitle className="text-base sm:text-lg">Financial Performance</CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Budget vs actual comparison for the period
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4">
                        {/* Revenue */}
                        <div className="bg-green-50 dark:bg-green-900/20 p-3 sm:p-4 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-center justify-between mb-2 sm:mb-3">
                            <div className="flex items-center space-x-2">
                              <div className="p-1.5 sm:p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                                <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-600 dark:text-green-400" />
                              </div>
                              <span className="font-semibold text-xs sm:text-sm text-green-800 dark:text-green-200">Revenue</span>
                            </div>
                          </div>
                          <div className="space-y-2 sm:space-y-3">
                            <div>
                              <div className="text-sm sm:text-base md:text-lg font-bold text-green-800 dark:text-green-200">
                                {formatCurrency(periodDetails.budgetSummary.totalActualRevenue)}
                              </div>
                              <div className="text-xs text-green-600 dark:text-green-400">
                                Budget: {formatCurrency(periodDetails.budgetSummary.totalBudgetedRevenue)}
                              </div>
                            </div>
                            <div className="border-t border-green-200 dark:border-green-700 pt-2">
                              <div className="flex justify-between items-center">
                                <span className="text-green-700 dark:text-green-300 font-medium text-xs">Variance</span>
                                <div className="text-right">
                                  <div className={`text-xs sm:text-sm font-bold ${
                                    periodDetails.budgetSummary.revenueVariance >= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-600 dark:text-red-400'
                                  }`}>
                                    {formatCurrency(periodDetails.budgetSummary.revenueVariance)}
                                  </div>
                                  <div className={`text-xs ${
                                    periodDetails.budgetSummary.revenueVariancePercent >= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-600 dark:text-red-400'
                                  }`}>
                                    {periodDetails.budgetSummary.revenueVariancePercent >= 0 ? '+' : ''}{periodDetails.budgetSummary.revenueVariancePercent.toFixed(1)}%
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Expenses */}
                        <div className="bg-red-50 dark:bg-red-900/20 p-3 sm:p-4 rounded-lg border border-red-200 dark:border-red-800">
                          <div className="flex items-center justify-between mb-2 sm:mb-3">
                            <div className="flex items-center space-x-2">
                              <div className="p-1.5 sm:p-2 bg-red-100 dark:bg-red-800 rounded-lg">
                                <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 text-red-600 dark:text-red-400" />
                              </div>
                              <span className="font-semibold text-xs sm:text-sm text-red-800 dark:text-red-200">Expenses</span>
                            </div>
                          </div>
                          <div className="space-y-2 sm:space-y-3">
                            <div>
                              <div className="text-sm sm:text-base md:text-lg font-bold text-red-800 dark:text-red-200">
                                {formatCurrency(periodDetails.budgetSummary.totalActualExpenses)}
                              </div>
                              <div className="text-xs text-red-600 dark:text-red-400">
                                Budget: {formatCurrency(periodDetails.budgetSummary.totalBudgetedExpenses)}
                              </div>
                            </div>
                            <div className="border-t border-red-200 dark:border-red-700 pt-2">
                              <div className="flex justify-between items-center">
                                <span className="text-red-700 dark:text-red-300 font-medium text-xs">Variance</span>
                                <div className="text-right">
                                  <div className={`text-xs sm:text-sm font-bold ${
                                    periodDetails.budgetSummary.expenseVariance <= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-600 dark:text-red-400'
                                  }`}>
                                    {formatCurrency(periodDetails.budgetSummary.expenseVariance)}
                                  </div>
                                  <div className={`text-xs ${
                                    periodDetails.budgetSummary.expenseVariancePercent <= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-600 dark:text-red-400'
                                  }`}>
                                    {periodDetails.budgetSummary.expenseVariancePercent >= 0 ? '+' : ''}{periodDetails.budgetSummary.expenseVariancePercent.toFixed(1)}%
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Net Income */}
                        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 sm:p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                          <div className="flex items-center justify-between mb-2 sm:mb-3">
                            <div className="flex items-center space-x-2">
                              <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                                <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 dark:text-blue-400" />
                              </div>
                              <span className="font-semibold text-xs sm:text-sm text-blue-800 dark:text-blue-200">Net Income</span>
                            </div>
                          </div>
                          <div className="space-y-2 sm:space-y-3">
                            <div>
                              <div className="text-sm sm:text-base md:text-lg font-bold text-blue-800 dark:text-blue-200">
                                {formatCurrency(periodDetails.budgetSummary.actualNetIncome)}
                              </div>
                              <div className="text-xs text-blue-600 dark:text-blue-400">
                                Budget: {formatCurrency(periodDetails.budgetSummary.budgetedNetIncome)}
                              </div>
                            </div>
                            <div className="border-t border-blue-200 dark:border-blue-700 pt-2">
                              <div className="flex justify-between items-center">
                                <span className="text-blue-700 dark:text-blue-300 font-medium text-xs">Variance</span>
                                <div className="text-right">
                                  <div className={`text-xs sm:text-sm font-bold ${
                                    periodDetails.budgetSummary.netIncomeVariance >= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-600 dark:text-red-400'
                                  }`}>
                                    {formatCurrency(periodDetails.budgetSummary.netIncomeVariance)}
                                  </div>
                                  <div className={`text-xs ${
                                    periodDetails.budgetSummary.netIncomeVariancePercent >= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-600 dark:text-red-400'
                                  }`}>
                                    {periodDetails.budgetSummary.netIncomeVariancePercent >= 0 ? '+' : ''}{periodDetails.budgetSummary.netIncomeVariancePercent.toFixed(1)}%
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Action Buttons - More Compact */}
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-3 sm:pt-4 border-t">
                  <Button
                    variant="destructive"
                    onClick={() => setShowDeleteDialog(true)}
                    disabled={isDeleting}
                    className="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-xs sm:text-sm"
                  >
                    <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    Delete Period
                  </Button>
                  <div className="flex-1"></div>
                  <Button
                    onClick={() => onOpenChange(false)}
                    variant="outline"
                    className="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-xs sm:text-sm"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 sm:py-16 px-4">
              <p className="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">
                Budget period details not found.
              </p>
            </div>
          )}
        </SheetContent>
      </Sheet>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Budget Period</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{periodDetails?.name}"? This will permanently delete
              the budget period and all associated budget line items.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePeriod}
              className="bg-red-600 hover:bg-red-700"
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Period"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 