"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { NewVendor, vendorFormSchema } from "@/lib/actions/vendors/vendor.schema";
import { useQuery } from "@tanstack/react-query";
import { getVendorById } from "@/lib/actions/vendors/get-vendor-by-id";

interface AddVendorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (vendorData: NewVendor) => void;
  isPending: boolean;
  vendorId?: string | null;
}

export default function AddVendorDialog({
  open,
  onOpenChange,
  onSubmit,
  isPending,
  vendorId,
}: AddVendorDialogProps) {
  const isEdit = !!vendorId;
  const { data: vendor, isLoading: isLoadingVendor } = useQuery({
    queryKey: ["vendor", vendorId],
    queryFn: () => vendorId ? getVendorById(vendorId) : null,
    enabled: !!vendorId && open,
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<NewVendor>({
    resolver: zodResolver(vendorFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      category: "",
      address: {
        street: "",
        city: "",
        zip: "",
        country: "",
      },
    },
  });

  useEffect(() => {
    if (vendor) {
      reset({
        name: vendor.name,
        email: vendor.email ?? "",
        phone: vendor.phone ?? "",
        category: vendor.category ?? "",
        address: {
          street: (vendor.address as any)?.street ?? "",
          city: (vendor.address as any)?.city ?? "",
          zip: (vendor.address as any)?.zip ?? "",
          country: (vendor.address as any)?.country ?? "",
        },
      });
    } else {
      reset({
        name: "",
        email: "",
        phone: "",
        category: "",
        address: {
          street: "",
          city: "",
          zip: "",
          country: "",
        },
      });
    }
  }, [vendor, reset]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{vendor ? "Edit Vendor" : "Add Vendor"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Label>Name</Label>
            <Input {...register("name")} />
            {errors.name && (
              <p className="text-red-500 text-xs">{errors.name.message}</p>
            )}
          </div>
          <div>
            <Label>Email</Label>
            <Input {...register("email")} />
            {errors.email && (
              <p className="text-red-500 text-xs">{errors.email.message}</p>
            )}
          </div>
          <div>
            <Label>Phone</Label>
            <Input {...register("phone")} />
          </div>
          <div>
            <Label>Category</Label>
            <Input {...register("category")} />
          </div>
          <div className="space-y-2">
            <Label>Address</Label>
            <Input {...register("address.street")} placeholder="Street" />
            <Input {...register("address.city")} placeholder="City" />
            <Input {...register("address.zip")} placeholder="ZIP / Postal Code" />
            <Input {...register("address.country")} placeholder="Country" />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="ghost"
              onClick={() => onOpenChange(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
