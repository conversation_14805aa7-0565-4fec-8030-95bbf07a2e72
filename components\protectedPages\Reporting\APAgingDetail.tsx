import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface Bill {
  id: string;
  billNumber: string;
  date: Date;
  dueDate: Date | null;
  total: string;
  status: string | null;
  vendorId: string;
  vendorName: string | null;
  daysPastDue: number;
  agingBucket: string;
  amount: number;
}

interface APAgingDetailData {
  bills: Bill[];
  totalAmount: number;
}

interface Props {
  data: APAgingDetailData;
}

export default function APAgingDetail({ data }: Props) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString();
  };

  const getAgingBadgeColor = (bucket: string) => {
    switch (bucket) {
      case "current":
        return "bg-green-100 text-green-800";
      case "1-30":
        return "bg-blue-100 text-blue-800";
      case "31-60":
        return "bg-yellow-100 text-yellow-800";
      case "61-90":
        return "bg-orange-100 text-orange-800";
      case "90+":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getAgingBadgeLabel = (bucket: string) => {
    switch (bucket) {
      case "current":
        return "Current";
      case "1-30":
        return "1-30 Days";
      case "31-60":
        return "31-60 Days";
      case "61-90":
        return "61-90 Days";
      case "90+":
        return "90+ Days";
      default:
        return bucket;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Accounts Payable Aging Detail</CardTitle>
        <CardDescription>
          Detailed view of outstanding bills ({data.bills.length} bills, {formatCurrency(data.totalAmount)} total)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Bill #</TableHead>
              <TableHead>Vendor</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Days Past Due</TableHead>
              <TableHead>Aging</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.bills.map((bill) => (
              <TableRow key={bill.id}>
                <TableCell className="font-medium">{bill.billNumber}</TableCell>
                <TableCell>{bill.vendorName || "Unknown Vendor"}</TableCell>
                <TableCell>{formatDate(bill.date)}</TableCell>
                <TableCell>{formatDate(bill.dueDate)}</TableCell>
                <TableCell>
                  <span className={bill.daysPastDue > 0 ? "text-red-600" : "text-green-600"}>
                    {bill.daysPastDue}
                  </span>
                </TableCell>
                <TableCell>
                  <Badge className={getAgingBadgeColor(bill.agingBucket)}>
                    {getAgingBadgeLabel(bill.agingBucket)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">{formatCurrency(bill.amount)}</TableCell>
              </TableRow>
            ))}
            {data.bills.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center text-gray-500 py-8">
                  No outstanding bills found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
} 