"use client";

import React from "react";
import TransactionEntryRow from "./TransactionEntryRow";

export type TransactionEntry = {
  id: string;
  accountId: string;
  debit: string;
  credit: string;
  description?: string;
};

interface Account {
  id: string;
  name: string;
  type: string;
}

interface TransactionEntriesSectionProps {
  entries: TransactionEntry[];
  onChange: (entries: TransactionEntry[]) => void;
  accounts: Account[];
}

export default function TransactionEntriesSection({ entries, onChange, accounts }: TransactionEntriesSectionProps) {
  const handleAdd = () => {
    onChange([
      ...entries,
      { id: `${Date.now()}-${Math.random()}`, accountId: "", debit: "", credit: "", description: "" },
    ]);
  };
  const handleChange = (idx: number, entry: TransactionEntry) => {
    const updated = [...entries];
    updated[idx] = entry;
    onChange(updated);
  };
  const handleRemove = (idx: number) => {
    const updated = [...entries];
    updated.splice(idx, 1);
    onChange(updated);
  };
  return (
    <div className="space-y-2">
      {entries.length === 0 && (
        <div className="bg-white/5 rounded p-4 text-gray-400">No entries yet.</div>
      )}
      {entries.map((entry, idx) => (
        <TransactionEntryRow
          key={entry.id}
          entry={entry}
          onChange={e => handleChange(idx, e)}
          onRemove={() => handleRemove(idx)}
          accounts={accounts}
        />
      ))}
      <button
        type="button"
        className="mt-2 px-3 py-1 bg-blue-600 text-white rounded"
        onClick={handleAdd}
      >
        Add Entry
      </button>
    </div>
  );
} 