'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Calendar, AlertTriangle, Download, Trash2, <PERSON>, Crown, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { authClient } from '@/lib/auth-client';
import { cancelSubscriptionAction, exportDataAction } from '@/lib/actions/billing/subscription';
import { ServerUserContext } from '@/lib/server-auth';

const plans = {
  starter: {
    name: 'Starter',
    price: 29,
    features: ['Automated categorization', 'Basic reporting', 'Email support', 'Bank connections', 'Invoice management'],
    users: 1,
    transactions: 50,
    monthlyProductId: "a700aa67-b982-4c2d-9231-abb57acf64d0",
    annualProductId: "7f159a03-91fb-4eb9-8941-6b600976024c",
  },
  professional: {
    name: 'Professional',
    price: 129,
    features: ['Everything in Starter', 'Project tracking', 'Client portal', 'Advanced reporting', 'Priority support', 'Custom branding'],
    users: 3,
    transactions: 500,
    monthlyProductId: "e2273e83-a20d-48e4-b0b6-a7b9e8543491",
    annualProductId: "1dca0ff7-2548-4c3f-9a4e-eac854a8fa5c",
  },
  business: {
    name: 'Business',
    price: 499,
    features: ['Everything in Professional', 'Multi-location', 'Advanced analytics', 'Team management', 'API access', 'Dedicated manager'],
    users: 15,
    transactions: 2500,
    monthlyProductId: "0fce1d49-7998-4e65-9649-8776ffec76b2",
    annualProductId: "c326bf62-44c5-4695-ab21-41d43ff43cf9",
  },
  unlimited: {
    name: 'Unlimited',
    price: 999,
    features: ['Everything in Business', 'Unlimited users', 'White-glove onboarding', 'Custom development', '24/7 support', 'SLA guarantee'],
    users: 'unlimited',
    transactions: 'unlimited',
    monthlyProductId: "ec35442c-9c52-4091-9f0f-f5aa1719489e",
    annualProductId: "cf96cc29-666f-4416-9e73-1412ec0b8f8a",
  }
};

type PlanKey = keyof typeof plans;
interface BillingPageClientProps {
  organization: ServerUserContext['organization'];
}

export function BillingPageClient({ organization }: BillingPageClientProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleUpgradePlan = async (slug: string) => {
    setIsProcessing(true);
    try {
      await authClient.checkout({ slug });
      // The success event is handled by our onboarding/redirect logic now
      // A simple refresh on close might be good.
      toast.success("Redirecting to checkout...");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to start upgrade process.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelSubscription = async () => {
    startTransition(async () => {
      const result = await cancelSubscriptionAction();
      if (result.success) {
        toast.success("Subscription cancellation initiated successfully.");
        router.refresh();
      } else {
        toast.error(result.error);
      }
    });
  };

  const handleExportData = async () => {
    startTransition(async () => {
      toast.info("Preparing your data export...");
      const result = await exportDataAction();
      
      if (result.success && result.data) {
        const blob = new Blob([result.data], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${organization.name}-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Data export completed successfully!');
      } else {
          toast.error(result.error || "An unknown error occurred during data export.");
      }
    });
  };

  const currentPlanKey = organization.planType as PlanKey;
  const currentPlan = currentPlanKey ? plans[currentPlanKey] : null;
  const transactionUsagePercent = currentPlan && organization.monthlyTransactionCount && currentPlan.transactions !== 'unlimited' 
    ? (organization.monthlyTransactionCount / (currentPlan.transactions as number)) * 100 
    : 0;
  const isTrialing = organization.isTrialing;
  const isCancelled = organization.planStatus === 'cancelled';

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Billing & Subscription</h1>
          <p className="text-muted-foreground">Manage your subscription and billing preferences</p>
        </div>
        
        {/* We would get the portal link from Polar and redirect */}
        {/* <Button variant="outline" disabled> 
          <CreditCard className="w-4 h-4 mr-2" />
          Manage Payment Methods
        </Button> */}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Current Plan */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Crown className="w-5 h-5 mr-2" />
                Current Plan
              </CardTitle>
              <Badge variant={isCancelled ? 'destructive' : isTrialing ? 'secondary' : 'default'}>
                {isCancelled ? 'Cancelled' : isTrialing ? 'Trial' : 'Active'}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-2xl font-bold capitalize">{organization.planType}</h3>
                <p className="text-muted-foreground">
                  ${currentPlan?.price || 0}/month
                </p>
              </div>
            </div>

            {isTrialing && organization.trialEndsAt && (
                <Alert>
                    <Calendar className="h-4 w-4" />
                    <AlertTitle>Trial Period</AlertTitle>
                    <AlertDescription>
                        Your trial ends on <strong>{new Date(organization.trialEndsAt).toLocaleDateString()}</strong>.
                    </AlertDescription>
                </Alert>
            )}

            {isCancelled && organization.subscriptionCurrentPeriodEnd && (
                <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Subscription Cancelled</AlertTitle>
                    <AlertDescription>
                        Your access will end on <strong>{new Date(organization.subscriptionCurrentPeriodEnd).toLocaleDateString()}</strong>.
                    </AlertDescription>
                </Alert>
            )}
            
            <Separator />

            <div>
              <h4 className="font-medium mb-2">Plan Usage</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Users</span>
                  <span className="font-mono">1 / {currentPlan?.users}</span>
                </div>
                <div className="flex justify-between">
                  <span>Transactions</span>
                  <span className="font-mono">{organization.monthlyTransactionCount} / {currentPlan?.transactions.toLocaleString()}</span>
                </div>
                <Progress value={transactionUsagePercent} className="mt-1" />
              </div>
            </div>

            <Separator />
            
            <div>
              <h4 className="font-medium mb-2">Features</h4>
              <ul className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                {currentPlan?.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <Check className="w-4 h-4 text-green-500 mr-2" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Dialog>
              <DialogTrigger asChild>
                <Button className="w-full" disabled={isCancelled || isProcessing}>Change Plan</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Change Your Plan</DialogTitle>
                  <DialogDescription>Select a new plan for your organization.</DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-1 gap-4 py-4">
                  {Object.entries(plans).map(([key, plan]) => (
                    <Card key={key} className={currentPlanKey === key ? 'border-primary' : ''}>
                      <CardHeader>
                        <div className="flex justify-between">
                          <CardTitle className="capitalize">{plan.name}</CardTitle>
                          {currentPlanKey === key && <Badge>Current</Badge>}
                        </div>
                        <p className="font-bold">${plan.price}/month</p>
                        <p className="text-sm text-muted-foreground">{plan.features[1]}</p>
                        <Button 
                          onClick={() => handleUpgradePlan(plan.monthlyProductId)} 
                          disabled={currentPlanKey === key || isProcessing}
                          size="sm"
                          className="mt-2"
                        >
                          {isProcessing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : currentPlan && currentPlan.price < plan.price ? 'Upgrade' : 'Downgrade'}
                        </Button>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </DialogContent>
            </Dialog>

            <Separator />
            
            <div className="space-y-2">
              <h4 className="font-medium">Danger Zone</h4>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" className="w-full">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Cancel Subscription
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will cancel your subscription at the end of the current billing period.
                      You will retain access to all features until then.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Keep Subscription</AlertDialogCancel>
                    <AlertDialogAction onClick={handleCancelSubscription} disabled={isPending}>
                      {isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                      Proceed with Cancellation
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <Button variant="outline" className="w-full" onClick={handleExportData} disabled={isPending}>
                {isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="w-4 h-4 mr-2" />}
                Export My Data
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 