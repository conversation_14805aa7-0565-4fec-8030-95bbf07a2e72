"use server";

import { db } from "@/db/drizzle";
import { auditLogs } from "@/db/schema/schema";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { eq, and } from "drizzle-orm";

async function getUserOrganizationId(userId: string) {
  const { member } = await import("@/db/schema/schema");
  const orgs = await db
    .select({ organizationId: member.organizationId })
    .from(member)
.where(eq(member.userId, userId));
  return orgs[0]?.organizationId || null;
}

export async function getTransactionAuditLog(transactionId: string) {
  const session = await auth.api.getSession({ headers: await headers() });
  const userId = session?.user?.id;
  if (!userId) return [];
  const organizationId = await getUserOrganizationId(userId);
  if (!organizationId) return [];

  return db
    .select()
    .from(auditLogs)
    .where(and(eq(auditLogs.resourceType, "transaction"), eq(auditLogs.resourceId, transactionId), eq(auditLogs.organizationId, organizationId)));
} 