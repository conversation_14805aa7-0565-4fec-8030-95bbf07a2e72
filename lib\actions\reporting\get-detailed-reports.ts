"use server";

import { getServerUserContext } from "@/lib/server-auth";
import { getVendorPerformanceReport } from "./get-vendor-performance-report";
import { getExpenseAnalysisReport } from "./get-expense-analysis-report";

export async function getDetailedReports(fromDate?: string, toDate?: string) {
  const { organization } = await getServerUserContext();
  const organizationId = organization.id;

  const [vendorPerformance, expenseAnalysis] = await Promise.all([
    getVendorPerformanceReport(fromDate, toDate),
    getExpenseAnalysisReport(fromDate, toDate)
  ]);

  return {
    vendorPerformance,
    expenseAnalysis
  };
} 