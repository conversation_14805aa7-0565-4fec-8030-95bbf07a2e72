"use server";

import { db } from "@/db/drizzle";
import { budgetPeriods, budgetLineItems } from "@/db/schema/schema";
import { getServerUserContext } from "@/lib/server-auth";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function deleteBudgetPeriod(budgetPeriodId: string) {
  await getServerUserContext();
  try {
    await db.delete(budgetLineItems).where(eq(budgetLineItems.budgetPeriodId, budgetPeriodId));
    await db.delete(budgetPeriods).where(eq(budgetPeriods.id, budgetPeriodId));
    revalidatePath("/accounting/budget");
    return { success: true };
  } catch (error) {
    console.error("Error deleting budget period:", error);
    return { error: "Failed to delete budget period" };
  }
} 