# Kit.com (ConvertKit) Integration Plan for Waitlist Funnel

## Overview
This document provides a complete step-by-step guide for integrating Kit.com (formerly ConvertKit) with your NextGen Business waitlist funnel page. This integration will enable professional email capture, automated sequences, and marketing automation for your early access list.

---

## Phase 1: Kit.com Account Setup

### Step 1: Create Kit.com Account
1. **Visit** [kit.com](https://kit.com) and click "Get Started"
2. **Sign up** with your business email
3. **Complete** the onboarding wizard
4. **Verify** your email address

### Step 2: Configure Account Settings
1. **Navigate to** Settings → Account
2. **Set up** your sender information:
   - Name: "NextGen Business"
   - Email: your business email (e.g., <EMAIL>)
   - Reply-to: your business email
3. **Configure** your timezone and date format
4. **Set up** your business address (required for CAN-SPAM compliance)

### Step 3: Create Your First Form
1. **Go to** Forms → Create Form
2. **Choose** "Inline Form" (for custom styling)
3. **Configure** the form:
   - Name: "NextGen Business Waitlist"
   - Description: "Join our early access list"
   - Fields: Email (required)
4. **Save** the form

### Step 4: Set Up Tags and Sequences
1. **Create tags** for segmentation:
   - `waitlist-signup`
   - `early-access`
   - `beta-user`
2. **Create a welcome sequence**:
   - Go to Sequences → Create Sequence
   - Name: "Waitlist Welcome"
   - Add subscribers to this sequence when they join the waitlist

---

## Phase 2: API Integration Setup

### Step 1: Get API Credentials
1. **Navigate to** Settings → Advanced → API
2. **Copy** your API Key (you'll need this for server-side integration)
3. **Note** your Account ID (found in the URL: `https://app.convertkit.com/account/{account_id}`)

### Step 2: Install Required Dependencies
```bash
# Install the ConvertKit/Kit.com SDK
npm install @convertkit/convertkit-js
# OR if using yarn
yarn add @convertkit/convertkit-js
```

### Step 3: Environment Variables
Add to your `.env.local`:
```env
KIT_API_KEY=your_api_key_here
KIT_FORM_ID=your_form_id_here
KIT_ACCOUNT_ID=your_account_id_here
```

---

## Phase 3: Backend Integration

### Step 1: Create API Route
Create `app/api/waitlist/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';

const KIT_API_KEY = process.env.KIT_API_KEY;
const KIT_FORM_ID = process.env.KIT_FORM_ID;

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Add subscriber to Kit.com
    const response = await fetch(`https://api.convertkit.com/v3/forms/${KIT_FORM_ID}/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${KIT_API_KEY}`,
      },
      body: JSON.stringify({
        email: email,
        tags: ['waitlist-signup', 'early-access'],
      }),
    });

    if (!response.ok) {
      throw new Error(`Kit.com API error: ${response.status}`);
    }

    const data = await response.json();

    return NextResponse.json({
      success: true,
      message: 'Successfully joined the waitlist!',
      subscriber: data.subscription
    });

  } catch (error) {
    console.error('Waitlist signup error:', error);
    return NextResponse.json(
      { error: 'Failed to join waitlist. Please try again.' },
      { status: 500 }
    );
  }
}
```

### Step 2: Create Server Action (Alternative)
Create `lib/actions/waitlist/join-waitlist.ts`:

```typescript
"use server";

export async function joinWaitlistAction(email: string) {
  try {
    const KIT_API_KEY = process.env.KIT_API_KEY;
    const KIT_FORM_ID = process.env.KIT_FORM_ID;

    if (!KIT_API_KEY || !KIT_FORM_ID) {
      throw new Error('Kit.com configuration missing');
    }

    const response = await fetch(`https://api.convertkit.com/v3/forms/${KIT_FORM_ID}/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${KIT_API_KEY}`,
      },
      body: JSON.stringify({
        email: email,
        tags: ['waitlist-signup', 'early-access'],
      }),
    });

    if (!response.ok) {
      throw new Error(`Kit.com API error: ${response.status}`);
    }

    const data = await response.json();

    return {
      success: true,
      message: 'Successfully joined the waitlist!',
      subscriber: data.subscription
    };

  } catch (error) {
    console.error('Waitlist signup error:', error);
    return {
      success: false,
      error: 'Failed to join waitlist. Please try again.'
    };
  }
}
```

---

## Phase 4: Frontend Integration

### Step 1: Update Waitlist Page
Update `app/waitlist/page.tsx`:

```typescript
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CheckCircle, Mail } from "lucide-react";
import { joinWaitlistAction } from "@/lib/actions/waitlist/join-waitlist";

export default function WaitlistPage() {
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const result = await joinWaitlistAction(email);
      
      if (result.success) {
        setSubmitted(true);
      } else {
        setError(result.error || "Something went wrong. Please try again.");
      }
    } catch (error) {
      setError("Failed to join waitlist. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center px-4 py-12">
      <div className="max-w-2xl w-full">
        <Card className="shadow-xl border-primary/20">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-primary mb-2">
              NextGen Business
            </CardTitle>
            <p className="text-lg text-muted-foreground mb-4">
              All-in-One Business Management Platform
            </p>
            <p className="text-base text-foreground">
              NextGen Business is a modern, intelligent business management platform designed to make financial management, project tracking, and team collaboration effortless—even for users with no accounting background. It automates complex accounting, provides real-time insights, and empowers business owners, freelancers, and teams to focus on growth, not paperwork.
            </p>
          </CardHeader>
          <CardContent>
            {/* Feature list remains the same */}
            <div className="grid gap-6 my-8">
              <ol className="grid gap-6 text-base text-foreground list-decimal list-inside [--list-marker-color:theme(colors.primary)] marker:text-primary">
                {/* ... existing feature list ... */}
              </ol>
            </div>
            
            <div className="my-8 text-center">
              <h2 className="text-2xl font-bold mb-2 text-primary">Join the Waitlist</h2>
              <p className="text-base text-muted-foreground mb-4">
                Be the first to experience NextGen Business. Get early access, shape the product, and unlock exclusive offers!
              </p>
              
              {submitted ? (
                <div className="flex flex-col items-center gap-2">
                  <CheckCircle className="w-10 h-10 text-green-500 mb-2" />
                  <span className="text-lg font-semibold text-green-600">Thank you! You're on the list.</span>
                  <span className="text-sm text-muted-foreground">We'll keep you updated with early access and special offers.</span>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row items-center gap-3 justify-center mt-4">
                  <div className="relative w-full sm:w-auto">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground w-5 h-5" />
                    <Input
                      type="email"
                      required
                      placeholder="Your email address"
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full sm:w-72 bg-card border border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary"
                      disabled={loading}
                    />
                  </div>
                  <Button 
                    type="submit" 
                    size="lg" 
                    className="w-full sm:w-auto mt-2 sm:mt-0"
                    disabled={loading}
                  >
                    {loading ? "Joining..." : "Join Waitlist"}
                  </Button>
                </form>
              )}
              
              {error && (
                <div className="mt-4 text-center">
                  <span className="text-sm text-red-600">{error}</span>
                </div>
              )}
            </div>
            
            <div className="mt-8 text-center text-sm text-muted-foreground">
              <span>Ready to simplify your business and supercharge your growth? <span className="text-primary font-semibold">Join the waitlist and get early access to NextGen Business!</span></span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

---

## Phase 5: Email Sequence Setup

### Step 1: Create Welcome Email
1. **Go to** Sequences → "Waitlist Welcome"
2. **Add** a welcome email:
   - Subject: "Welcome to NextGen Business! 🚀"
   - Content: Thank them for joining, explain what's coming next
3. **Set** delay to 0 minutes (send immediately)

### Step 2: Create Follow-up Sequence
1. **Create** a new sequence: "Early Access Updates"
2. **Add** emails for:
   - Product updates and progress
   - Feature announcements
   - Beta testing invitations
   - Launch announcements

### Step 3: Set Up Automation
1. **Configure** when subscribers are added to sequences
2. **Set up** tags for different subscriber types
3. **Create** broadcast templates for major announcements

---

## Phase 6: Testing and Validation

### Step 1: Test Email Capture
1. **Submit** test emails through your waitlist form
2. **Verify** emails appear in Kit.com dashboard
3. **Check** that welcome sequences are triggered
4. **Test** on different devices and browsers

### Step 2: Test API Integration
```bash
# Test the API endpoint directly
curl -X POST http://localhost:3000/api/waitlist \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

### Step 3: Monitor and Debug
1. **Check** browser console for errors
2. **Monitor** Kit.com dashboard for new subscribers
3. **Verify** email delivery and open rates
4. **Test** unsubscribe functionality

---

## Phase 7: Advanced Features

### Step 1: Add Custom Fields
```typescript
// Add more subscriber data
body: JSON.stringify({
  email: email,
  tags: ['waitlist-signup', 'early-access'],
  fields: {
    source: 'waitlist-page',
    signup_date: new Date().toISOString(),
  }
})
```

### Step 2: Implement Double Opt-in
1. **Enable** double opt-in in Kit.com settings
2. **Update** your form to mention confirmation email
3. **Test** the confirmation flow

### Step 3: Add Analytics
```typescript
// Track signup events
import { track } from '@/lib/analytics';

const handleSubmit = async (e: React.FormEvent) => {
  // ... existing code ...
  
  if (result.success) {
    track('waitlist_signup', { email });
    setSubmitted(true);
  }
};
```

---

## Phase 8: Launch and Monitor

### Step 1: Go Live
1. **Deploy** your updated waitlist page
2. **Test** the live integration
3. **Monitor** signup rates and email performance

### Step 2: Optimize
1. **Track** conversion rates
2. **A/B test** different copy and CTAs
3. **Monitor** email engagement
4. **Iterate** based on data

### Step 3: Scale
1. **Create** additional landing pages
2. **Set up** retargeting campaigns
3. **Implement** advanced segmentation
4. **Plan** launch sequence

---

## Troubleshooting

### Common Issues
1. **API Key Issues**: Verify your API key and form ID
2. **CORS Errors**: Ensure your API route is properly configured
3. **Email Not Sending**: Check Kit.com deliverability settings
4. **Form Not Working**: Verify environment variables are set

### Debug Steps
1. **Check** browser network tab for API calls
2. **Verify** environment variables in production
3. **Test** API endpoint directly
4. **Monitor** Kit.com dashboard for errors

---

## Best Practices

1. **Always** validate email format on frontend and backend
2. **Use** proper error handling and user feedback
3. **Implement** rate limiting to prevent abuse
4. **Follow** GDPR and email compliance guidelines
5. **Test** thoroughly before going live
6. **Monitor** performance and engagement metrics

---

## Next Steps

1. **Complete** the integration following this guide
2. **Set up** your email sequences and automation
3. **Launch** your waitlist page
4. **Monitor** and optimize based on results
5. **Plan** your launch sequence and beta testing

This integration will give you a professional, scalable email capture system that grows with your business! 