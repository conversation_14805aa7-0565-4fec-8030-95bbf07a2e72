import { cache } from 'react'
import { redirect } from 'next/navigation'
import { auth } from '@/lib/auth'
import { db } from '@/db/drizzle'
import { onboarding } from '@/db/schema/schema'
import { eq } from 'drizzle-orm'

export interface ServerUserContext {
  user: {
    id: string
    name: string | null
    email: string
    image: string | null
  }
  organization: {
    id: string
    name: string
    slug?: string
    businessType: "physical" | "digital" | "hybrid" | null
    planType: string | null
    planStatus: string | null
    subscriptionId: string | null
    subscriptionCurrentPeriodEnd: Date | null
    trialEndsAt: Date | null
    monthlyTransactionCount: number | null
    isTrialing: boolean
    logoUrl: string | null
    email: string | null
    industry: string | null
    website: string | null
    phone: string | null
    taxId: string | null
    currency: string | null
    requireTwoFactor: boolean
  }
  membership: {
    role: string
    status?: string
  }
  isOnboardingCompleted: boolean
}

/**
 * Simplified context fetching using Better Auth's built-in caching
 * - Automatically cached per request
 * - Single database query for session + organization data
 * - Built-in error handling and redirects
 */
export const getServerUserContext = cache(async (): Promise<ServerUserContext> => {
  try {
    // Get session with Better Auth's built-in caching
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers())
    });

    if (!session?.user?.id) {
      redirect('/signin');
    }

    // Get organization data in a single query using Better Auth's organization plugin
    const organization = await auth.api.getFullOrganization({
      headers: await import('next/headers').then(h => h.headers())
    });

    if (!organization) {
      redirect('/onboarding');
    }

    // Get member data (role, status) in the same query
    const member = await auth.api.getActiveMember({
      headers: await import('next/headers').then(h => h.headers())
    });

    // Check onboarding completion
    const onboardingStatus = await db
      .select({ isCompleted: onboarding.isCompleted })
      .from(onboarding)
      .where(eq(onboarding.userId, session.user.id))
      .limit(1);

    // Check for suspended status
    if (organization.planStatus !== 'active' && organization.planStatus !== 'suspended') {
      redirect('/billing/status');
    }

    return {
      user: {
        id: session.user.id,
        name: session.user.name || null,
        email: session.user.email || '',
        image: session.user.image || null,
      },
      organization: {
        id: organization.id,
        name: organization.name,
        slug: organization.slug,
        businessType: (organization.businessType as "physical" | "digital" | "hybrid") || null,
        planType: organization.planType || null,
        planStatus: organization.planStatus || null,
        subscriptionId: organization.subscriptionId || null,
        subscriptionCurrentPeriodEnd: organization.subscriptionCurrentPeriodEnd || null,
        trialEndsAt: organization.trialEndsAt || null,
        monthlyTransactionCount: organization.monthlyTransactionCount || null,
        isTrialing: organization.trialEndsAt ? new Date() < organization.trialEndsAt : false,
        logoUrl: organization.logo || null,
        email: organization.email || null,
        industry: organization.industry || null,
        website: organization.website || null,
        phone: organization.phone || null,
        taxId: organization.taxId || null,
        currency: organization.currency || null,
        requireTwoFactor: organization.requireTwoFactor || false,
      },
      membership: {
        role: member?.role || 'member',
        status: 'active', // Remove status since it doesn't exist
      },
      isOnboardingCompleted: onboardingStatus[0]?.isCompleted || false,
    };
  } catch (error) {
    console.error('Error fetching user context:', error);
    redirect('/signin');
  }
});

export const requireAuth = cache(async (): Promise<string> => {
  const session = await auth.api.getSession({
    headers: await import('next/headers').then(h => h.headers())
  });

  if (!session?.user?.id) {
    redirect('/signin');
  }

  return session.user.id;
});