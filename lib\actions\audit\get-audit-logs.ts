"use server";

import { db } from "@/db/drizzle";
import { auditLogs } from "@/db/schema/schema";
import { and, desc, eq, gte, lte } from "drizzle-orm";
import { getAuditContext } from "./context";
import { AuditAction, ResourceType, RiskLevel } from "./types";

/**
 * Retrieve audit logs with filtering and pagination
 */
export async function getAuditLogs(options: {
  organizationId?: string;
  userId?: string;
  resourceType?: ResourceType;
  action?: AuditAction;
  riskLevel?: RiskLevel;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}) {
  try {
    const { organizationId: contextOrgId } = await getAuditContext();
    const targetOrgId = options.organizationId || contextOrgId;

    if (!targetOrgId) {
      // Potentially throw an error or return empty array if no org context
      return [];
    }

    const conditions = [eq(auditLogs.organizationId, targetOrgId)];

    if (options.userId) {
      conditions.push(eq(auditLogs.userId, options.userId));
    }
    if (options.resourceType) {
      conditions.push(eq(auditLogs.resourceType, options.resourceType));
    }
    if (options.action) {
      conditions.push(eq(auditLogs.action, options.action));
    }
    if (options.riskLevel) {
      conditions.push(eq(auditLogs.riskLevel, options.riskLevel));
    }
    if (options.startDate) {
      conditions.push(gte(auditLogs.createdAt, options.startDate));
    }
    if (options.endDate) {
      conditions.push(lte(auditLogs.createdAt, options.endDate));
    }

    const query = db
      .select()
      .from(auditLogs)
      .where(and(...conditions))
      .orderBy(desc(auditLogs.createdAt))
      .limit(options.limit || 50)
      .offset(options.offset || 0);

    const logs = await query;
    return logs;
  } catch (error) {
    console.error("Failed to retrieve audit logs:", error);
    return []; // Return empty array on error
  }
} 