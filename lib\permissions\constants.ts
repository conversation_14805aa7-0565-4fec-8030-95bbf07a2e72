// ================================
// SYSTEM PERMISSIONS DEFINITION
// ================================

export const SYSTEM_PERMISSIONS = {
  // Financial Management
  FINANCIAL_READ: { 
    name: 'financial:read', 
    category: 'financial', 
    description: 'View financial data',
    dependsOn: []
  },
  FINANCIAL_WRITE: { 
    name: 'financial:write', 
    category: 'financial', 
    description: 'Create and edit transactions',
    dependsOn: ['financial:read']
  },
  FINANCIAL_DELETE: { 
    name: 'financial:delete', 
    category: 'financial', 
    description: 'Delete financial records',
    dependsOn: ['financial:write']
  },
  FINANCIAL_EXPORT: { 
    name: 'financial:export', 
    category: 'financial', 
    description: 'Export financial data',
    dependsOn: ['financial:read']
  },
  
  // Product Management
  PRODUCTS_READ: {
    name: 'products:read',
    category: 'products',
    description: 'View products and services',
    dependsOn: []
  },
  PRODUCTS_WRITE: {
    name: 'products:write',
    category: 'products',
    description: 'Create and edit products',
    dependsOn: ['products:read']
  },
  PRODUCTS_DELETE: {
    name: 'products:delete',
    category: 'products',
    description: 'Delete products',
    dependsOn: ['products:write']
  },
  
  // Client Management
  CLIENTS_READ: {
    name: 'clients:read',
    category: 'clients',
    description: 'View clients',
    dependsOn: []
  },
  CLIENTS_WRITE: {
    name: 'clients:write',
    category: 'clients',
    description: 'Create and edit clients',
    dependsOn: ['clients:read']
  },
  CLIENTS_DELETE: {
    name: 'clients:delete',
    category: 'clients',
    description: 'Delete clients',
    dependsOn: ['clients:write']
  },

  // Vendor Management
  VENDORS_READ: {
    name: 'vendors:read',
    category: 'vendors',
    description: 'View vendors',
    dependsOn: []
  },
  VENDORS_WRITE: {
    name: 'vendors:write',
    category: 'vendors',
    description: 'Create and edit vendors',
    dependsOn: ['vendors:read']
  },
  VENDORS_DELETE: {
    name: 'vendors:delete',
    category: 'vendors',
    description: 'Delete vendors',
    dependsOn: ['vendors:write']
  },

  // Project Management
  PROJECTS_READ: {
    name: 'projects:read',
    category: 'projects',
    description: 'View projects',
    dependsOn: []
  },
  PROJECTS_WRITE: {
    name: 'projects:write',
    category: 'projects',
    description: 'Create and edit projects',
    dependsOn: ['projects:read']
  },
  PROJECTS_DELETE: {
    name: 'projects:delete',
    category: 'projects',
    description: 'Delete projects',
    dependsOn: ['projects:write']
  },

  // Invoice Management
  INVOICES_READ: { 
    name: 'invoices:read', 
    category: 'invoices', 
    description: 'View invoices',
    dependsOn: ['financial:read']
  },
  INVOICES_WRITE: { 
    name: 'invoices:write', 
    category: 'invoices', 
    description: 'Create and edit invoices',
    dependsOn: ['invoices:read', 'financial:write']
  },
  INVOICES_SEND: { 
    name: 'invoices:send', 
    category: 'invoices', 
    description: 'Send invoices to clients',
    dependsOn: ['invoices:read']
  },
  INVOICES_DELETE: { 
    name: 'invoices:delete', 
    category: 'invoices', 
    description: 'Delete invoices',
    dependsOn: ['invoices:write']
  },
  // Organization Administration
  ORG_SETTINGS_VIEW: { 
    name: 'org:settings:view', 
    category: 'admin', 
    description: 'View organization settings',
    dependsOn: []
  },
  ORG_SETTINGS_EDIT: { 
    name: 'org:settings:edit', 
    category: 'admin', 
    description: 'Manage organization settings',
    dependsOn: ['org:settings:view']
  },
  ORG_BILLING_VIEW: { 
    name: 'org:billing:view', 
    category: 'admin', 
    description: 'View billing information',
    dependsOn: []
  },
  ORG_BILLING_MANAGE: { 
    name: 'org:billing:manage', 
    category: 'admin', 
    description: 'Manage billing and subscriptions',
    dependsOn: ['org:billing:view']
  },
  
  // Reporting
  REPORTS_BASIC: { 
    name: 'reports:basic', 
    category: 'reporting', 
    description: 'View basic reports',
    dependsOn: ['financial:read']
  },
  REPORTS_ADVANCED: { 
    name: 'reports:advanced', 
    category: 'reporting', 
    description: 'View advanced analytics and reports',
    dependsOn: ['reports:basic']
  },
  REPORTS_EXPORT: { 
    name: 'reports:export', 
    category: 'reporting', 
    description: 'Export reports',
    dependsOn: ['reports:basic']
  },
} as const;

// ================================
// ROLE DEFINITIONS
// ================================

export const SYSTEM_ROLE_PERMISSIONS: Record<string, string[]> = {
  owner: Object.values(SYSTEM_PERMISSIONS).map(p => p.name),
  admin: [
    SYSTEM_PERMISSIONS.FINANCIAL_READ.name,
    SYSTEM_PERMISSIONS.FINANCIAL_WRITE.name,
    SYSTEM_PERMISSIONS.FINANCIAL_DELETE.name,
    SYSTEM_PERMISSIONS.FINANCIAL_EXPORT.name,
    SYSTEM_PERMISSIONS.INVOICES_READ.name,
    SYSTEM_PERMISSIONS.INVOICES_WRITE.name,
    SYSTEM_PERMISSIONS.INVOICES_SEND.name,
    SYSTEM_PERMISSIONS.INVOICES_DELETE.name,
    SYSTEM_PERMISSIONS.ORG_SETTINGS_VIEW.name,
    SYSTEM_PERMISSIONS.ORG_SETTINGS_EDIT.name,
    SYSTEM_PERMISSIONS.ORG_BILLING_VIEW.name,
    SYSTEM_PERMISSIONS.REPORTS_BASIC.name,
    SYSTEM_PERMISSIONS.REPORTS_ADVANCED.name,
    SYSTEM_PERMISSIONS.REPORTS_EXPORT.name,
  ],
  manager: [
    SYSTEM_PERMISSIONS.FINANCIAL_READ.name,
    SYSTEM_PERMISSIONS.FINANCIAL_WRITE.name,
    SYSTEM_PERMISSIONS.FINANCIAL_EXPORT.name,
    SYSTEM_PERMISSIONS.INVOICES_READ.name,
    SYSTEM_PERMISSIONS.INVOICES_WRITE.name,
    SYSTEM_PERMISSIONS.INVOICES_SEND.name,
    SYSTEM_PERMISSIONS.REPORTS_BASIC.name,
    SYSTEM_PERMISSIONS.REPORTS_ADVANCED.name,
    SYSTEM_PERMISSIONS.REPORTS_EXPORT.name,
  ],
  member: [
    SYSTEM_PERMISSIONS.FINANCIAL_READ.name,
    SYSTEM_PERMISSIONS.FINANCIAL_WRITE.name,
    SYSTEM_PERMISSIONS.FINANCIAL_DELETE.name,
    SYSTEM_PERMISSIONS.FINANCIAL_EXPORT.name,
    SYSTEM_PERMISSIONS.INVOICES_READ.name,
    SYSTEM_PERMISSIONS.INVOICES_WRITE.name,
    SYSTEM_PERMISSIONS.INVOICES_SEND.name,
    SYSTEM_PERMISSIONS.INVOICES_DELETE.name,
    SYSTEM_PERMISSIONS.ORG_SETTINGS_VIEW.name,
    SYSTEM_PERMISSIONS.ORG_SETTINGS_EDIT.name,
    SYSTEM_PERMISSIONS.ORG_BILLING_VIEW.name,
    SYSTEM_PERMISSIONS.REPORTS_BASIC.name,
    SYSTEM_PERMISSIONS.REPORTS_ADVANCED.name,
    SYSTEM_PERMISSIONS.REPORTS_EXPORT.name,
  ],
  auditor: [
    SYSTEM_PERMISSIONS.FINANCIAL_READ.name,
    SYSTEM_PERMISSIONS.FINANCIAL_EXPORT.name,
    SYSTEM_PERMISSIONS.INVOICES_READ.name,
    SYSTEM_PERMISSIONS.REPORTS_BASIC.name,
    SYSTEM_PERMISSIONS.REPORTS_ADVANCED.name,
    SYSTEM_PERMISSIONS.REPORTS_EXPORT.name,
  ],
};

export const PERMISSION_CATEGORIES = [
  { id: 'financial', name: 'Financial' },
  { id: 'products', name: 'Products' },
  { id: 'clients', name: 'Clients' },
  { id: 'vendors', name: 'Vendors' },
  { id: 'projects', name: 'Projects' },
  { id: 'invoices', name: 'Invoices' },
  { id: 'admin', name: 'Organization Admin' },
  { id: 'reporting', name: 'Reporting' },
];

export type MemberRole = 'owner' | 'admin' | 'member' | 'auditor'; 