"use client";

import * as React from "react";
import { CountrySelector } from "./CountrySelector";
import { Input } from "@/components/ui/input";
import { countries, Country } from "@/lib/country-utils";
import { cn } from "@/lib/utils";

interface PhoneNumberInputProps {
  value: string; // Full phone number including country code, e.g., "+15551234567"
  onChange: (value: string) => void;
  className?: string;
  autoFocus?: boolean; // Whether to auto-focus the country selector search input when opened
  id?: string;
}

export function PhoneNumberInput({ value, onChange, className, autoFocus = true, id }: PhoneNumberInputProps) {
  const [selectedCountry, setSelectedCountry] = React.useState<Country | null>(null);
  const [numberPart, setNumberPart] = React.useState("");
  
  // Memoize the parsing function to avoid recreating it on every render
  const parseValue = React.useCallback((inputValue: string) => {
    let matchedCountry: Country | null = null;
    let number = inputValue || "";

    if (inputValue) {
      // Find the country with the longest matching phone code
      for (const country of countries) {
        if (inputValue.startsWith(`+${country.phone}`)) {
          if (!matchedCountry || country.phone.length > matchedCountry.phone.length) {
            matchedCountry = country;
          }
        }
      }
    }

    if (matchedCountry) {
      number = inputValue.substring(1 + matchedCountry.phone.length);
      return { country: matchedCountry, number };
    }
    
    return { country: null, number };
  }, []);

  // Effect to parse the initial value
  React.useEffect(() => {
    const { country, number } = parseValue(value);
    
    // Only update state if there's an actual change
    if (country?.code !== selectedCountry?.code) {
      setSelectedCountry(country);
    }
    
    if (number !== numberPart) {
      setNumberPart(number);
    }
  }, [value, parseValue, selectedCountry?.code, numberPart]);

  const handleCountryChange = (countryCode: string) => {
    const country = countries.find((c: Country) => c.code === countryCode);
    if (country) {
      setSelectedCountry(country);
      // Update the full phone number immediately
      onChange(`+${country.phone}${numberPart}`);
    }
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newNumberPart = e.target.value.replace(/\D/g, ""); // Allow only digits
    setNumberPart(newNumberPart);
    
    // Always update the parent component with the full value
    if (selectedCountry) {
      onChange(`+${selectedCountry.phone}${newNumberPart}`);
    } else {
      onChange(newNumberPart);
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <CountrySelector
        value={selectedCountry?.code || ""}
        onChange={handleCountryChange}
        className="w-full"
        autoFocus={autoFocus}
      />
      <div className="relative">
        {selectedCountry && (
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground z-10">
            +{selectedCountry.phone}
          </span>
        )}
        <Input
          id={id}
          type="tel"
          value={numberPart}
          onChange={handleNumberChange}
          className={cn("h-10", selectedCountry ? "pl-16" : "pl-3")}
          placeholder="************"
        />
      </div>
    </div>
  );
} 