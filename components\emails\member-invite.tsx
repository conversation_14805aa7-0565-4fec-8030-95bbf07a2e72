import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Tailwind,
  Font,
} from '@react-email/components';

// Adapted props for invite email
export interface OrganizationInvitationEmailProps {
  orgName: string;
  inviterName: string;
  inviterEmail: string;
  role: string;
  inviteUrl: string;
}

const OrganizationInvitationEmail = (props: OrganizationInvitationEmailProps) => {
  const { orgName, inviterName, inviterEmail, role, inviteUrl } = props;

  return (
    <Html lang="en" dir="ltr">
      <Head>
        <Font
          fontFamily="Poppins"
          fallbackFontFamily="Arial"
          webFont={{
            url: "https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap",
            format: "woff2",
          }}
        />
      </Head>
      <Tailwind>
        <Preview>You've been invited to join {orgName} organization.</Preview>
        <Body className="bg-[#f1f1f1] py-[40px] font-sans">
          <Container className="bg-white mx-auto px-[40px] py-[40px] rounded-[12px] max-w-[600px]">
            {/* Header */}
            <Section className="text-center mb-[32px]">
              <div className="w-[60px] h-[60px] bg-[#bc00fe] rounded-[12px] mx-auto mb-[24px] flex items-center justify-center">
                <div className="text-white text-[24px] font-bold">
                  {orgName?.charAt(0) || 'T'}
                </div>
              </div>
              <Heading className="text-[#222121] text-[28px] font-bold m-0 leading-[1.2]">
                You're invited to join
              </Heading>
              <Text className="text-[#bc00fe] text-[32px] font-bold m-0 mt-[8px] leading-[1.2]">
                {orgName}
              </Text>
            </Section>

            {/* Main Content */}
            <Section className="mb-[32px]">
              <Text className="text-[#222121] text-[16px] leading-[1.6] mb-[16px] m-0">
                Hello there! 👋
              </Text>
              <Text className="text-[#222121] text-[16px] leading-[1.6] mb-[16px] m-0">
                <strong>{inviterName}</strong> ({inviterEmail}) has invited you to join the <strong>{orgName}</strong> {role ? ` as a ${role}` : ''}.
              </Text>
              <Text className="text-[#222121] text-[16px] leading-[1.6] mb-[24px] m-0">
                Join us to collaborate, share ideas, and build amazing things together. We're excited to have you on board!
              </Text>
            </Section>

            {/* CTA Button */}
            <Section className="text-center mb-[32px]">
              <Button
                className="bg-[#bc00fe] text-white px-[32px] py-[16px] rounded-[8px] text-[16px] font-semibold no-underline box-border hover:bg-[#9900cc] transition-colors"
                href={inviteUrl}
              >
                Join {orgName} Organization
              </Button>
            </Section>

            {/* Additional Info */}
            <Section className="bg-[#f1f1f1] p-[24px] rounded-[8px] mb-[32px]">
              <Text className="text-[#222121] text-[14px] leading-[1.5] m-0 mb-[12px]">
                <strong>What happens next?</strong>
              </Text>
              <Text className="text-[#222121] text-[14px] leading-[1.5] m-0 mb-[8px]">
                • Click the "Join Organization" button above
              </Text>
              <Text className="text-[#222121] text-[14px] leading-[1.5] m-0 mb-[8px]">
                • Complete your account setup
              </Text>
              <Text className="text-[#222121] text-[14px] leading-[1.5] m-0">
                • Start collaborating with your organization
              </Text>
            </Section>

            {/* Footer */}
            <Section className="border-t border-solid border-[#f1f1f1] pt-[24px]">
              <Text className="text-[#666666] text-[12px] leading-[1.4] m-0 text-center">
                This invitation was sent by {inviterName} from {orgName}.
              </Text>
              <Text className="text-[#666666] text-[12px] leading-[1.4] m-0 text-center mt-[8px]">
                If you didn't expect this invitation, you can safely ignore this email.
              </Text>
              <Text className="text-[#666666] text-[12px] leading-[1.4] m-0 text-center mt-[16px]">
                © {new Date().getFullYear()} {orgName}. All rights reserved.
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default OrganizationInvitationEmail;